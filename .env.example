# Application Configuration
APP_NAME="Enterprise Logistics Tracking Application"
APP_ENV=development # development, production
APP_DEBUG=true # true or false
APP_KEY= # Generate a secure key for production
BASE_URL=http://localhost/courier/public # Adjust to your local setup

# Database Configuration
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=elta_db
DB_USERNAME=root
DB_PASSWORD=

# Mail Configuration (Example using Mailtrap for development)
MAIL_MAILER=smtp
MAIL_HOST=smtp.mailtrap.io
MAIL_PORT=2525
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"

# Admin User (For initial seeding, if applicable)
ADMIN_EMAIL=<EMAIL>
ADMIN_PASSWORD=password # Change this!

# Other Settings
SESSION_DRIVER=file
SESSION_LIFETIME=120 # Minutes
LOG_CHANNEL=stack
LOG_LEVEL=debug
