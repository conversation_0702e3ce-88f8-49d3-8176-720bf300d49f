<?php
try {
    $pdo = new PDO('mysql:host=127.0.0.1;dbname=shipment', 'root', 'root');
    
    echo "=== DATABASE STRUCTURE ANALYSIS ===\n\n";
    
    // Get all tables
    $stmt = $pdo->query('SHOW TABLES');
    $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    echo "TABLES IN DATABASE:\n";
    foreach($tables as $table) {
        echo "- $table\n";
    }
    
    echo "\n=== DETAILED TABLE STRUCTURES ===\n\n";
    
    // Analyze each table structure
    foreach($tables as $table) {
        echo "TABLE: $table\n";
        echo str_repeat("-", 50) . "\n";
        
        $stmt = $pdo->query("DESCRIBE $table");
        $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        foreach($columns as $column) {
            $null = $column['Null'] == 'YES' ? 'NULL' : 'NOT NULL';
            $key = $column['Key'] ? " [{$column['Key']}]" : '';
            $default = $column['Default'] ? " DEFAULT: {$column['Default']}" : '';
            $extra = $column['Extra'] ? " {$column['Extra']}" : '';
            
            echo sprintf("  %-20s %-15s %s%s%s%s\n", 
                $column['Field'], 
                $column['Type'], 
                $null, 
                $key, 
                $default, 
                $extra
            );
        }
        
        // Show sample data for key tables
        if (in_array($table, ['shipments', 'shipment_history', 'users', 'clients'])) {
            echo "\nSample data (first 3 rows):\n";
            $stmt = $pdo->query("SELECT * FROM $table LIMIT 3");
            $sample = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            if (!empty($sample)) {
                $headers = array_keys($sample[0]);
                echo "  " . implode(" | ", array_map(function($h) { return substr($h, 0, 12); }, $headers)) . "\n";
                echo "  " . str_repeat("-", count($headers) * 15) . "\n";
                
                foreach($sample as $row) {
                    $values = array_map(function($v) { 
                        return substr(str_replace(["\n", "\r"], ' ', $v ?? 'NULL'), 0, 12); 
                    }, array_values($row));
                    echo "  " . implode(" | ", $values) . "\n";
                }
            } else {
                echo "  (No data)\n";
            }
        }
        
        echo "\n";
    }
    
    // Check for foreign key relationships
    echo "=== FOREIGN KEY RELATIONSHIPS ===\n\n";
    $stmt = $pdo->query("
        SELECT 
            TABLE_NAME,
            COLUMN_NAME,
            CONSTRAINT_NAME,
            REFERENCED_TABLE_NAME,
            REFERENCED_COLUMN_NAME
        FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE 
        WHERE REFERENCED_TABLE_SCHEMA = 'shipment' 
        AND REFERENCED_TABLE_NAME IS NOT NULL
    ");
    
    $fks = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (!empty($fks)) {
        foreach($fks as $fk) {
            echo "{$fk['TABLE_NAME']}.{$fk['COLUMN_NAME']} -> {$fk['REFERENCED_TABLE_NAME']}.{$fk['REFERENCED_COLUMN_NAME']}\n";
        }
    } else {
        echo "No foreign key relationships found.\n";
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
?>
