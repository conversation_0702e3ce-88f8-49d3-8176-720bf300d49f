<?php

use Dotenv\Dotenv;

// Load environment variables
$dotenv = Dotenv::createImmutable(__DIR__ . '/../'); // Go up one level from config/ to the root
$dotenv->safeLoad(); // Use safeLoad to avoid errors if .env doesn't exist

return [
    'name' => $_ENV['APP_NAME'] ?? 'ELTA',
    'env' => $_ENV['APP_ENV'] ?? 'production',
    'debug' => filter_var($_ENV['APP_DEBUG'] ?? false, FILTER_VALIDATE_BOOLEAN),
    'url' => $_ENV['APP_URL'] ?? 'http://localhost',

    /*
    |--------------------------------------------------------------------------
    | Application Timezone
    |--------------------------------------------------------------------------
    |
    | Here you may specify the default timezone for your application, which
    | will be used by the PHP date and date-time functions. We have gone
    | ahead and set this to a sensible default for you out of the box.
    |
    */
    'timezone' => 'UTC',

    /*
    |--------------------------------------------------------------------------
    | Application Locale Configuration
    |--------------------------------------------------------------------------
    |
    | The application locale determines the default locale that will be used
    | by the translation service provider. You are free to set this value
    | to any of the locales which will be supported by the application.
    |
    */
    'locale' => 'en',
    'fallback_locale' => 'en',

    /*
    |--------------------------------------------------------------------------
    | Encryption Key
    |--------------------------------------------------------------------------
    |
    | This key is used by the encryption services and should be set
    | to a random, 32 character string, otherwise these encrypted strings
    | will not be safe. Please do this before deploying an application!
    | You should generate this using a secure random byte generator.
    |
    */
    'key' => $_ENV['APP_KEY'] ?? '', // Ensure this is set in .env for production

    /*
    |--------------------------------------------------------------------------
    | Logging Configuration
    |--------------------------------------------------------------------------
    |
    | Here you may configure the log channels for your application. Out of
    | the box, Laravel uses the Monolog PHP logging library. This gives
    | you a variety of powerful log handlers / formatters to utilize.
    |
    | Available Drivers: "single", "daily", "slack", "syslog",
    |                    "errorlog", "monolog",
    |                    "custom", "stack"
    |
    */
    'logging' => [
        'default' => $_ENV['LOG_CHANNEL'] ?? 'stack',
        'channels' => [
            'stack' => [
                'driver' => 'stack',
                'channels' => ['single'],
                'ignore_exceptions' => false,
            ],
            'single' => [
                'driver' => 'single',
                'path' => __DIR__.'/../debug/app.log', // Path relative to config dir
                'level' => $_ENV['LOG_LEVEL'] ?? 'debug',
            ],
            'daily' => [
                'driver' => 'daily',
                'path' => __DIR__.'/../debug/app.log',
                'level' => $_ENV['LOG_LEVEL'] ?? 'debug',
                'days' => 14,
            ],
            // Add other channels like syslog, errorlog etc. if needed
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Error Reporting Level
    |--------------------------------------------------------------------------
    |
    | Define the level of error reporting based on the environment.
    | Display errors in development, log them in production.
    |
    */
    'error_reporting' => (filter_var($_ENV['APP_DEBUG'] ?? false, FILTER_VALIDATE_BOOLEAN))
        ? E_ALL
        : 0, // Report all errors in debug, none directly in production (rely on logging)

    'display_errors' => (filter_var($_ENV['APP_DEBUG'] ?? false, FILTER_VALIDATE_BOOLEAN))
        ? '1'
        : '0', // Display errors only in debug mode

];
