<?php

// Define base path (assuming this file is in config/ which is one level below root)
define('BASE_PATH', dirname(__DIR__));

// Define base URL (read from .env or use a default)
// Ensure BASE_URL in .env does NOT have a trailing slash
$baseUrl = rtrim($_ENV['BASE_URL'] ?? 'http://localhost', '/');
define('BASE_URL', $baseUrl);

// Define other common paths relative to BASE_PATH
define('PUBLIC_PATH', BASE_PATH . '/public');
define('CONFIG_PATH', BASE_PATH . '/config');
define('SRC_PATH', BASE_PATH . '/src');
define('VIEWS_PATH', SRC_PATH . '/Views');
define('STORAGE_PATH', BASE_PATH . '/storage'); // Example, if needed later
define('DEBUG_PATH', BASE_PATH . '/debug');
define('VENDOR_PATH', BASE_PATH . '/vendor');

// You can add more paths as needed, e.g., for uploads, logs, etc.
