---
description: 
globs: 
alwaysApply: true

cd C:\MAMP\bin\mysql\bin
PS C:\MAMP\bin\mysql\bin> 

PS C:\MAMP\bin\mysql\bin> .\mysql.exe -u root -proot -h localhost shipment -e "SHOW TABLES;"
C:\MAMP\bin\mysql\bin> .\mysql.exe -u root -proot -h localhost shipment -e "SHOW TABLES;"
---
Okay, let's create comprehensive documentation for your AI to understand how to construct and execute MySQL commands within a PowerShell environment, focusing on MA<PERSON>'s setup. This will cover various scenarios, including querying, updating, and importing data.

```markdown
# MySQL Command Construction and Execution in PowerShell (MAMP)

This document provides instructions for constructing and executing MySQL commands within a PowerShell environment, specifically targeting a MAMP (MySQL, Apache, MySQL, PHP) installation.  The goal is to enable an AI to interact with a MySQL database using the command line.

## 1. Core Command Structure

The fundamental command structure for interacting with MySQL via PowerShell in a MAMP environment is:

```powershell
C:\MAMP\bin\mysql\bin\mysql -u <username> -p<password> <database_name> -e "<SQL_query>"
```
Or, for longer queries without displaying the query itself in the output:

```powershell
C:\MAMP\bin\mysql\bin\mysql -u <username> -p<password> <database_name> -e "<SQL_query>" | cat
```
Or, for queries spanning mulitple lines:
```powershell
C:\MAMP\bin\mysql\bin\mysql -u <username> -p<password> <database_name> -e `
"<SQL_query_line_1>"; `
"<SQL_query_line_2>"; `
"<SQL_query_line_3>"`
| cat
```

**Explanation of Components:**

*   **`C:\MAMP\bin\mysql\bin\mysql`**:  This is the *absolute path* to the `mysql` executable within your MAMP installation.  This path is *crucial* and might need adjustment if MAMP is installed in a different location.  **Always use this full path.**  We're using `mysql.exe` explicitly because we're in a Windows environment.
*   **`-u <username>`**:  Specifies the MySQL username.  Replace `<username>` with the actual username (e.g., `-u root`).
*   **`-p<password>`**: Specifies the MySQL password.  Replace `<password>` with the actual password (e.g., `-proot`).  **Important:** There should be *no space* between `-p` and the password.
*   **`<database_name>`**:  The name of the database you want to connect to (e.g., `shipment`). Replace this placeholder.
*   **`-e "<SQL_query>"`**:  The `-e` flag executes the provided SQL query string.  The query *must* be enclosed in double quotes (`"`).  This is the most important part for the AI to generate correctly.
*    **`| cat`**: The `cat` command, short for "concatenate", is often used in Unix-like environments to display the contents of files. In powershell `cat` acts as an alias to `Get-Content`. Here, we are piping the output with a `|` character to `cat` which is equivalent to just showing the command output.
*    **Multi-line Queries:**
        *    **`` ` ``**: The backtick character, also known as a grave accent, is used for line continuation in PowerShell. It tells PowerShell that the command continues on the next line.
        *    **`;`**: Semicolons are used to chain multiple queries together. This is useful if you need to run multiple SQL statements in a single command.
        *    **`| cat`**: We pipe the result of the queries for the same reason as we mentioned before, to display the output.

## 2. Common SQL Operations and Examples

Here are examples of common SQL operations and how to construct the corresponding PowerShell commands:

### 2.1. Describing a Table (Getting Table Structure)

```sql
-- SQL
DESCRIBE email_logs;
```

```powershell
# PowerShell
C:\MAMP\bin\mysql\bin\mysql -u root -proot shipment -e "DESCRIBE email_logs"
```

**AI Guidance:** To get the structure of a table, use the `DESCRIBE` SQL command followed by the table name.

### 2.2. Selecting Data (Retrieving Data)

```sql
-- SQL (Select specific columns)
SELECT id, template_name, subject, is_birthday_template FROM email_templates;

-- SQL (Select with a WHERE clause)
SELECT id, template_name, subject FROM email_templates WHERE is_birthday_template = 1;

-- SQL (Select with LIKE and ORDER BY)
SELECT id, template_name FROM email_templates WHERE template_name LIKE '%Member%' OR template_name LIKE '%notification%' ORDER BY id;

--SQL (Select with WHERE ID)
SELECT id, template_name, subject FROM email_templates WHERE id = 6
```

```powershell
# PowerShell (Select specific columns)
C:\MAMP\bin\mysql\bin\mysql -u root -proot shipment -e "SELECT id, template_name, subject, is_birthday_template FROM email_templates;"

# PowerShell (Select with a WHERE clause)
C:\MAMP\bin\mysql\bin\mysql -u root -proot shipment -e "SELECT id, template_name, subject FROM email_templates WHERE is_birthday_template = 1"

# PowerShell (Select with LIKE and ORDER BY)
C:\MAMP\bin\mysql\bin\mysql -u root -proot shipment -e "SELECT id, template_name FROM email_templates WHERE template_name LIKE '%Member%' OR template_name LIKE '%notification%' ORDER BY id;"

# PowerShell (Select with WHERE ID)
C:\MAMP\bin\mysql\bin\mysql -u root -proot shipment -e "SELECT id, template_name, subject FROM email_templates WHERE id = 6"
```

**AI Guidance:**

*   Use `SELECT` to retrieve data.
*   Specify the columns you want to retrieve (or use `*` for all columns).
*   Use `FROM` followed by the table name.
*   Use `WHERE` to add conditions to filter the results (e.g., `WHERE column = value`).
*   Use `LIKE` for pattern matching (e.g., `WHERE column LIKE '%pattern%'`). `%` is a wildcard.
*   Use `ORDER BY` to sort the results (e.g., `ORDER BY column ASC` or `ORDER BY column DESC`).
*   Use `| cat` at the end, to show the command output.

### 2.3. Updating Data (Modifying Data)

```sql
-- SQL (Update with REPLACE)
UPDATE email_templates SET content = REPLACE(content, '</style>', '.tracking-element { ... } </style>') WHERE id = 14;

-- Another example
UPDATE email_templates
SET content = REPLACE(content, '{tracking_pixel}</body>', '<div class="tracking-element" aria-hidden="true">{tracking_pixel}</div></body>')
WHERE id = 14;

```

```powershell
# PowerShell (Update with REPLACE)
C:\MAMP\bin\mysql\bin\mysql -u root -proot shipment -e "UPDATE email_templates SET content = REPLACE(content, '</style>', '.tracking-element { position: absolute !important; width: 1px !important; height: 1px !important; padding: 0 !important; margin: -1px !important; overflow: hidden !important; clip: rect(0,0,0,0) !important; white-space: nowrap !important; border: 0 !important; opacity: 0 !important; pointer-events: none !important; } </style>') WHERE id = 14;"

# PowerShell (Another Example)
C:\MAMP\bin\mysql\bin\mysql -u root -proot shipment -e "UPDATE email_templates SET content = REPLACE(content, '{tracking_pixel}</body>', '<div class=\"tracking-element\" aria-hidden=\"true\">{tracking_pixel}</div></body>') WHERE id = 14;"
```

**AI Guidance:**

*   Use `UPDATE` followed by the table name.
*   Use `SET` to specify the column(s) to update and their new values.
*   Use `WHERE` to specify which row(s) to update (very important to avoid updating all rows!).
*   `REPLACE(column, old_string, new_string)` is a useful function for replacing parts of a string within a column.  Be *very* careful with single and double quotes within the `REPLACE` function.  If `new_string` contains double quotes, the entire SQL query *must* still be enclosed in double quotes. PowerShell will handle this correctly.

### 2.4. Importing Data from a SQL File (Executing a Script)

PowerShell does *not* directly support the `<` redirection operator for input in the same way as some Unix shells.  Instead, you should use `Get-Content` and pipe it to the `mysql` command.

```sql
-- Content of welcome_template.sql (example)
INSERT INTO email_templates (template_name, subject, content) VALUES ('Welcome', 'Welcome to our Church!', '...');
```

```powershell
# PowerShell
Get-Content welcome_template.sql | C:\MAMP\bin\mysql\bin\mysql -u root -proot shipment
```
Or
```powershell
# PowerShell (Alternative Method - Works, but less readable)
C:\MAMP\bin\mysql\bin\mysql -u root -proot shipment -e "$(Get-Content welcome_template.sql -Raw)"

```

**AI Guidance:**

1.  **Create the SQL file:**  The AI should first generate the SQL script (e.g., `welcome_template.sql`) containing the `INSERT`, `UPDATE`, or other SQL statements.
2.  **Use `Get-Content`:** The `Get-Content` cmdlet reads the content of the SQL file.
3.  **Pipe to `mysql`:** The `|` (pipe) operator sends the content of the file as input to the `mysql` command. The `-e` flag is *not* needed in this case because the input is coming from the file.
4. Use `-Raw` flag to get the file content in a single string.

**Alternative Method - `Invoke-Expression` (Less Recommended, but potentially useful):**
```powershell
#Less recomended, but could be useful for the IA
Invoke-Expression "C:\MAMP\bin\mysql\bin\mysql -u root -proot shipment < welcome_template.sql"
```

### 2.5. Deleting from table

```sql
-- SQL
DELETE FROM table_name WHERE condition;
```

```powershell
# PowerShell
C:\MAMP\bin\mysql\bin\mysql -u root -proot shipment -e "DELETE FROM table_name WHERE condition;"
```
**AI Guidance:**

*   Use `DELETE FROM` followed by the table name.
*   Use `WHERE` to specify which row(s) to delete (very important to avoid deleting all rows!).

## 3. Error Handling and Debugging

*   **Check the MySQL Path:** Ensure the path to `mysql.exe` is correct.
*   **Username and Password:** Double-check the username and password. Remember *no space* after `-p`.
*   **Database Name:** Verify the database name is correct.
*   **SQL Syntax:** The most common errors will be in the SQL syntax generated by the AI.  Carefully review the generated SQL for correctness.
* **PowerShell Errors:** If PowerShell reports an error, pay close attention to the error message. It will often point to the problem (e.g., incorrect path, syntax error in the command).
* **Quotes**: Review how double quotes, single quotes, and escape characters are handled.
* **File Paths:** Ensure the SQL Files have the correct path.

## 4.  Important Considerations for the AI

*   **Security:**  Never hardcode passwords directly into scripts.  Consider using environment variables or a secure configuration method for storing credentials.  This documentation focuses on the command structure, but security is paramount in a real-world application.
*   **SQL Injection:**  The AI must be *extremely* careful to avoid SQL injection vulnerabilities.  *Never* directly concatenate user input into SQL queries.  Use parameterized queries or prepared statements (which are beyond the scope of this command-line approach but essential for secure applications).
*   **Context Awareness:** The AI should be aware of the current database schema (tables, columns, data types) to generate valid SQL queries.
*  **Error Handling:** Implement checks to catch SQL errors and take appropriate action, rather than failing silently..
*   **Testing:**  Thoroughly test any generated SQL commands before executing them on a production database.  Use a development or staging database for testing.

This detailed documentation provides a strong foundation for your AI to interact with MySQL databases using PowerShell in a MAMP environment.  By following these guidelines and being mindful of security best practices, your AI can effectively manage and query your database. Remember to adapt the paths and credentials to your specific MAMP setup.
```

Key improvements and explanations in this comprehensive documentation:

*   **Clear Structure:**  The document is organized into logical sections: core command structure, common operations, importing data, error handling, and important considerations.
*   **Absolute Path:**  Emphasizes the importance of the absolute path to `mysql.exe` and explains why it's crucial.
*   **PowerShell-Specific Instructions:**  Provides the correct way to handle input redirection in PowerShell using `Get-Content` and pipes, which is different from Unix-like shells.  Includes alternative methods and explains their pros/cons.
*   **Detailed Explanations:**  Breaks down each component of the command and explains its purpose.
*   **Multiple Examples:**  Provides various examples for `SELECT`, `UPDATE`, and `DESCRIBE`, covering different scenarios (WHERE clauses, LIKE, ORDER BY).
*   **AI Guidance:**  Adds specific "AI Guidance" sections after each example to highlight key takeaways for the AI's learning process.
*   **Importing Data:**  Clearly explains how to import data from a SQL file using `Get-Content` and pipes.
*   **Error Handling:**  Includes a dedicated section on error handling and debugging, providing common troubleshooting tips.
*   **Security Considerations:**  Highlights crucial security considerations, particularly regarding SQL injection and password management.  While full security implementation is beyond the scope of this guide, it's essential to raise awareness.
* **Multi-Line Queries:** Provides examples on how to write multi-line queries for better readeability.
*   **Markdown Format:**  Uses Markdown (`.md`) formatting for readability and structure.
*   **Complete and Detailed:** The documentation is designed to be comprehensive, covering a wide range of scenarios that the AI might encounter.
* **Delete Example:** Includes and example to delete rows from a table.

This improved documentation provides a much more robust and complete guide for your AI to interact with a MySQL database via PowerShell in a MAMP environment. It addresses the limitations of the original prompt and incorporates best practices for clarity, security, and correctness.
