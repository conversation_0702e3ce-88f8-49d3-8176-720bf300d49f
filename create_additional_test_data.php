<?php
try {
    $pdo = new PDO('mysql:host=127.0.0.1;dbname=shipment', 'root', 'root');
    
    // Get shipment ID for TSD250422999001
    $stmt = $pdo->prepare('SELECT id FROM shipments WHERE tracking_number = ?');
    $stmt->execute(['TSD250422999001']);
    $shipment = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$shipment) {
        echo "Shipment not found!\n";
        exit;
    }
    
    $shipment_id = $shipment['id'];
    
    // Get admin user ID
    $stmt = $pdo->query('SELECT id FROM users LIMIT 1');
    $admin = $stmt->fetch(PDO::FETCH_ASSOC);
    $admin_id = $admin['id'];
    
    // Get document type ID for "Commercial Invoice"
    $stmt = $pdo->prepare('SELECT id FROM document_types WHERE name = ?');
    $stmt->execute(['Commercial Invoice']);
    $docType = $stmt->fetch(PDO::FETCH_ASSOC);
    $doc_type_id = $docType['id'];
    
    // Create Commercial Invoice request
    $stmt = $pdo->prepare('
        INSERT INTO document_requests 
        (shipment_id, document_type_id, requested_by, request_message, priority, due_date, status) 
        VALUES (?, ?, ?, ?, ?, ?, ?)
    ');
    
    $stmt->execute([
        $shipment_id,
        $doc_type_id,
        $admin_id,
        'Please provide a commercial invoice for this international shipment. This document is required for customs processing and must include itemized details of the goods being shipped.',
        'high',
        date('Y-m-d H:i:s', strtotime('+5 days')), // Due in 5 days
        'pending'
    ]);
    
    $request_id = $pdo->lastInsertId();
    
    echo "✓ Created Commercial Invoice request with ID: $request_id\n";
    
    // Create a notification for this request
    $stmt = $pdo->prepare('
        INSERT INTO notifications 
        (shipment_id, document_request_id, type, title, message, recipient_email) 
        VALUES (?, ?, ?, ?, ?, ?)
    ');
    
    $stmt->execute([
        $shipment_id,
        $request_id,
        'document_requested',
        'Commercial Invoice Required for Shipment TSD250422999001',
        'A Commercial Invoice is required for your international shipment. Please upload the document as soon as possible.',
        '<EMAIL>'
    ]);
    
    echo "✓ Created notification for Commercial Invoice request\n";
    
    // Create another notification for testing
    $stmt = $pdo->prepare('
        INSERT INTO notifications 
        (shipment_id, document_request_id, type, title, message, recipient_email, is_read) 
        VALUES (?, ?, ?, ?, ?, ?, ?)
    ');
    
    $stmt->execute([
        $shipment_id,
        null,
        'document_approved',
        'Document Approved for Shipment TSD250422999001',
        'Your previously submitted document has been approved and your shipment will proceed.',
        '<EMAIL>',
        0 // Unread
    ]);
    
    echo "✓ Created additional notification for testing\n";
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
?>
