-- Document Management System Database Schema
-- This creates tables for document requests, uploads, approvals, and notifications

-- 1. Document Types Table (predefined and custom document types)
CREATE TABLE document_types (
    id INT(11) NOT NULL AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL,
    description TEXT NULL,
    is_predefined TINYINT(1) NOT NULL DEFAULT 0,
    is_active TINYINT(1) NOT NULL DEFAULT 1,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (id),
    UNIQUE KEY unique_name (name)
);

-- 2. Document Requests Table (admin requests documents from users)
CREATE TABLE document_requests (
    id INT(11) NOT NULL AUTO_INCREMENT,
    shipment_id INT(11) NOT NULL,
    document_type_id INT(11) NOT NULL,
    requested_by INT(11) NOT NULL, -- admin user ID
    request_message TEXT NULL,
    priority ENUM('low', 'medium', 'high', 'urgent') NOT NULL DEFAULT 'medium',
    due_date DATETIME NULL,
    status ENUM('pending', 'uploaded', 'approved', 'rejected', 'cancelled') NOT NULL DEFAULT 'pending',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (id),
    FOREIGN KEY (shipment_id) REFERENCES shipments(id) ON DELETE CASCADE,
    FOREIGN KEY (document_type_id) REFERENCES document_types(id) ON DELETE RESTRICT,
    FOREIGN KEY (requested_by) REFERENCES users(id) ON DELETE RESTRICT,
    INDEX idx_shipment_status (shipment_id, status),
    INDEX idx_status_priority (status, priority)
);

-- 3. Document Uploads Table (user uploads requested documents)
CREATE TABLE document_uploads (
    id INT(11) NOT NULL AUTO_INCREMENT,
    document_request_id INT(11) NOT NULL,
    original_filename VARCHAR(255) NOT NULL,
    stored_filename VARCHAR(255) NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    file_size INT(11) NOT NULL,
    mime_type VARCHAR(100) NOT NULL,
    upload_notes TEXT NULL,
    uploaded_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (id),
    FOREIGN KEY (document_request_id) REFERENCES document_requests(id) ON DELETE CASCADE,
    INDEX idx_request_id (document_request_id)
);

-- 4. Document Approvals Table (admin reviews and approves/rejects documents)
CREATE TABLE document_approvals (
    id INT(11) NOT NULL AUTO_INCREMENT,
    document_request_id INT(11) NOT NULL,
    document_upload_id INT(11) NOT NULL,
    reviewed_by INT(11) NOT NULL, -- admin user ID
    status ENUM('approved', 'rejected') NOT NULL,
    review_notes TEXT NULL,
    reviewed_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (id),
    FOREIGN KEY (document_request_id) REFERENCES document_requests(id) ON DELETE CASCADE,
    FOREIGN KEY (document_upload_id) REFERENCES document_uploads(id) ON DELETE CASCADE,
    FOREIGN KEY (reviewed_by) REFERENCES users(id) ON DELETE RESTRICT,
    INDEX idx_request_status (document_request_id, status)
);

-- 5. Notifications Table (system notifications for document workflow)
CREATE TABLE notifications (
    id INT(11) NOT NULL AUTO_INCREMENT,
    shipment_id INT(11) NOT NULL,
    document_request_id INT(11) NULL,
    type ENUM('document_requested', 'document_uploaded', 'document_approved', 'document_rejected', 'document_overdue') NOT NULL,
    title VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    recipient_email VARCHAR(255) NOT NULL,
    is_read TINYINT(1) NOT NULL DEFAULT 0,
    is_sent TINYINT(1) NOT NULL DEFAULT 0,
    sent_at TIMESTAMP NULL,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (id),
    FOREIGN KEY (shipment_id) REFERENCES shipments(id) ON DELETE CASCADE,
    FOREIGN KEY (document_request_id) REFERENCES document_requests(id) ON DELETE SET NULL,
    INDEX idx_shipment_unread (shipment_id, is_read),
    INDEX idx_type_sent (type, is_sent)
);

-- Insert predefined document types
INSERT INTO document_types (name, description, is_predefined, is_active) VALUES
('Custom Clearance Document', 'Required for customs clearance process', 1, 1),
('Commercial Invoice', 'Invoice for commercial shipments', 1, 1),
('Packing List', 'Detailed list of package contents', 1, 1),
('Certificate of Origin', 'Document certifying the origin of goods', 1, 1),
('Insurance Certificate', 'Proof of shipment insurance coverage', 1, 1),
('Import/Export License', 'Required license for international shipments', 1, 1),
('Bill of Lading', 'Transport document for cargo shipments', 1, 1),
('Delivery Receipt', 'Proof of delivery confirmation', 1, 1),
('Damage Report', 'Report for damaged shipments', 1, 1),
('Identity Verification', 'ID verification for pickup/delivery', 1, 1);

-- Create indexes for better performance
CREATE INDEX idx_document_requests_shipment_status ON document_requests(shipment_id, status);
CREATE INDEX idx_document_uploads_request ON document_uploads(document_request_id);
CREATE INDEX idx_notifications_shipment_read ON notifications(shipment_id, is_read);
CREATE INDEX idx_notifications_email_sent ON notifications(recipient_email, is_sent);
