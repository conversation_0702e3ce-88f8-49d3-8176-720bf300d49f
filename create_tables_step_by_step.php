<?php
try {
    $pdo = new PDO('mysql:host=127.0.0.1;dbname=shipment', 'root', 'root');
    
    echo "Creating document management tables step by step...\n\n";
    
    // 1. Document Types Table
    echo "Creating document_types table...\n";
    $sql = "CREATE TABLE document_types (
        id INT(11) NOT NULL AUTO_INCREMENT,
        name VARCHAR(100) NOT NULL,
        description TEXT NULL,
        is_predefined TINYINT(1) NOT NULL DEFAULT 0,
        is_active TINYINT(1) NOT NULL DEFAULT 1,
        created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (id),
        UNIQUE KEY unique_name (name)
    )";
    $pdo->exec($sql);
    echo "✓ document_types table created\n\n";
    
    // 2. Document Requests Table
    echo "Creating document_requests table...\n";
    $sql = "CREATE TABLE document_requests (
        id INT(11) NOT NULL AUTO_INCREMENT,
        shipment_id INT(11) NOT NULL,
        document_type_id INT(11) NOT NULL,
        requested_by INT(11) NOT NULL,
        request_message TEXT NULL,
        priority ENUM('low', 'medium', 'high', 'urgent') NOT NULL DEFAULT 'medium',
        due_date DATETIME NULL,
        status ENUM('pending', 'uploaded', 'approved', 'rejected', 'cancelled') NOT NULL DEFAULT 'pending',
        created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (id),
        FOREIGN KEY (shipment_id) REFERENCES shipments(id) ON DELETE CASCADE,
        FOREIGN KEY (document_type_id) REFERENCES document_types(id) ON DELETE RESTRICT,
        FOREIGN KEY (requested_by) REFERENCES users(id) ON DELETE RESTRICT
    )";
    $pdo->exec($sql);
    echo "✓ document_requests table created\n\n";
    
    // 3. Document Uploads Table
    echo "Creating document_uploads table...\n";
    $sql = "CREATE TABLE document_uploads (
        id INT(11) NOT NULL AUTO_INCREMENT,
        document_request_id INT(11) NOT NULL,
        original_filename VARCHAR(255) NOT NULL,
        stored_filename VARCHAR(255) NOT NULL,
        file_path VARCHAR(500) NOT NULL,
        file_size INT(11) NOT NULL,
        mime_type VARCHAR(100) NOT NULL,
        upload_notes TEXT NULL,
        uploaded_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (id),
        FOREIGN KEY (document_request_id) REFERENCES document_requests(id) ON DELETE CASCADE
    )";
    $pdo->exec($sql);
    echo "✓ document_uploads table created\n\n";
    
    // 4. Document Approvals Table
    echo "Creating document_approvals table...\n";
    $sql = "CREATE TABLE document_approvals (
        id INT(11) NOT NULL AUTO_INCREMENT,
        document_request_id INT(11) NOT NULL,
        document_upload_id INT(11) NOT NULL,
        reviewed_by INT(11) NOT NULL,
        status ENUM('approved', 'rejected') NOT NULL,
        review_notes TEXT NULL,
        reviewed_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (id),
        FOREIGN KEY (document_request_id) REFERENCES document_requests(id) ON DELETE CASCADE,
        FOREIGN KEY (document_upload_id) REFERENCES document_uploads(id) ON DELETE CASCADE,
        FOREIGN KEY (reviewed_by) REFERENCES users(id) ON DELETE RESTRICT
    )";
    $pdo->exec($sql);
    echo "✓ document_approvals table created\n\n";
    
    // 5. Notifications Table
    echo "Creating notifications table...\n";
    $sql = "CREATE TABLE notifications (
        id INT(11) NOT NULL AUTO_INCREMENT,
        shipment_id INT(11) NOT NULL,
        document_request_id INT(11) NULL,
        type ENUM('document_requested', 'document_uploaded', 'document_approved', 'document_rejected', 'document_overdue') NOT NULL,
        title VARCHAR(255) NOT NULL,
        message TEXT NOT NULL,
        recipient_email VARCHAR(255) NOT NULL,
        is_read TINYINT(1) NOT NULL DEFAULT 0,
        is_sent TINYINT(1) NOT NULL DEFAULT 0,
        sent_at TIMESTAMP NULL,
        created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (id),
        FOREIGN KEY (shipment_id) REFERENCES shipments(id) ON DELETE CASCADE,
        FOREIGN KEY (document_request_id) REFERENCES document_requests(id) ON DELETE SET NULL
    )";
    $pdo->exec($sql);
    echo "✓ notifications table created\n\n";
    
    // Insert predefined document types
    echo "Inserting predefined document types...\n";
    $types = [
        ['Custom Clearance Document', 'Required for customs clearance process'],
        ['Commercial Invoice', 'Invoice for commercial shipments'],
        ['Packing List', 'Detailed list of package contents'],
        ['Certificate of Origin', 'Document certifying the origin of goods'],
        ['Insurance Certificate', 'Proof of shipment insurance coverage'],
        ['Import/Export License', 'Required license for international shipments'],
        ['Bill of Lading', 'Transport document for cargo shipments'],
        ['Delivery Receipt', 'Proof of delivery confirmation'],
        ['Damage Report', 'Report for damaged shipments'],
        ['Identity Verification', 'ID verification for pickup/delivery']
    ];
    
    $stmt = $pdo->prepare("INSERT INTO document_types (name, description, is_predefined, is_active) VALUES (?, ?, 1, 1)");
    
    foreach ($types as $type) {
        $stmt->execute($type);
    }
    
    echo "✓ Inserted " . count($types) . " predefined document types\n\n";
    
    echo "All document management tables created successfully!\n";
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
?>
