<?php
try {
    $pdo = new PDO('mysql:host=127.0.0.1;dbname=shipment', 'root', 'root');
    
    // Get shipment ID for TSD250422999001
    $stmt = $pdo->prepare('SELECT id FROM shipments WHERE tracking_number = ?');
    $stmt->execute(['TSD250422999001']);
    $shipment = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$shipment) {
        echo "Shipment not found!\n";
        exit;
    }
    
    $shipment_id = $shipment['id'];
    
    // Get admin user ID (assuming first user is admin)
    $stmt = $pdo->query('SELECT id FROM users LIMIT 1');
    $admin = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$admin) {
        echo "No admin user found!\n";
        exit;
    }
    
    $admin_id = $admin['id'];
    
    // Get document type ID for "Custom Clearance Document"
    $stmt = $pdo->prepare('SELECT id FROM document_types WHERE name = ?');
    $stmt->execute(['Custom Clearance Document']);
    $docType = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$docType) {
        echo "Document type not found!\n";
        exit;
    }
    
    $doc_type_id = $docType['id'];
    
    // Create test document request
    $stmt = $pdo->prepare('
        INSERT INTO document_requests 
        (shipment_id, document_type_id, requested_by, request_message, priority, due_date, status) 
        VALUES (?, ?, ?, ?, ?, ?, ?)
    ');
    
    $stmt->execute([
        $shipment_id,
        $doc_type_id,
        $admin_id,
        'Please upload your custom clearance document for international shipment processing. This document is required to clear customs.',
        'high',
        date('Y-m-d H:i:s', strtotime('+3 days')), // Due in 3 days
        'pending'
    ]);
    
    $request_id = $pdo->lastInsertId();
    
    echo "✓ Created test document request with ID: $request_id\n";
    echo "  - Shipment: TSD250422999001\n";
    echo "  - Document Type: Custom Clearance Document\n";
    echo "  - Priority: High\n";
    echo "  - Due Date: " . date('Y-m-d H:i:s', strtotime('+3 days')) . "\n";
    echo "  - Status: Pending\n";
    
    // Create a notification for this request
    $stmt = $pdo->prepare('
        INSERT INTO notifications 
        (shipment_id, document_request_id, type, title, message, recipient_email) 
        VALUES (?, ?, ?, ?, ?, ?)
    ');
    
    $stmt->execute([
        $shipment_id,
        $request_id,
        'document_requested',
        'Document Required for Shipment TSD250422999001',
        'A Custom Clearance Document is required for your shipment. Please upload the document as soon as possible.',
        '<EMAIL>' // This would normally be the receiver's email
    ]);
    
    echo "✓ Created notification for document request\n";
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
?>
