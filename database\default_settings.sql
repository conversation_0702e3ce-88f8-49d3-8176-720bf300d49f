-- Default settings for the application

-- General settings
INSERT INTO settings (category, name, value, type, description) VALUES
('general', 'app_name', 'ELTA Courier', 'string', 'The name of the application'),
('general', 'company_name', 'ELTA Courier Services', 'string', 'Your company\'s legal name'),
('general', 'company_address', '123 Shipping Lane, Logistics City, LC 12345', 'text', 'Your company\'s physical address'),
('general', 'company_phone', '+****************', 'string', 'Your company\'s contact phone number'),
('general', 'company_email', '<EMAIL>', 'string', 'Your company\'s contact email address'),
('general', 'currency', 'USD', 'string', 'The currency used for pricing'),
('general', 'timezone', 'UTC', 'string', 'The timezone used for dates and times'),
('general', 'date_format', 'Y-m-d', 'string', 'The format used for displaying dates')
ON DUPLICATE KEY UPDATE value = VALUES(value);

-- Package settings
INSERT INTO settings (category, name, value, type, description) VALUES
('packages', 'package_types', '[{"name":"Envelope","max_weight":0.5,"dimensions":"30x20x1","price":5.99},{"name":"Small Box","max_weight":2,"dimensions":"20x20x20","price":9.99},{"name":"Medium Box","max_weight":5,"dimensions":"30x30x30","price":14.99},{"name":"Large Box","max_weight":10,"dimensions":"40x40x40","price":19.99}]', 'json', 'Available package types'),
('packages', 'distance_price_factor', '0.1', 'float', 'Price per kilometer'),
('packages', 'weight_price_factor', '0.5', 'float', 'Additional price per kg above the package\'s base weight'),
('packages', 'express_delivery_factor', '1.5', 'float', 'Multiplier for express delivery')
ON DUPLICATE KEY UPDATE value = VALUES(value);

-- Email settings
INSERT INTO settings (category, name, value, type, description) VALUES
('emails', 'mail_mailer', 'smtp', 'string', 'The mail driver to use for sending emails'),
('emails', 'mail_host', 'smtp.example.com', 'string', 'The SMTP server host'),
('emails', 'mail_port', '587', 'integer', 'The SMTP server port'),
('emails', 'mail_username', '<EMAIL>', 'string', 'The SMTP server username'),
('emails', 'mail_password', 'password', 'string', 'The SMTP server password'),
('emails', 'mail_encryption', 'tls', 'string', 'The encryption protocol to use'),
('emails', 'mail_from_address', '<EMAIL>', 'string', 'The email address that will appear in the "From" field'),
('emails', 'mail_from_name', 'ELTA Courier', 'string', 'The name that will appear in the "From" field'),
('emails', 'notify_admin_new_shipment', '1', 'boolean', 'Notify administrators when a new shipment is created'),
('emails', 'notify_customer_status_change', '1', 'boolean', 'Notify customers when their shipment status changes'),
('emails', 'notify_customer_delivery', '1', 'boolean', 'Notify customers when their shipment is delivered')
ON DUPLICATE KEY UPDATE value = VALUES(value);
