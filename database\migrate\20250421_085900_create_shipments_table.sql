-- Create the shipments table
CREATE TABLE IF NOT EXISTS shipments (
    id INT AUTO_INCREMENT PRIMARY KEY,
    tracking_number VARCHAR(50) NOT NULL UNIQUE,
    shipper_name VARCHAR(255) NOT NULL,
    shipper_address TEXT NOT NULL,
    shipper_phone VARCHAR(50) NULL,
    receiver_name VARCHAR(255) NOT NULL,
    receiver_address TEXT NOT NULL,
    receiver_phone VARCHAR(50) NULL,
    origin VARCHAR(255) NOT NULL,
    destination VARCHAR(255) NOT NULL,
    shipment_type VARCHAR(100) NULL, -- Corresponds to settings
    shipment_mode VARCHAR(100) NULL, -- Corresponds to settings
    carrier VARCHAR(100) NULL,       -- Corresponds to settings
    payment_mode VARCHAR(100) NULL,  -- Corresponds to settings
    status VARCHAR(100) NOT NULL,    -- Corresponds to settings
    pickup_date DATETIME NOT NULL,
    departure_date DATETIME NULL,
    expected_delivery_date DATETIME NULL,
    total_weight DECIMAL(10, 2) NULL, -- Example: 10 total digits, 2 after decimal
    comments TEXT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_tracking_number (tracking_number),
    INDEX idx_status (status),
    INDEX idx_pickup_date (pickup_date)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Add any other initial schema or modifications needed for this table here.
