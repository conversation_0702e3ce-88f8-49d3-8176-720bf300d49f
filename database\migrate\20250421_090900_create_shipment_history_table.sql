-- Create the shipment_history table
CREATE TABLE IF NOT EXISTS shipment_history (
    id INT AUTO_INCREMENT PRIMARY KEY,
    shipment_id INT NOT NULL,
    status VARCHAR(100) NOT NULL,    -- Current status of the shipment
    location VARCHAR(255) NOT NULL,   -- Location where the status was updated
    message TEXT NOT NULL,           -- Detailed description of the status update
    date_time DATETIME NOT NULL,     -- When this status/location update occurred
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (shipment_id) REFERENCES shipments(id) ON DELETE CASCADE,
    INDEX idx_shipment_id (shipment_id),
    INDEX idx_status (status),
    INDEX idx_date_time (date_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Add any other initial schema or modifications needed for this table here.
