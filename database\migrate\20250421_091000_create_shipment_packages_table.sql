-- Create the shipment_packages table
CREATE TABLE IF NOT EXISTS shipment_packages (
    id INT AUTO_INCREMENT PRIMARY KEY,
    shipment_id INT NOT NULL,
    piece_type VARCHAR(100) NULL,     -- Type of package (Box, Pallet, etc.) from settings
    description TEXT NULL,            -- Package contents description
    quantity INT NOT NULL DEFAULT 1,
    length DECIMAL(10, 2) NULL,       -- Length in units from settings (cm/inches)
    width DECIMAL(10, 2) NULL,        -- Width in units from settings
    height DECIMAL(10, 2) NULL,       -- Height in units from settings
    weight DECIMAL(10, 2) NULL,       -- Weight in units from settings (kg/lbs)
    volume DECIMAL(10, 2) NULL,       -- Calculated volume
    volumetric_weight DECIMAL(10, 2) NULL, -- Calculated volumetric weight
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIG<PERSON> KEY (shipment_id) REFERENCES shipments(id) ON DELETE CASCADE,
    INDEX idx_shipment_id (shipment_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Add any other initial schema or modifications needed for this table here.
