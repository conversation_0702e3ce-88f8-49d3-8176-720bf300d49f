-- Create the settings table
CREATE TABLE IF NOT EXISTS settings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    category VARCHAR(50) NOT NULL,     -- 'general', 'email', 'packages', etc.
    name VARCHAR(100) NOT NULL,        -- Setting key/name
    value TEXT NULL,                   -- Setting value (can be JSON for arrays)
    type VARCHAR(20) NOT NULL DEFAULT 'string', -- 'string', 'boolean', 'integer', 'json', etc.
    description TEXT NULL,             -- Optional description of the setting
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY unique_setting (category, name),
    INDEX idx_category (category)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Insert default settings
INSERT INTO settings (category, name, value, type, description) VALUES
-- General Settings
('general', 'site_name', 'ELTA Courier', 'string', 'Website name'),
('general', 'company_name', 'ELTA Logistics', 'string', 'Company legal name'),
('general', 'logo_url', NULL, 'string', 'Company logo URL'),
('general', 'support_email', '<EMAIL>', 'string', 'Support email address'),
('general', 'phone', '+1234567890', 'string', 'Support phone number'),

-- Package Settings
('packages', 'enable_multiple_packages', 'true', 'boolean', 'Allow multiple packages per shipment'),
('packages', 'weight_unit', 'kg', 'string', 'Weight unit (kg/lbs)'),
('packages', 'dimension_unit', 'cm', 'string', 'Dimension unit (cm/inch)'),
('packages', 'piece_types', '["Box","Pallet","Envelope","Other"]', 'json', 'Available piece types'),

-- Tracking Settings
('tracking', 'number_prefix', 'ELT', 'string', 'Tracking number prefix'),
('tracking', 'number_suffix', NULL, 'string', 'Tracking number suffix'),
('tracking', 'number_digits', '8', 'integer', 'Number of digits in tracking number'),
('tracking', 'auto_generate', 'true', 'boolean', 'Auto-generate tracking numbers'),

-- Email Settings
('email', 'from_email', '<EMAIL>', 'string', 'System email sender address'),
('email', 'from_name', 'ELTA Courier', 'string', 'System email sender name'),
('email', 'client_notifications', 'true', 'boolean', 'Enable client email notifications'),
('email', 'admin_notifications', 'true', 'boolean', 'Enable admin email notifications'),
('email', 'client_notify_statuses', '["Delivered","In Transit","Exception"]', 'json', 'Status changes that trigger client notifications'),
('email', 'admin_notify_statuses', '["Exception"]', 'json', 'Status changes that trigger admin notifications'),
('email', 'admin_emails', '["<EMAIL>"]', 'json', 'Admin email addresses for notifications'),

-- Shipment Settings
('shipment', 'statuses', '["Pending","Picked Up","In Transit","Out for Delivery","Delivered","Exception","Cancelled"]', 'json', 'Available shipment statuses'),
('shipment', 'types', '["Standard","Express","Same Day"]', 'json', 'Available shipment types'),
('shipment', 'modes', '["Ground","Air","Sea"]', 'json', 'Available shipment modes'),
('shipment', 'payment_modes', '["Cash","Credit Card","Account"]', 'json', 'Available payment modes');

-- Add any other initial settings needed here
