-- Update the shipments table to add client_id and employee_id
ALTER TABLE shipments 
ADD COLUMN client_id INT NULL AFTER tracking_number,
ADD COLUMN employee_id INT NULL AFTER client_id,
ADD FOREIGN KEY (client_id) REFERENCES clients(id) ON DELETE SET NULL,
ADD FOREIGN KEY (employee_id) REFERENCES employees(id) ON DELETE SET NULL,
ADD INDEX idx_client_id (client_id),
ADD INDEX idx_employee_id (employee_id);
