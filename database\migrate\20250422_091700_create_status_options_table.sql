-- Create a table for status options
CREATE TABLE IF NOT EXISTS status_options (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description TEXT NULL,
    color VARCHAR(20) NULL,
    is_active BOOLEAN NOT NULL DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY (name)
);

-- Insert default status options
INSERT INTO status_options (name, description, color, is_active) VALUES
('pending', 'Shipment is pending processing', '#f0ad4e', 1),
('in_transit', 'Shipment is in transit', '#5bc0de', 1),
('delivered', 'Shipment has been delivered', '#5cb85c', 1),
('delayed', 'Shipment is delayed', '#d9534f', 1),
('cancelled', 'Shipment has been cancelled', '#777777', 1);
