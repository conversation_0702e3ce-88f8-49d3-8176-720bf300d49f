-- Add currency support to the courier system
-- This migration adds currency fields to shipments and settings tables

-- Add currency field to shipments table
ALTER TABLE shipments 
ADD COLUMN currency VARCHAR(3) DEFAULT 'EUR' AFTER total_freight,
ADD COLUMN exchange_rate DECIMAL(10,4) DEFAULT 1.0000 AFTER currency;

-- Add currency settings to settings table
INSERT INTO settings (category, setting_key, setting_value, description) VALUES
('currency', 'default_currency', 'EUR', 'Default currency for new shipments'),
('currency', 'supported_currencies', 'USD,EUR,GBP,CAD,AUD,JPY,CHF,SEK,NOK,DKK', 'Comma-separated list of supported currencies'),
('currency', 'currency_symbols', '{"USD":"$","EUR":"€","GBP":"£","CAD":"C$","AUD":"A$","JPY":"¥","CHF":"CHF","SEK":"kr","NOK":"kr","DKK":"kr"}', 'JSON object mapping currency codes to symbols'),
('currency', 'auto_update_rates', '0', 'Whether to automatically update exchange rates (0=disabled, 1=enabled)'),
('currency', 'base_currency', 'EUR', 'Base currency for exchange rate calculations');

-- Create exchange rates table for future use
CREATE TABLE IF NOT EXISTS exchange_rates (
    id INT AUTO_INCREMENT PRIMARY KEY,
    from_currency VARCHAR(3) NOT NULL,
    to_currency VARCHAR(3) NOT NULL,
    rate DECIMAL(10,4) NOT NULL,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY unique_currency_pair (from_currency, to_currency)
);

-- Insert some default exchange rates (these should be updated regularly in production)
INSERT INTO exchange_rates (from_currency, to_currency, rate) VALUES
('EUR', 'USD', 1.0850),
('EUR', 'GBP', 0.8650),
('EUR', 'CAD', 1.4750),
('EUR', 'AUD', 1.6250),
('EUR', 'JPY', 161.50),
('EUR', 'CHF', 0.9650),
('EUR', 'SEK', 11.45),
('EUR', 'NOK', 11.85),
('EUR', 'DKK', 7.46),
('USD', 'EUR', 0.9217),
('GBP', 'EUR', 1.1561),
('CAD', 'EUR', 0.6780),
('AUD', 'EUR', 0.6154),
('JPY', 'EUR', 0.0062),
('CHF', 'EUR', 1.0363),
('SEK', 'EUR', 0.0873),
('NOK', 'EUR', 0.0844),
('DKK', 'EUR', 0.1340);
