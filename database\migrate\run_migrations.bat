@echo off
echo Initializing database...

set MYSQL="C:\MAMP\bin\mysql\bin\mysql.exe"
set USER=root
set PASS=root
set SCRIPT_DIR=%~dp0

echo Creating database...
%MYSQL% -u%USER% -p%PASS% < "%SCRIPT_DIR%init_database.sql"
if errorlevel 1 (
    echo Error creating database!
    pause
    exit /b 1
)

echo Database created successfully!
echo.
echo Running migrations...
set DB=shipment

echo Creating shipments table...
%MYSQL% -u%USER% -p%PASS% %DB% < "%SCRIPT_DIR%20250421_085900_create_shipments_table.sql"

echo Creating shipment_history table...
%MYSQL% -u%USER% -p%PASS% %DB% < "%SCRIPT_DIR%20250421_090900_create_shipment_history_table.sql"

echo Creating shipment_packages table...
%MYSQL% -u%USER% -p%PASS% %DB% < "%SCRIPT_DIR%20250421_091000_create_shipment_packages_table.sql"

echo Creating users table...
%MYSQL% -u%USER% -p%PASS% %DB% < "%SCRIPT_DIR%20250421_091100_create_users_table.sql"

echo Creating settings table...
%MYSQL% -u%USER% -p%PASS% %DB% < "%SCRIPT_DIR%20250421_091200_create_settings_table.sql"

echo.
echo Migration completed!
echo Default admin credentials:
echo Username: admin
echo Password: admin123
echo.
pause
