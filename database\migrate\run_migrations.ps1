# PowerShell script to run all migrations in order
Write-Host "Running database migrations..."

# MAMP MySQL path and credentials
$mysqlPath = "C:\MAMP\bin\mysql\bin\mysql"
$user = "root"
$pass = "root"
$database = "shipment"

# Array of migration files in order
$migrations = @(
    "20250421_085900_create_shipments_table.sql",
    "20250421_090900_create_shipment_history_table.sql",
    "20250421_091000_create_shipment_packages_table.sql",
    "20250421_091100_create_users_table.sql",
    "20250421_091200_create_settings_table.sql"
)

# Execute each migration file
foreach ($migration in $migrations) {
    Write-Host "Executing $migration..."
    Get-Content $migration | & $mysqlPath -u $user "-p$pass" $database
    if ($LASTEXITCODE -eq 0) {
        Write-Host "Success: $migration" -ForegroundColor Green
    } else {
        Write-Host "Error executing $migration" -ForegroundColor Red
        exit 1
    }
}

Write-Host "All migrations completed successfully!" -ForegroundColor Green
