@echo off
echo MySQL Terminal for Courier Application
echo =====================================

set MYSQL="C:\MAMP\bin\mysql\bin\mysql.exe"
set USER=root
set PASS=root
set HOST=127.0.0.1
set DB=shipment

:menu
echo.
echo 1. Show Tables
echo 2. Describe Table
echo 3. Select Data from Table
echo 4. Execute Custom Query
echo 5. Exit
echo.

set /p choice=Enter your choice (1-5): 

if "%choice%"=="1" goto show_tables
if "%choice%"=="2" goto describe_table
if "%choice%"=="3" goto select_data
if "%choice%"=="4" goto custom_query
if "%choice%"=="5" goto exit
echo Invalid choice. Please try again.
goto menu

:show_tables
echo.
echo Tables in the %DB% database:
%MYSQL% -u%USER% -p%PASS% -h%HOST% %DB% -e "SHOW TABLES;"
pause
goto menu

:describe_table
echo.
set /p table=Enter table name: 
echo Structure of table %table%:
%MYSQL% -u%USER% -p%PASS% -h%HOST% %DB% -e "DESCRIBE %table%;"
pause
goto menu

:select_data
echo.
set /p table=Enter table name: 
set /p columns=Enter columns (comma-separated, or * for all): 
set /p where=Enter WHERE clause (optional): 
echo Data from %table%:
if "%where%"=="" (
    %MYSQL% -u%USER% -p%PASS% -h%HOST% %DB% -e "SELECT %columns% FROM %table%;"
) else (
    %MYSQL% -u%USER% -p%PASS% -h%HOST% %DB% -e "SELECT %columns% FROM %table% WHERE %where%;"
)
pause
goto menu

:custom_query
echo.
set /p query=Enter SQL query: 
echo Executing query:
%MYSQL% -u%USER% -p%PASS% -h%HOST% %DB% -e "%query%"
pause
goto menu

:exit
echo.
echo MySQL Terminal closed.
exit
