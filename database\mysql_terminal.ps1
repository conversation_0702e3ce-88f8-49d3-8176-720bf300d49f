# MySQL Terminal Script for Courier Application
# This script provides a simple interface for interacting with the MySQL database

# Configuration
$mysqlPath = "C:\MAMP\bin\mysql\bin\mysql.exe"
$user = "root"
$pass = "root"
$dbHost = "127.0.0.1"
$database = "shipment"

# Function to execute a MySQL query
function Execute-MySQLQuery {
    param (
        [Parameter(Mandatory=$true)]
        [string]$Query,

        [Parameter(Mandatory=$false)]
        [string]$Database = $database
    )

    $result = & $mysqlPath -u$user -p$pass -h$dbHost $Database -e "$Query"
    return $result
}

# Function to show tables in the database
function Show-Tables {
    Write-Host "Tables in the $database database:"
    Execute-MySQLQuery "SHOW TABLES;"
}

# Function to describe a table
function Describe-Table {
    param (
        [Parameter(Mandatory=$true)]
        [string]$TableName
    )

    Write-Host "Structure of table ${TableName}:"
    Execute-MySQLQuery "DESCRIBE $TableName;"
}

# Function to select data from a table
function Select-Data {
    param (
        [Parameter(Mandatory=$true)]
        [string]$TableName,

        [Parameter(Mandatory=$false)]
        [string]$Columns = "*",

        [Parameter(Mandatory=$false)]
        [string]$Where = ""
    )

    $query = "SELECT $Columns FROM $TableName"
    if ($Where -ne "") {
        $query += " WHERE $Where"
    }
    $query += ";"

    Write-Host "Data from ${TableName}:"
    Execute-MySQLQuery $query
}

# Function to execute a custom SQL query
function Execute-CustomQuery {
    param (
        [Parameter(Mandatory=$true)]
        [string]$Query
    )

    Write-Host "Executing custom query:"
    Execute-MySQLQuery $Query
}

# Main menu
function Show-Menu {
    Clear-Host
    Write-Host "===== MySQL Terminal for Courier Application ====="
    Write-Host "1: Show Tables"
    Write-Host "2: Describe Table"
    Write-Host "3: Select Data from Table"
    Write-Host "4: Execute Custom Query"
    Write-Host "5: Exit"
    Write-Host "=================================================="
}

# Main loop
$running = $true
while ($running) {
    Show-Menu
    $choice = Read-Host "Enter your choice (1-5)"

    switch ($choice) {
        "1" {
            Show-Tables
            Read-Host "Press Enter to continue"
        }
        "2" {
            $tableName = Read-Host "Enter table name"
            Describe-Table $tableName
            Read-Host "Press Enter to continue"
        }
        "3" {
            $tableName = Read-Host "Enter table name"
            $columns = Read-Host "Enter columns (comma-separated, or * for all)"
            $where = Read-Host "Enter WHERE clause (optional)"
            Select-Data -TableName $tableName -Columns $columns -Where $where
            Read-Host "Press Enter to continue"
        }
        "4" {
            $query = Read-Host "Enter SQL query"
            Execute-CustomQuery $query
            Read-Host "Press Enter to continue"
        }
        "5" {
            $running = $false
        }
        default {
            Write-Host "Invalid choice. Please try again."
            Read-Host "Press Enter to continue"
        }
    }
}

Write-Host "MySQL Terminal closed."
