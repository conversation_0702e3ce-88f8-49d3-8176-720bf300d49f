<?php
// Generate a password hash for 'admin123'
$password = 'admin123';
$hash = password_hash($password, PASSWORD_DEFAULT);

echo "Generated hash for 'admin123': " . $hash . "\n";

// Connect to the database
$dsn = "mysql:host=127.0.0.1;port=3306;dbname=shipment;charset=utf8mb4";
$user = "root";
$pass = "root";

try {
    $pdo = new PDO($dsn, $user, $pass, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        PDO::ATTR_EMULATE_PREPARES => false,
    ]);
    
    // Update the admin user's password
    $stmt = $pdo->prepare("UPDATE users SET password = :password WHERE username = 'admin'");
    $result = $stmt->execute(['password' => $hash]);
    
    if ($result) {
        echo "Admin password updated successfully!\n";
    } else {
        echo "Failed to update admin password.\n";
    }
} catch (PDOException $e) {
    echo "Database error: " . $e->getMessage() . "\n";
}
