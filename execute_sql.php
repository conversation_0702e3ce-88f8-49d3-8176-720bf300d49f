<?php
try {
    $pdo = new PDO('mysql:host=127.0.0.1;dbname=shipment', 'root', 'root');
    
    // Read the SQL file
    $sql = file_get_contents('create_document_management_tables.sql');
    
    // Split into individual statements
    $statements = array_filter(array_map('trim', explode(';', $sql)));
    
    echo "Executing document management database schema...\n\n";
    
    foreach ($statements as $statement) {
        if (empty($statement) || strpos($statement, '--') === 0) {
            continue;
        }
        
        try {
            $pdo->exec($statement);
            
            // Extract table name for feedback
            if (preg_match('/CREATE TABLE\s+(\w+)/i', $statement, $matches)) {
                echo "✓ Created table: {$matches[1]}\n";
            } elseif (preg_match('/INSERT INTO\s+(\w+)/i', $statement, $matches)) {
                echo "✓ Inserted data into: {$matches[1]}\n";
            } elseif (preg_match('/CREATE INDEX\s+(\w+)/i', $statement, $matches)) {
                echo "✓ Created index: {$matches[1]}\n";
            }
        } catch (PDOException $e) {
            echo "✗ Error executing statement: " . $e->getMessage() . "\n";
            echo "Statement: " . substr($statement, 0, 100) . "...\n\n";
        }
    }
    
    echo "\nDatabase schema creation completed!\n";
    
    // Verify tables were created
    echo "\nVerifying created tables:\n";
    $stmt = $pdo->query("SHOW TABLES LIKE '%document%' OR SHOW TABLES LIKE 'notifications'");
    $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    foreach ($tables as $table) {
        echo "- $table\n";
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
?>
