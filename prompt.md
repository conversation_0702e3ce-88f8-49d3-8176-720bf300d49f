Okay, let's create an extremely detailed, multi-faceted prompt for building the Enterprise Logistics Tracking Application (ELTA) from the ground up. This will aim for maximum clarity and cover all your requirements, including the highly detailed frontend tracking UI, backend specifics, architecture, technologies, directory structure, security, debugging, testing, dynamic paths, and terminal-based database operations.

---

**Project Title:** Enterprise Logistics Tracking Application (ELTA) - Highly Detailed Build Prompt

**Version:** 1.0

**Project Goal:** To develop a secure, scalable, and visually rich web-based application for logistics companies to manage and enable their clients to track shipments effectively.

**Application Type:** Standalone Web Application (not a CMS plugin)

**Target Environment:** Standard LAMP/LEMP stack (Linux, Apache/Nginx, MySQL/PostgreSQL, PHP).

**Architecture:**

*   **Layered Architecture:**
    *   **Presentation Layer (Frontend HTML, CSS, JS):** User interface rendering, client-side validation, AJAX interactions, dynamic content updates.
    *   **Application Layer (PHP Controllers, Services):** Handles incoming requests, orchestrates business logic execution, interacts with models and services.
    *   **Domain Layer (PHP Models):** Represents the core business entities (Shipment, User, Setting, etc.) and encapsulates data interaction logic.
    *   **Infrastructure Layer (Database, File System, External Services):** Handles data persistence (DB), file storage (Logo), email sending, etc.
*   **API-Driven Frontend:** The public tracking page will interact with a dedicated backend API endpoint (`/api/track`) via AJAX to fetch shipment data, allowing for a more dynamic and responsive user experience without full page reloads.

**Technologies & Frameworks:**

*   **Backend:** PHP 8.1+ (or latest stable 8.x) is highly recommended for performance and features.
*   **Database:** MySQL 8+ or PostgreSQL 13+.
*   **Frontend:** HTML5, CSS3, JavaScript (Vanilla JS primarily, with specific libraries for UI components like date pickers, charting, or repeatable fields).
*   **UI/CSS Framework:** A **modern, production-ready, and visually appealing** CSS framework suitable for a clean, professional, and potentially courier-branded look. Must be fully responsive. **Strong recommendation for Tailwind CSS or Bulma** due to their flexibility and relatively smaller footprint compared to full-featured frameworks. The framework should be integrated via a build process (like Webpack/Mix or Parcel) if required for customization or asset optimization.
*   **PHP Libraries (Managed via Composer):**
    *   `vlucas/phpdotenv` or similar for `.env` file loading.
    *   A robust database abstraction library or a PDO wrapper class (`src/Core/Database.php`).
    *   `phpmailer/phpmailer` for email sending.
    *   A barcode generation library (e.g., `picqer/php-barcode-generator`).
    *   A simple routing library or a custom router implementation (`src/Core/Router.php`).
    *   (Optional) A simple templating library (e.g., Twig, or stick to plain PHP templates as planned).
*   **JavaScript Libraries (Managed via npm/Yarn):**
    *   A date/time picker library.
    *   A charting library for the tracking progress graph (e.g., Chart.js, ApexCharts - lightweight option preferred).
    *   (Optional) A library for creating repeatable form fields in the admin.
    *   (Optional) A library for simple modal/dialog boxes.

**User Roles & Access:**

*   **Administrator:**
    *   Secure login access to `/admin/*` routes.
    *   Full CRUD (Create, Read, Update, Delete) operations on shipments.
    *   Full access to application settings.
    *   Ability to view application logs and debug information (when debug mode is active).
*   **Public User:**
    *   Access to the public tracking page `/track`.
    *   Ability to submit a tracking number.
    *   View detailed, visually rich tracking information for *that specific shipment* if found.
    *   No access to admin functionality or data modification.

**Database Management (Schema via Terminal Only):**

*   **Schema Definition:** All database schema creation, modifications (adding tables, columns, indexes), and initial data seeding scripts MUST reside in the `database/migrate/` and `database/seed/` directories.
*   **Execution:** These scripts MUST be executed **ONLY via the database terminal** (e.g., `mysql -uuser -p database_name < path/to/script.sql` or `psql -U user -d database_name -f path/to/script.sql`).
*   **Versioning:** Use clear naming conventions for migration scripts (e.g., `YYYYMMDD_HHMMSS_description.sql`) to manage schema evolution.
*   **Application Interaction:** The PHP application will interact with the database *strictly* through the Model classes (`src/Models/`) using a secure database abstraction layer (e.g., PDO wrapper) and prepared statements. Direct SQL queries outside of the Model classes are forbidden.

**File Structure (Clean Root Folder & Web Root):**

The `public/` directory will be the web server's document root. All sensitive files (`src/`, `config/`, `database/`, `vendor/`, `tests/`, `debug/`, `.env`, `composer.json`, etc.) must be outside the web-accessible directory.

```
/ # Application Root (outside web root)
├── .env               # Environment variables (DB_HOST, DB_NAME, DB_USER, DB_PASS, APP_DEBUG, BASE_URL, etc.)
├── .env.example       # Example .env file for setup
├── composer.json      # Composer configuration
├── package.json       # npm/Yarn configuration for frontend assets
├── webpack.mix.js     # Laravel Mix or similar build config (if using)
├── node_modules/      # Frontend dependencies (installed by npm/Yarn)
├── vendor/            # PHP dependencies (installed by Composer)
├── config/            # Application configuration files
│   ├── app.php        # General app config (debug mode, error reporting level, etc.)
│   ├── database.php   # DB connection settings (reads from .env)
│   └── paths.php      # Defines dynamic paths (BASE_URL, BASE_PATH, etc.)
├── database/          # Database schema management (terminal ops ONLY)
│   ├── migrate/       # SQL migration scripts (e.g., 20231027_create_initial_tables.sql)
│   └── seed/          # SQL seed scripts (e.g., initial_admin_user.sql)
├── src/               # Application source code (PHP)
│   ├── Core/          # Core application components
│   │   ├── App.php        # Application bootstrap, loads config, sets up error handling
│   │   ├── Router.php     # Handles URL routing to controllers
│   │   ├── Database.php   # Database connection and query wrapper (PDO)
│   │   ├── Config.php     # Configuration loader
│   │   ├── Session.php    # Secure session management wrapper
│   │   └── View.php       # Handles rendering view templates, passing data
│   ├── Controllers/   # Handle HTTP requests and delegate tasks
│   │   ├── Admin/
│   │   │   ├── AuthController.php       # Handles admin login/logout
│   │   │   ├── DashboardController.php    # Admin dashboard view
│   │   │   ├── ShipmentController.php     # Handles admin shipment CRUD (List, Add, Edit, Delete)
│   │   │   └── SettingsController.php     # Handles admin settings pages (General, Packages, Email)
│   │   └── Api/
│   │       └── TrackingApiController.php  # Handles public AJAX tracking requests
│   ├── Models/        # Interact with the database tables
│   │   ├── Shipment.php       # Shipments table CRUD and data handling
│   │   ├── ShipmentHistory.php  # Shipment history table CRUD
│   │   ├── ShipmentPackage.php  # Shipment packages table CRUD
│   │   ├── User.php           # Users table CRUD
│   │   └── Setting.php        # Settings table CRUD
│   ├── Services/      # Business logic services
│   │   ├── AuthService.php      # Admin authentication logic
│   │   ├── ShipmentService.php  # Complex shipment operations (validation, status updates, history creation)
│   │   ├── SettingsService.php  # Get/Set/Validate settings logic
│   │   ├── EmailService.php     # Handles email templating and sending (uses PHPMailer)
│   │   └── BarcodeService.php   # Generates barcode data/images
│   ├── Utils/         # Helper classes/functions
│   │   ├── Sanitizer.php      # Input sanitization methods
│   │   ├── Validator.php      # Input validation methods
│   │   └── UrlHelper.php      # Helper for generating dynamic URLs (e.g., `baseUrl()`, `assetUrl()`)
│   └── Views/         # HTML templates (PHP files, no complex logic)
│       ├── admin/
│       │   ├── layout.php       # Admin panel base layout
│       │   ├── login.php        # Admin login form
│       │   ├── dashboard.php      # Admin dashboard content
│       │   ├── shipments/       # Admin shipment views
│       │   │   ├── list.php         # Shipment list table HTML
│       │   │   └── form.php         # Add/Edit shipment form HTML
│       │   └── settings/      # Admin settings views
│       │       ├── layout.php       # Settings page layout (tabs)
│       │       ├── general.php      # General settings form
│       │       ├── packages.php     # Multiple Packages settings form
│       │       └── emails.php       # Email settings forms
│       ├── public/
│       │   ├── layout.php       # Public site base layout
│       │   ├── track.php        # Public tracking page (form + results placeholder)
│       │   └── email/         # Email template files (e.g., html/plain text)
│       │       └── shipment_update.php # Template for shipment update email
│       └── error/           # Error pages
│           └── 404.php
├── public/            # Web server's document root - ONLY PUBLIC FILES HERE
│   ├── index.php      # Public entry point, bootstraps app, handles routing
│   ├── .htaccess      # Apache rewrite rules (for clean URLs)
│   ├── css/           # Compiled/built CSS files (from node_modules/src)
│   │   └── app.css
│   ├── js/            # Compiled/built JS files (from node_modules/src)
│   │   └── app.js
│   └── assets/        # Static assets (images, fonts, uploaded logo, etc.)
│       └── logo.png
├── tests/             # PHPUnit tests
│   ├── bootstrap.php  # Test suite bootstrap
│   ├── Unit/          # Unit tests (isolated class logic)
│   │   ├── Services/
│   │   └── Utils/
│   └── Integration/   # Integration tests (interactions between components, DB)
│       └── Models/
├── debug/             # Debug logs and tools
│   └── app.log        # Main application log file
└── README.md          # Project setup, usage, DB migration instructions, testing instructions
```

**Detailed Feature Implementation:**

**1. Core Application Setup:**

*   **Bootstrap (`src/Core/App.php`, `public/index.php`):**
    *   Load environment variables (`.env`).
    *   Load configuration files (`config/`).
    *   Set up error reporting and logging based on `APP_DEBUG` in `.env` and `config/app.php`.
    *   Initialize the database connection (`src/Core/Database.php`).
    *   Start/resume secure session (`src/Core/Session.php`).
    *   Initialize the Router and dispatch the request.
*   **Dynamic Paths (`config/paths.php`, `src/Utils/UrlHelper.php`):**
    *   Define constants or variables for `BASE_PATH` (file system path to app root) and `BASE_URL` (web URL to public directory) in config, potentially detecting them dynamically based on server variables or reading from `.env`.
    *   Create helper functions (e.g., `UrlHelper::assetUrl($path)`, `UrlHelper::routeUrl($routeName, $params)`) to generate all links and resource URLs dynamically. **NO hardcoded `/css/`, `/js/`, `/admin/`, `/track` paths** in the code. Use these helper functions everywhere.
*   **Routing (`src/Core/Router.php`):**
    *   Define routes mapping URL patterns to Controller@method actions (e.g., `GET /admin/shipments` -> `ShipmentController@list`, `POST /api/track` -> `TrackingApiController@track`).
    *   Implement route grouping and middleware (e.g., `/admin` group requires authentication middleware).
*   **Database Interaction (`src/Core/Database.php`, `src/Models/*`):**
    *   `src/Core/Database.php`: Wrapper around PDO for secure connections and query execution. Methods for `query()`, `prepare()`, `execute()`, `fetch()`, `fetchAll()`, `lastInsertId()`, transaction management.
    *   `src/Models/*`: Each model class represents a DB table. Methods for basic CRUD (`find($id)`, `findAll()`, `create($data)`, `update($id, $data)`, `delete($id)`) and specific queries (e.g., `Shipment->findByTrackingNumber($number)`, `ShipmentHistory->findByShipmentId($id)`). **ALL database access from Controllers/Services MUST go through these Model methods.**

**2. Backend (Admin Panel - `/admin`):**

*   **Login (`src/Controllers/Admin/AuthController.php`, `src/Views/admin/login.php`):**
    *   Display login form at `/admin`.
    *   Handle POST request: Validate username/password. Use `password_verify()` against the stored hash.
    *   If successful, create a secure session (`src/Core/Session.php`), store user ID/role, regenerate session ID, redirect to dashboard.
    *   If failed, show error message.
    *   Logout route (`/admin/logout`) to destroy session.
*   **Dashboard (`src/Controllers/Admin/DashboardController.php`, `src/Views/admin/dashboard.php`):**
    *   Simple landing page after login. Can display summary stats (total shipments, pending shipments, etc.) fetched via Models.
*   **Shipment Management (`src/Controllers/Admin/ShipmentController.php`, `src/Models/Shipment.php`, etc.):**
    *   **List (`/admin/shipments`, `src/Views/admin/shipments/list.php`):**
        *   Fetch paginated shipment data, joining or querying history for the latest status.
        *   Render an HTML table using the chosen CSS framework's table components.
        *   Implement server-side sorting and filtering via URL parameters (`?sort=column&order=asc`, `?status=pending`). Pass parameters to Model queries.
        *   Action links/buttons for View (optional, could just click row), Edit (`/admin/shipments/edit/{id}`), Delete (`/admin/shipments/delete/{id}`). Use dynamic URL helpers.
    *   **Add (`/admin/shipments/new`, `src/Views/admin/shipments/form.php`):**
        *   Render the shipment form template.
        *   PHP populates dropdowns for Type, Mode, Location, Carrier, Payment Mode, Status (from settings via `SettingsService`).
        *   Tracking Number: If settings enable autogenerate, pre-fill based on Prefix/Suffix/Digits. If manual, leave empty.
        *   **JavaScript:** Initialize date/time pickers. Setup repeatable fields for History and Packages.
        *   Handle POST submission to `/admin/shipments/new`. Validate *all* input data using `src/Utils/Validator.php`. Sanitize inputs using `src/Utils/Sanitizer.php`. Use `ShipmentService` to handle creation logic (including tracking number validation/generation, saving main shipment, history, and packages). Redirect to the list view on success.
    *   **Edit (`/admin/shipments/edit/{id}`, `src/Views/admin/shipments/form.php`):**
        *   Fetch existing shipment data, history, and packages using Models.
        *   Render the form template, pre-filling all fields.
        *   **JavaScript:** Initialize date/time pickers. Populate repeatable fields with existing History and Package data. Setup JS for adding new entries.
        *   Handle POST submission to `/admin/shipments/edit/{id}`. Validate/Sanitize inputs. Use `ShipmentService` to handle update logic (updating main shipment, adding *new* history entries, updating/adding/deleting packages). Redirect on success.
    *   **Delete (`/admin/shipments/delete/{id}`):**
        *   Handle POST request (should be triggered by a form/button with CSRF token).
        *   Use `ShipmentService` to delete the shipment and related history/packages (database cascade delete is an option). Redirect on success.
*   **Settings Management (`src/Controllers/Admin/SettingsController.php`, `src/Models/Setting.php`, `src/Services/SettingsService.php`):**
    *   **Pages (`/admin/settings/general`, `/admin/settings/packages`, `/admin/settings/emails`):**
        *   Render forms for each settings section using the tabbed layout (`src/Views/admin/settings/layout.php`).
        *   Load current settings via `SettingsService`.
        *   **Comma-separated lists:** In PHP, store these as JSON arrays in the `settings` table. On loading, decode the JSON and populate the textarea with a comma-separated string. On saving, convert the comma-separated string back to a JSON array.
        *   **Logo Upload:** Handle file upload on the server. Store the file in `public/assets/` (or a dedicated `uploads` subdir within public). Save the resulting URL (`assetUrl('assets/logo.png')`) in settings. Implement validation for file type/size.
        *   **Dynamic Number Format:** PHP logic for generating the tracking number based on Prefix, Suffix, Digits, and a counter/random generator (ensure uniqueness loop).
    *   Handle POST submission for each settings page. Validate and sanitize inputs. Use `SettingsService` to update settings in the database.

**3. Frontend (Public Tracking - `/track`):**

*   **Tracking Page (`public/index.php` routed to `/track`, `src/Views/public/track.php`):**
    *   Serve the main HTML page with a form.
    *   Include necessary CSS framework files and your main `public/css/app.css`.
    *   Include necessary JS libraries (charting, date picker if needed for display formatting) and your main `public/js/app.js`.
    *   The page has a placeholder element (`#tracking-results`) for injecting dynamic content.
*   **JavaScript (`public/js/app.js`):**
    *   Event listener on the tracking form submit.
    *   `event.preventDefault()`.
    *   Get tracking number input value.
    *   Basic client-side validation (check if empty).
    *   Display loading indicator (e.g., spinner styled with CSS framework).
    *   Clear previous content in `#tracking-results`.
    *   Make an **AJAX POST** request to `/api/track`. Send the tracking number in the request body (JSON or form data).
    *   Handle AJAX response:
        *   Hide loading indicator.
        *   If response is success (e.g., HTTP 200, JSON status 'success'):
            *   Parse JSON data (shipment details, history, packages, settings info like units, logo URL, barcode data).
            *   **Dynamically build detailed HTML for results** based on the JSON data.
            *   Inject the generated HTML into `#tracking-results`.
        *   If response is error (e.g., HTTP 404, JSON status 'error'):
            *   Display the error message from the JSON response (e.g., "Shipment not found.") in `#tracking-results`.
*   **PHP API Endpoint (`/api/track`, `src/Controllers/Api/TrackingApiController.php`):**
    *   Receive AJAX request (POST).
    *   Get tracking number from request body.
    *   Sanitize and validate the tracking number input.
    *   Use `ShipmentService` to fetch shipment data by tracking number. The service should query `shipments`, `shipment_history` (ordered), and `shipment_packages` (if enabled for results in settings). It should also fetch relevant settings (logo, barcode, package display, units).
    *   If shipment not found, return a JSON error response (e.g., `{"status": "error", "message": "Shipment not found."}`).
    *   If found:
        *   Use `BarcodeService` to generate barcode data (e.g., base64 image string or a URL to an image generated server-side temporarily) if barcode display is enabled.
        *   Format the fetched data into a structured PHP array.
        *   Calculate total package weights/volumes server-side as a fallback/verification, send these in the JSON.
        *   Return a JSON success response containing all shipment details, history, packages, logo URL, barcode data, units, etc. (`{"status": "success", "data": {...}}`).
*   **Highly Detailed Tracking Results UI (HTML Generated by JS, Styled by CSS Framework):**
    *   **Use the chosen CSS framework components extensively:** Cards, panels, grids, flexbox for layout. Typography, buttons, forms.
    *   **Header:** Logo (dynamically loaded `<img>` using the URL from settings), Tracking Number (large font), current Status (prominent label, potentially with status-specific background color styled via CSS classes). Barcode (dynamically generated `<img>` using barcode data).
    *   **Shipment Overview:** Use a multi-column layout (grid/flexbox) to display Shipper, Receiver, Origin, Destination, Dates (Pickup, Departure, Expected Delivery), Weight, Type, Mode, Carrier, etc. Use icons next to each label (e.g., user icon, map pin icon, calendar icon - using FontAwesome or similar icon library).
    *   **Shipment Movement/Progress Visualization:**
        *   **Timeline:** A clear vertical timeline using CSS. Each history entry is a point on the line.
        *   **Status Icons:** Use a relevant icon next to each timeline point representing the status (e.g., `fa-box`, `fa-truck-moving`, `fa-home`, `fa-check-circle`, `fa-ban`). Style the icon color based on status.
        *   **Progress Graph/Bar:** Implement a simple progress bar or a line graph (using a JS charting library like Chart.js) that visually represents the shipment's progress through the defined status steps or over time based on history entries. This adds a strong visual "movement" element.
    *   **Packages Details (Conditional):** If enabled in settings, display a responsive table or list of package details (Qty, Piece Type, Description, Length, Width, Height, Weight). Display calculated totals (Volumetric, Volume, Actual) below.
    *   **Responsiveness:** Ensure the entire results layout reflows correctly on different screen sizes using the CSS framework's responsive utilities.

**4. Email Notifications (PHP):**

*   **Email Service (`src/Services/EmailService.php`):**
    *   Contains a method like `sendShipmentUpdateEmail($shipmentId, $recipients, $template)` which fetches shipment data.
    *   Implements logic for **merge tag replacement**. Use `str_replace` or a simple loop to replace `{tag}` placeholders in the subject, body, etc., with actual shipment data values fetched from the database.
    *   Uses PHPMailer to configure and send the email. Set 'From' address using the "Domain Email" from settings. Handle 'To', 'Cc', 'Bcc' parsed from settings, supporting comma-separated emails and merge tags within these fields.
    *   Load email body content from template files in `src/Views/public/email/`.
*   **Triggering:**
    *   In `src/Services/ShipmentService.php`, after successfully updating a shipment and adding a new history entry:
    *   Check Client and Admin email settings (`SettingsService`).
    *   If enabled for Client AND the new status is in the Client's selected statuses: Call `EmailService->sendShipmentUpdateEmail()` with client recipients (from shipment data via merge tags) and the client email template.
    *   If enabled for Admin AND the new status is in the Admin's selected statuses: Call `EmailService->sendShipmentUpdateEmail()` with admin recipients (from settings via merge tags) and the admin email template.
*   Use a background process or queue for email sending in a production environment if email volume is high (out of scope for initial version, but good to note).

**5. Security Requirements (Comprehensive):**

*   **Input Validation & Sanitization:**
    *   Use `src/Utils/Validator.php` for server-side validation of *all* incoming data (forms, API requests) – check required fields, data types (numeric, string length, email format, date format), valid options (for dropdowns).
    *   Use `src/Utils/Sanitizer.php` to clean inputs before using them (e.g., `filter_var` with appropriate filters, `htmlspecialchars` or similar for strings, casting to required types). Sanitize before saving to DB or using in queries/output.
*   **Output Escaping:**
    *   Escape *all* data rendered in HTML views to prevent XSS attacks (`htmlspecialchars()`, `htmlentities()`). Apply this strictly to *any* data originating from the database or user input before displaying it.
*   **Database Security:**
    *   Use **Prepared Statements** via PDO (`src/Core/Database.php`) for *all* database queries involving user-supplied data. **NEVER** concatenate user input directly into SQL strings.
    *   Grant minimum necessary privileges to the database user configured in `.env`.
*   **Authentication & Session Security:**
    *   Use `password_hash()` and `password_verify()` for storing and checking admin passwords.
    *   Use `session_regenerate_id(true)` after successful login to prevent session fixation.
    *   Configure sessions to use HTTP-only cookies to mitigate XSS session hijacking.
    *   Set appropriate session cookie secure flags and SameSite policies.
    *   Implement session timeouts and inactivity limits for the admin panel.
*   **CSRF Protection:** Implement CSRF tokens for all state-changing admin forms (Add, Edit, Delete, Settings). Generate a unique token per session, include it in forms (hidden field), and verify it on POST requests.
*   **Access Control:** Strictly enforce access to `/admin/*` routes based on authentication and potentially user roles using routing middleware or checks in Controllers.
*   **File Uploads (Logo):** Validate uploaded file types (allow only images), file size, and use a secure naming convention to prevent directory traversal or execution of malicious scripts (e.g., rename file, store outside web root and serve via a PHP script, or store in web root with strict MIME type headers and no execute permissions). Store in `public/assets/uploads/`.
*   **API Security (`/api/track`):** This endpoint is public, so prevent abuse:
    *   Strictly validate the tracking number format.
    *   Implement basic rate limiting based on IP address or session to prevent brute-force attacks or scraping.
    *   Only allow READ operations.
*   **HTTP Headers:** Configure web server or application to send security headers (e.g., `Strict-Transport-Security`, `X-Content-Type-Options: nosniff`, `X-Frame-Options: DENY`, `Content-Security-Policy` - start simple and make it stricter).
*   **Error Reporting:** Disable displaying errors on production environments (`display_errors = Off` in `php.ini` or `config/app.php`). Rely on logging instead.

**6. Debugging & Logging:**

*   **Debug Mode (`.env`, `config/app.php`):** A boolean flag (`APP_DEBUG`).
*   **Error Reporting:** Configure PHP error reporting levels in `config/app.php`. In debug mode, display errors (during development only). In production, disable display but enable logging.
*   **Logging (`debug/app.log`):**
    *   Implement a simple logging mechanism (`src/Utils/Logger.php` or a function `log_message($level, $message, $context = [])`).
    *   Log errors, warnings, and notices.
    *   Log important application events (e.g., admin login/logout, shipment creation/update/deletion, failed tracking lookups, email sending attempts/failures).
    *   Include timestamps and severity levels (INFO, WARNING, ERROR, DEBUG).
    *   Ensure the `debug/` directory is writable by the web server user.
*   **Debugging Tools:** Mention using Xdebug for step debugging during development.

**7. Testing (Via PHPUnit):**

*   **Setup:** Configure `phpunit.xml` to define test suites and bootstrap the application for tests (`tests/bootstrap.php`).
*   **Unit Tests (`tests/Unit/`):** Write tests for individual classes and methods in isolation. Mock dependencies (like the Database class or external services).
    *   Examples: `Validator` methods, `Sanitizer` methods, `UrlHelper` output, `BarcodeService` output format, `EmailService` merge tag replacement logic (without actually sending email), basic Model methods (e.g., testing query building logic).
*   **Integration Tests (`tests/Integration/`):** Test interactions between multiple components, including database interactions. Requires a separate test database instance.
    *   Examples: Test shipment creation via `ShipmentService` and verify data in DB (`shipments`, `shipment_history`, `shipment_packages`), test fetching data via `ShipmentService` and verify format, test the `TrackingApiController` response format given specific DB data, test authentication flow via `AuthController`.
*   **Execution:** Tests must be runnable from the terminal using the `phpunit` command from the application root. Instructions in `README.md`.
*   **Test Data:** Use factory patterns or seed scripts (`database/seed/`) to create test data in the test database.

**8. Deployment Considerations:**

*   Instructions in `README.md` for setting `public/` as the web server document root.
*   Provide `.htaccess` rules for Apache or equivalent configuration for Nginx to direct all requests (except static assets) to `public/index.php` while allowing access to static files in `public/`.
*   Mention setting appropriate file permissions.
*   Remind to disable `APP_DEBUG` and hide error details in production.

**Deliverables:**

*   Complete source code meeting all requirements and the specified file structure.
*   Database schema migration scripts (`database/migrate/`) and optional seed scripts (`database/seed/`).
*   Detailed `README.md` including:
    *   System Requirements
    *   Installation Guide (Cloning, Composer, npm/Yarn, `.env` setup, DB creation & migration via terminal).
    *   Web server configuration hints (.htaccess/Nginx).
    *   How to run tests.
    *   How to enable/disable debug mode and view logs.
    *   Initial admin user creation steps (e.g., running a seed script or a command).
    *   Usage guide for admin panel settings and shipment management.
    *   Link to the public tracking page (`/track`).
*   `composer.json` and `package.json`.
*   `.env.example` file.
*   Comprehensive code comments explaining complex logic.
