# Enterprise Logistics Tracking Application (ELTA)
---

**Project Title:** Enterprise Logistics Tracking Application (ELTA)

**Purpose:** Develop a modern, secure, and highly detailed web application for managing and tracking logistics shipments. The application will feature a comprehensive admin panel for operators to add, update, and monitor shipments, and a public-facing frontend designed for end-users to track their packages with rich visual feedback.

**Target Audience:** Small to medium logistics companies requiring an in-house tracking solution.

**Architecture:**

*   **Backend (PHP):** Acts as the core engine. Handles routing, requests, business logic, database interactions, authentication, admin panel rendering, API endpoint for tracking, and email notifications.
*   **Frontend (HTML, CSS, JavaScript):** Provides the user interface. HTML for structure, CSS for styling (leveraging a framework), and JavaScript for dynamic behavior, form interactions (AJAX), and rendering detailed tracking results.
*   **Database (MySQL or PostgreSQL):** Stores all application data securely.

**Technologies & Frameworks:**

*   **Backend:** PHP (version 7.4+ or 8+ strongly recommended).
*   **Database:** MySQL or PostgreSQL.
*   **Frontend:** HTML5, CSS3, JavaScript (Vanilla JS preferred, or a minimal library if strictly necessary for specific components like date pickers, repeatable fields).
*   **UI/CSS Framework:** Integrate a *modern, clean, and courier-style* responsive CSS framework. Focus on aesthetics suitable for logistics (e.g., professional, clear typography, perhaps subtle use of logistics-related colors). Examples: **Bulma, Tailwind CSS, or a similar lightweight, modern framework.** Avoid bulky frameworks like older Bootstrap versions unless specifically justified and only necessary components are included.
*   **PHP Libraries (Via Composer):** Utilize Composer for dependency management. Essential libraries might include:
    *   A database abstraction layer (e.g., PDO wrapper).
    *   PHPMailer for robust email sending.
    *   A simple routing library (optional, can be custom).
    *   A barcode generation library (PHP-based or JS-based).

**User Roles & Access:**

*   **Administrator:** Full access to the entire application backend (`/admin` route). Can add, view, edit, delete shipments, manage settings, view logs (if implemented). Secure login required.
*   **Public User:** Can only access the public tracking page (`/track` route). Can input a tracking number and view the corresponding shipment details if found and publicly visible. No authentication required for tracking.

**Database Design (Required Tables - Terminal Operated):**

Database schema changes MUST be managed via SQL scripts executed from the terminal, not GUI tools. Include initial scripts for table creation.

1.  `users`: Stores admin users. Columns: `id` (PK), `username` (UNIQUE), `password_hash`, `role` (e.g., 'admin'), `created_at`, `updated_at`.
2.  `settings`: Stores application settings. Columns: `key` (VARCHAR, UNIQUE), `value` (TEXT or JSON), `created_at`, `updated_at`. Stores data for General, Package, and Email settings (can be stored as JSON or serialized arrays in the `value` column, keyed by type).
3.  `shipments`: Stores core shipment details. Columns: `id` (PK), `tracking_number` (VARCHAR, UNIQUE, INDEX), `shipper_name`, `shipper_phone`, `shipper_address`, `shipper_email`, `receiver_name`, `receiver_phone`, `receiver_address`, `receiver_email`, `shipment_type`, `weight`, `courier`, `packages_count` (INT), `mode`, `product`, `quantity` (INT), `payment_mode`, `total_freight` (DECIMAL), `carrier`, `carrier_reference_no`, `departure_time` (DATETIME), `origin`, `destination`, `pickup_date` (DATE), `pickup_time` (TIME), `expected_delivery_date` (DATE), `comments` (TEXT), `assigned_client_id` (INT, FK to `users` or separate clients table if needed, nullable), `assigned_agent_id` (INT, FK to `users`, nullable), `assigned_employee_id` (INT, FK to `users`, nullable), `created_at` (DATETIME), `updated_at` (DATETIME).
4.  `shipment_history`: Stores updates for each shipment. Columns: `id` (PK), `shipment_id` (INT, FK to `shipments`, INDEX), `date` (DATE), `time` (TIME), `location`, `status`, `updated_by` (VARCHAR - store username or user ID), `remarks` (TEXT), `created_at` (DATETIME). Order by `date` then `time` ascending.
5.  `shipment_packages`: Stores multiple package details. Columns: `id` (PK), `shipment_id` (INT, FK to `shipments`, INDEX), `qty` (INT), `piece_type`, `description`, `length` (DECIMAL), `width` (DECIMAL), `height` (DECIMAL), `weight` (DECIMAL), `created_at` (DATETIME). Only populated if multiple packages are enabled.

**File Structure (Perfect Clean Root Folder):**

```
/ # Application Root
├── config/ # Configuration files
│   ├── config.php # Database credentials, base paths, URLs, debug settings
│   └── settings.default.php # Default settings structure (for installation/reference)
├── database/ # SQL scripts for schema management (terminal ops)
│   ├── migrate/ # Migration scripts (e.g., v1.0_create_tables.sql)
│   └── seed/ # Optional seed data
├── public/ # Web-accessible files (document root of the web server)
│   ├── index.php # Public entry point, handles routing
│   ├── css/ # Compiled/processed CSS, framework CSS
│   │   └── style.css # Main application CSS
│   ├── js/ # Compiled/processed JS, framework JS
│   │   └── script.js # Main application JS
│   └── assets/ # Images, fonts, etc.
│       └── images/
├── src/ # Application source code (PHP)
│   ├── Controllers/ # Handle requests, orchestrate logic
│   │   ├── Admin/
│   │   │   ├── AuthController.php # Login/Logout
│   │   │   ├── DashboardController.php
│   │   │   ├── ShipmentController.php # List, Add, Edit, Delete
│   │   │   └── SettingsController.php # Handle settings pages
│   │   └── Api/
│   │       └── TrackingApiController.php # Handles public tracking AJAX requests
│   ├── Models/ # Database interaction logic
│   │   ├── Shipment.php # Handles shipments table
│   │   ├── ShipmentHistory.php # Handles shipment_history table
│   │   ├── ShipmentPackage.php # Handles shipment_packages table
│   │   ├── User.php # Handles users table
│   │   └── Setting.php # Handles settings table
│   ├── Services/ # Business logic components
│   │   ├── AuthService.php # User authentication/session
│   │   ├── ShipmentService.php # Complex shipment operations, data validation
│   │   ├── SettingsService.php # Get/Set settings
│   │   ├── EmailService.php # Sending emails with merge tags (uses PHPMailer)
│   │   └── BarcodeService.php # Barcode generation logic
│   ├── Views/ # HTML templates (PHP files)
│   │   ├── admin/
│   │   │   ├── layout.php # Admin layout wrapper
│   │   │   ├── login.php
│   │   │   ├── dashboard.php
│   │   │   ├── shipments/ # Shipment views
│   │   │   │   ├── list.php
│   │   │   │   └── form.php # Used for add and edit
│   │   │   └── settings/ # Settings views
│   │   │       ├── layout.php # Settings page layout (tabs)
│   │   │       ├── general.php
│   │   │       ├── packages.php
│   │   │       └── emails.php
│   │   └── public/
│   │       ├── layout.php # Public layout wrapper
│   │       └── track.php # Tracking page (form + results area)
│   └── Core/ # Core application components
│       ├── App.php # Application bootstrap/initialization
│       ├── Router.php # Handles URL routing
│       ├── Database.php # Database connection/wrapper
│       ├── Config.php # Loading configuration
│       ├── Session.php # Session management
│       └── View.php # Rendering views
├── vendor/ # Composer dependencies
├── tests/ # Test files (PHPUnit, etc.)
│   ├── Unit/
│   ├── Integration/
│   └── bootstrap.php # Test setup
├── debug/ # Debugging logs and tools
│   └── app.log # Main application log file
├── .env.example # Example environment file
├── composer.json # Composer configuration
├── .htaccess # Apache rewrite rules for routing (if applicable)
└── README.md # Project setup and usage instructions
```

**Core Components & Detailed Requirements:**

**1. Configuration & Environment:**

*   Use a `config/config.php` file (or environment variables via a `.env` file and loader library) for sensitive data (DB credentials) and environment-specific settings (base URL, base path, debug mode).
*   **Dynamic Paths:** Implement a mechanism (e.g., constants loaded from `config.php`) to generate all URLs and file paths dynamically based on the application's installation directory and URL. No hardcoded `/css/`, `/js/`, `/admin/`, `/track` etc. links or paths. This ensures the application works regardless of whether it's in the web root, a subdirectory, or a subdomain.

**2. Routing:**

*   Implement a routing system (either a simple custom one or a micro-framework's router) to map URLs (e.g., `/admin/shipments`, `/track`, `/api/track`) to specific PHP controller methods.
*   Use clean URLs (requires `.htaccess` for Apache or equivalent config for Nginx).

**3. Database Operations (Terminal Only for Schema):**

*   All database schema creation and modification must be done via SQL scripts located in the `database/migrate/` directory.
*   Include instructions in `README.md` on how to run these scripts using the terminal (e.g., `mysql -uuser -p database_name < database/migrate/vX.X_script_name.sql`).
*   PHP code will interact with the database *only* through the Models (`src/Models/`).

**4. Authentication & Authorization (Admin):**

*   Implement a secure login form at `/admin`.
*   Validate credentials against the `users` table (`password_hash`).
*   Use secure session management (`src/Core/Session.php`) to maintain login state.
*   Protect all `/admin/*` routes, redirecting unauthenticated users to the login page.
*   (Optional but recommended) Implement basic role checking if needed beyond just 'admin'.

**5. Admin Panel:**

*   **Layout (`src/Views/admin/layout.php`):** A consistent header, sidebar navigation (linking to Shipments, Settings), and footer using the chosen CSS framework.
*   **Shipment Management (`src/Controllers/Admin/ShipmentController.php`, `src/Models/Shipment.php`, etc.):**
    *   **List View (`src/Views/admin/shipments/list.php`):**
        *   Display data from `shipments`, `shipment_history` (latest status), and `users` tables in a responsive table.
        *   Pagination for large numbers of shipments.
        *   Server-side sorting based on column clicks.
        *   Filtering options (e.g., by status, origin, destination, assigned user).
        *   Action links/buttons (View, Edit, Delete) styled with framework classes.
    *   **Add/Edit Form (`src/Views/admin/shipments/form.php`):**
        *   A single, comprehensive form template used for both adding and editing.
        *   Uses the chosen CSS framework for form styling (grid layouts, form controls).
        *   **PHP:** Load necessary data (settings for dropdowns, existing shipment data for edit).
        *   **JavaScript:**
            *   Implement date and time pickers.
            *   Dynamic, repeatable sections for Shipment History and Packages. Admin can add/remove rows with JS.
            *   Auto-populate 'Updated By' field in history rows (maybe hidden, set server-side).
            *   Dynamically calculate and display Package totals (Volumetric Weight, Volume, Actual Weight) using JS based on inputs and settings (Dimension Divisor, Units from hidden fields or JS variables populated by PHP).
        *   **PHP (Server-side):** Handle form submission (POST requests). Validate all input data. Save/Update data across `shipments`, `shipment_history`, and `shipment_packages` tables. Implement transaction if saving multiple related records. Handle Tracking Number uniqueness/autogeneration based on settings.
*   **Settings Management (`src/Controllers/Admin/SettingsController.php`, `src/Models/Setting.php`, `src/Services/SettingsService.php`):**
    *   **Layout (`src/Views/admin/settings/layout.php`):** Tabbed interface for General, Packages, Email Settings.
    *   **Forms (`src/Views/admin/settings/*.php`):** HTML forms for all settings fields as detailed in the previous prompt (comma-separated inputs, checkboxes, text, numbers, file upload/URL for logo, color picker, multi-selects).
    *   **PHP:** Load/Save settings data from/to the `settings` table (handle data serialization/deserialization, e.g., JSON).
*   **Email Sending Trigger (PHP):**
    *   Within the Shipment update logic in `ShipmentService.php` (or similar), after successfully saving status history:
    *   Check settings for Client and Admin email activation and trigger statuses.
    *   If conditions met, use the `EmailService.php` to build and send emails.
    *   `EmailService.php`: Contains logic to fetch shipment data, perform merge tag replacement (`{tag}` to value) in subject, body, to, cc, bcc using shipment data. Uses PHPMailer to send the actual email, respecting configured "Domain Email" and potentially SMTP settings (if added).

**6. Frontend (Public Tracking):**

*   **Tracking Page (`public/index.php` routed to `src/Views/public/track.php`):**
    *   A clean public page layout (`src/Views/public/layout.php`) including the chosen CSS framework and public-facing JS/CSS.
    *   An HTML form with a single text input for `tracking_number` and a submit button. Style with the UI framework.
    *   A dedicated empty `div` or section (`id="tracking-results"`) below the form to display results.
*   **JavaScript (`public/js/script.js`):**
    *   Add event listener to the tracking form submission.
    *   Prevent default browser form submission.
    *   Get tracking number value.
    *   Display a loading indicator (styled with CSS framework).
    *   Perform an AJAX request (using `fetch` or `XMLHttpRequest`) to the public API endpoint (`/api/track`). Send the tracking number.
    *   On AJAX success: Hide loading indicator. Parse the JSON response. Dynamically generate and inject HTML into the `#tracking-results` div to display the shipment details. Clear previous results first.
    *   On AJAX error or "not found" response: Hide loading indicator. Display a "Shipment not found" message (styled).
*   **PHP API Endpoint (`public/index.php` routed to `src/Controllers/Api/TrackingApiController.php`):**
    *   Receive GET or POST request with tracking number.
    *   Query `shipments` table by `tracking_number`.
    *   If found, query `shipment_history` (ordered by date/time) and `shipment_packages` (if enabled in settings for results) for that shipment ID.
    *   Fetch relevant settings (Logo URL, Barcode enabled, Package display enabled, Units, Divisor).
    *   Generate barcode data/URL if enabled using `BarcodeService.php`.
    *   Combine all fetched data (shipment details, history, packages, settings info) into a structured PHP array.
    *   Encode the array as JSON and return it with appropriate headers (`Content-Type: application/json`).
    *   If not found, return a JSON response indicating failure or "not found" status (e.g., `{"status": "error", "message": "Shipment not found."}`).
*   **Highly Detailed Tracking Results UI (`public/js/script.js` output, styled by CSS framework):**
    *   Leverage the chosen CSS framework for layout and components.
    *   **Key Info Header:** Large Tracking Number, current Status (styled prominently, perhaps with a color based on status - e.g., green for delivered, red for cancelled), Logo (image tag), Barcode (image tag or SVG generated by JS/PHP).
    *   **Shipment Details Sections:** Use panels, cards, or grid layout (framework components) to display Shipper, Receiver, Origin, Destination, Dates, Weight, Type, etc. Clearly labeled.
    *   **Shipment History Timeline:** **Implement this visually.**
        *   Use CSS framework components (e.g., list group, steps, timeline components if available or custom CSS) to create a vertical timeline.
        *   Each history entry is a point on the timeline.
        *   Use **icons** next to each point representing the status type (e.g., box for picked up, truck for in transit, house for delivered - simple FontAwesome or similar).
        *   Display Date, Time, Location, Status text, and Remarks for each point.
        *   Visualize "movement": The chronological list itself represents movement. The timeline style and potentially lines connecting points reinforce this. A simple progress bar at the top indicating percentage complete based on history count vs. typical steps is optional but adds to the "movement" feel.
    *   **Packages Section (Conditional):** If "Enable Multiple Package on Results" setting is true:
        *   Display a section with a table or list of package details (Qty, Type, Description, Dimensions, Weight).
        *   Display the calculated totals (Volumetric Weight, Volume, Actual Weight) clearly below the list.
    *   Ensure the entire output is responsive.

**7. Debugging Tools & Logging:**

*   Implement a debug mode toggle in `config/config.php`.
*   When debug mode is true:
    *   Display detailed PHP errors on screen (disable in production).
    *   Log errors, warnings, and potentially key application events (like email sending attempts, DB query errors) to a file in the `debug/` directory (`app.log`).
    *   Use a simple logging function (`log_message($level, $message)`) throughout the application.
*   Include instructions in `README.md` on how to enable debug mode and locate the log file.

**8. Testing:**

*   Set up a testing environment using PHPUnit (`tests/bootstrap.php`, `phpunit.xml`).
*   Write **Unit Tests** (`tests/Unit/`) for individual classes/methods (e.g., `SettingsService` logic, `BarcodeService` generation, merge tag replacement).
*   Write **Integration Tests** (`tests/Integration/`) to test interactions between components (e.g., saving a shipment and verifying history entries are created, testing the API endpoint response).
*   All test files must reside in the `tests/` directory.
*   Include instructions on how to run the test suite from the terminal.

**Technical Requirements (Reinforced):**

*   **Code Style:** Adhere to PSR standards (PSR-1, PSR-4 for autoloading, PSR-12). Use a code sniffer.
*   **Security:**
    *   Prevent SQL Injection (Prepared Statements via PDO).
    *   Sanitize and validate all user inputs (GET, POST, URL parameters).
    *   Escape all output displayed in HTML (`htmlspecialchars` or similar).
    *   Implement CSRF protection for all admin forms.
    *   Secure session management (regenerate ID on login, http-only cookies).
    *   Hash passwords securely (e.g., `password_hash()`).
    *   Limit access to `src/`, `config/`, `database/`, `tests/`, `vendor/`, `debug/` directories from the web (achieved by setting `public/` as the document root).
    *   Basic rate limiting on the `/api/track` endpoint to prevent abuse.
*   **Error Handling:** Implement try-catch blocks for potential errors (DB errors, file operations, etc.). Log errors gracefully.
*   **Performance:** Optimize database queries, especially for the public tracking endpoint. Lazy loading where appropriate.
*   **Code Organization:** Use OOP principles, separation of concerns (MVC-like structure: Controller <-> Service <-> Model).
*   **Composer:** Properly define dependencies in `composer.json` and use Composer autoloading.

**Deliverables:**

*   Complete application source code matching the specified file structure.
*   SQL script(s) in `database/migrate/` to create the database schema.
*   Detailed `README.md` covering:
    *   System requirements (PHP version, extensions).
    *   Installation steps (cloning, Composer install, environment config, database setup/migration via terminal).
    *   Instructions for running tests.
    *   Instructions for enabling/viewing debug logs.
    *   Admin panel access URL and initial user setup.
    *   Public tracking page URL.
    *   Brief overview of settings.
*   `composer.json` file.
*   Example environment/config file (`.env.example` or `config.example.php`).

---

This prompt breaks down the project into granular requirements, specifies the technologies, structure, database, UI details, and includes crucial development practices like dynamic paths, terminal DB ops, debugging, and testing, all tailored to a standalone web application context with distinct admin and public user experiences.