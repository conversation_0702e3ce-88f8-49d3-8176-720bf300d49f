<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

try {
    $pdo = new PDO('mysql:host=127.0.0.1;dbname=shipment', 'root', 'root');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    if ($_SERVER['REQUEST_METHOD'] === 'GET') {
        // Get document requests for a specific shipment (admin view)
        $shipment_id = $_GET['shipment_id'] ?? '';
        
        if (empty($shipment_id)) {
            echo json_encode(['success' => false, 'message' => 'Shipment ID is required']);
            exit;
        }
        
        // Get document requests for this shipment with additional admin info
        $stmt = $pdo->prepare('
            SELECT 
                dr.id,
                dr.shipment_id,
                dr.document_type_id,
                dr.request_message,
                dr.priority,
                dr.due_date,
                dr.status,
                dr.created_at,
                dr.updated_at,
                dt.name as document_type_name,
                dt.description as document_type_description,
                u.name as requested_by_name,
                du.id as uploaded_document_id,
                du.original_filename,
                du.file_path,
                du.uploaded_at,
                da.status as approval_status,
                da.review_notes,
                da.reviewed_at,
                reviewer.name as reviewed_by_name
            FROM document_requests dr
            JOIN document_types dt ON dr.document_type_id = dt.id
            JOIN users u ON dr.requested_by = u.id
            LEFT JOIN document_uploads du ON dr.id = du.document_request_id
            LEFT JOIN document_approvals da ON dr.id = da.document_request_id
            LEFT JOIN users reviewer ON da.reviewed_by = reviewer.id
            WHERE dr.shipment_id = ?
            ORDER BY dr.created_at DESC
        ');
        
        $stmt->execute([$shipment_id]);
        $requests = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // Format dates and group uploaded documents
        $processedRequests = [];
        foreach ($requests as $request) {
            $requestId = $request['id'];
            
            if (!isset($processedRequests[$requestId])) {
                $processedRequests[$requestId] = [
                    'id' => $request['id'],
                    'shipment_id' => $request['shipment_id'],
                    'document_type_id' => $request['document_type_id'],
                    'document_type_name' => $request['document_type_name'],
                    'document_type_description' => $request['document_type_description'],
                    'request_message' => $request['request_message'],
                    'priority' => $request['priority'],
                    'due_date' => $request['due_date'],
                    'status' => $request['status'],
                    'created_at' => $request['created_at'],
                    'updated_at' => $request['updated_at'],
                    'requested_by_name' => $request['requested_by_name'],
                    'created_at_formatted' => date('M j, Y g:i A', strtotime($request['created_at'])),
                    'uploaded_documents' => [],
                    'approval_info' => null
                ];
                
                if ($request['due_date']) {
                    $processedRequests[$requestId]['due_date_formatted'] = date('M j, Y g:i A', strtotime($request['due_date']));
                    $processedRequests[$requestId]['is_overdue'] = strtotime($request['due_date']) < time();
                }
            }
            
            // Add uploaded document if exists
            if ($request['uploaded_document_id']) {
                $processedRequests[$requestId]['uploaded_documents'][] = [
                    'id' => $request['uploaded_document_id'],
                    'original_filename' => $request['original_filename'],
                    'file_path' => $request['file_path'],
                    'uploaded_at' => $request['uploaded_at'],
                    'uploaded_at_formatted' => date('M j, Y g:i A', strtotime($request['uploaded_at']))
                ];
                
                // Set the latest uploaded document as the main one
                $processedRequests[$requestId]['uploaded_document'] = [
                    'id' => $request['uploaded_document_id'],
                    'original_filename' => $request['original_filename'],
                    'file_path' => $request['file_path'],
                    'uploaded_at' => $request['uploaded_at'],
                    'uploaded_at_formatted' => date('M j, Y g:i A', strtotime($request['uploaded_at']))
                ];
            }
            
            // Add approval info if exists
            if ($request['approval_status']) {
                $processedRequests[$requestId]['approval_info'] = [
                    'status' => $request['approval_status'],
                    'review_notes' => $request['review_notes'],
                    'reviewed_at' => $request['reviewed_at'],
                    'reviewed_by_name' => $request['reviewed_by_name'],
                    'reviewed_at_formatted' => date('M j, Y g:i A', strtotime($request['reviewed_at']))
                ];
            }
        }
        
        echo json_encode([
            'success' => true,
            'requests' => array_values($processedRequests),
            'shipment_id' => $shipment_id
        ]);
        
    } else {
        echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    }
    
} catch (PDOException $e) {
    error_log("Database error in admin-document-requests.php: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'Database error occurred']);
} catch (Exception $e) {
    error_log("General error in admin-document-requests.php: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'An error occurred']);
}
?>
