<?php
// Define BASE_PATH if not already defined
if (!defined('BASE_PATH')) {
    define('BASE_PATH', dirname(__DIR__, 4));
}

// Register Composer autoloader
require_once BASE_PATH . '/vendor/autoload.php';

use App\Core\App;
use App\Core\Database;
use App\Core\Session;

// Initialize the application
$app = App::getInstance();

// Set JSON header
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// Check if user is authenticated as admin
if (!Session::has('admin_user_id')) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Unauthorized']);
    exit;
}

try {
    if ($_SERVER['REQUEST_METHOD'] === 'GET') {
        $limit = $_GET['limit'] ?? 5;
        
        // For now, return mock data since we don't have an invoices table yet
        // In a real implementation, you would query an invoices table
        $invoices = [
            [
                'id' => 1,
                'invoice_number' => 'INV-2024-000001',
                'shipment_id' => 6,
                'tracking_number' => 'TSD250423273119',
                'client_name' => 'John Doe',
                'total_amount' => 125.50,
                'status' => 'Paid',
                'created_at' => date('Y-m-d H:i:s', strtotime('-1 day')),
                'due_date' => date('Y-m-d', strtotime('+29 days'))
            ],
            [
                'id' => 2,
                'invoice_number' => 'INV-2024-000002',
                'shipment_id' => 3,
                'tracking_number' => 'TSD250422999001',
                'client_name' => 'Jane Smith',
                'total_amount' => 89.75,
                'status' => 'Pending',
                'created_at' => date('Y-m-d H:i:s', strtotime('-3 hours')),
                'due_date' => date('Y-m-d', strtotime('+27 days'))
            ],
            [
                'id' => 3,
                'invoice_number' => 'INV-2024-000003',
                'shipment_id' => 5,
                'tracking_number' => 'TSD250423000123',
                'client_name' => 'ABC Corporation',
                'total_amount' => 245.00,
                'status' => 'Overdue',
                'created_at' => date('Y-m-d H:i:s', strtotime('-5 days')),
                'due_date' => date('Y-m-d', strtotime('-5 days'))
            ],
            [
                'id' => 4,
                'invoice_number' => 'INV-2024-000004',
                'shipment_id' => 7,
                'tracking_number' => 'TSD250423456789',
                'client_name' => 'XYZ Logistics',
                'total_amount' => 156.25,
                'status' => 'Paid',
                'created_at' => date('Y-m-d H:i:s', strtotime('-1 week')),
                'due_date' => date('Y-m-d', strtotime('-7 days'))
            ],
            [
                'id' => 5,
                'invoice_number' => 'INV-2024-000005',
                'shipment_id' => 2,
                'tracking_number' => 'TSD250422111222',
                'client_name' => 'Global Shipping Inc',
                'total_amount' => 312.80,
                'status' => 'Pending',
                'created_at' => date('Y-m-d H:i:s', strtotime('-2 weeks')),
                'due_date' => date('Y-m-d', strtotime('+14 days'))
            ]
        ];
        
        // Limit the results
        $invoices = array_slice($invoices, 0, intval($limit));
        
        echo json_encode([
            'success' => true,
            'invoices' => $invoices,
            'total_count' => count($invoices)
        ]);
        
    } else {
        http_response_code(405);
        echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    }
    
} catch (Exception $e) {
    error_log("Error in recent invoices API: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Server error occurred']);
}
?>
