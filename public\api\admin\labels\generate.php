<?php
// Define BASE_PATH if not already defined
if (!defined('BASE_PATH')) {
    define('BASE_PATH', dirname(__DIR__, 4));
}

// Register Composer autoloader
require_once BASE_PATH . '/vendor/autoload.php';

use App\Core\App;
use App\Core\Database;
use App\Core\Session;
use App\Core\View;

// Initialize the application
$app = App::getInstance();

// Set appropriate headers
if (isset($_POST['download']) && $_POST['download'] === '1') {
    header('Content-Type: application/pdf');
    header('Content-Disposition: attachment; filename="shipping-label.pdf"');
} else {
    header('Content-Type: application/json');
}

header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// Check if user is authenticated as admin
if (!Session::has('admin_user_id')) {
    http_response_code(401);
    if (!isset($_POST['download'])) {
        echo json_encode(['success' => false, 'message' => 'Unauthorized']);
    }
    exit;
}

try {
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        // Get form data
        $shipment_id = $_POST['shipment_id'] ?? null;
        $label_type = $_POST['label_type'] ?? 'standard';
        $label_size = $_POST['label_size'] ?? '4x6';
        $include_logo = isset($_POST['include_logo']) ? true : false;
        $include_barcode = isset($_POST['include_barcode']) ? true : false;
        $include_return_address = isset($_POST['include_return_address']) ? true : false;
        
        if (!$shipment_id) {
            if (isset($_POST['download'])) {
                http_response_code(400);
                echo "Shipment ID is required";
                exit;
            } else {
                echo json_encode(['success' => false, 'message' => 'Shipment ID is required']);
                exit;
            }
        }
        
        // Get shipment data
        Database::prepare("SELECT * FROM shipments WHERE id = :id");
        Database::bindValue(':id', $shipment_id, PDO::PARAM_INT);
        Database::execute();
        $shipment = Database::fetch();
        
        if (!$shipment) {
            if (isset($_POST['download'])) {
                http_response_code(404);
                echo "Shipment not found";
                exit;
            } else {
                echo json_encode(['success' => false, 'message' => 'Shipment not found']);
                exit;
            }
        }
        
        // Generate label HTML
        $labelHtml = generateLabelHtml($shipment, [
            'label_type' => $label_type,
            'label_size' => $label_size,
            'include_logo' => $include_logo,
            'include_barcode' => $include_barcode,
            'include_return_address' => $include_return_address
        ]);
        
        if (isset($_POST['download']) && $_POST['download'] === '1') {
            // Generate PDF and return as download
            $pdf = generateLabelPdf($labelHtml, $shipment);
            echo $pdf;
        } else {
            // Return HTML for preview
            echo json_encode([
                'success' => true,
                'html' => $labelHtml,
                'shipment_id' => $shipment_id,
                'tracking_number' => $shipment['tracking_number']
            ]);
        }
        
    } else {
        http_response_code(405);
        if (!isset($_POST['download'])) {
            echo json_encode(['success' => false, 'message' => 'Method not allowed']);
        }
    }
    
} catch (Exception $e) {
    error_log("Error in label generation API: " . $e->getMessage());
    http_response_code(500);
    if (isset($_POST['download'])) {
        echo "Error generating label: " . $e->getMessage();
    } else {
        echo json_encode(['success' => false, 'message' => 'Error generating label: ' . $e->getMessage()]);
    }
}

function generateLabelHtml($shipment, $options) {
    // Get current date and time
    $currentDate = date('Y-m-d');
    $currentTime = date('H:i');

    // Get company logo from settings
    $logoHtml = '';
    try {
        Database::prepare("SELECT setting_value FROM settings WHERE category = 'appearance' AND setting_key = 'company_logo'");
        Database::execute();
        $logoResult = Database::fetch();

        if ($logoResult && !empty($logoResult['setting_value'])) {
            $logoPath = $logoResult['setting_value'];
            $logoUrl = '/uploads/logos/' . $logoPath;
            $logoHtml = '<img src="' . $logoUrl . '" alt="Company Logo" style="max-height: 50px; max-width: 150px; margin-bottom: 5px;">';
        }
    } catch (Exception $e) {
        // Fallback to text logo
    }

    $html = '
    <div class="shipping-label" style="width: 600px; min-height: 800px; border: 2px solid #000; padding: 0; font-family: Arial, sans-serif; background: white; margin: 0 auto;">
        <!-- Header Section -->
        <div class="label-header" style="background: #f8f9fa; padding: 15px 20px; border-bottom: 2px solid #000; display: flex; justify-content: space-between; align-items: center;">
            <div class="company-info">
                ' . ($logoHtml ? $logoHtml : '
                <div class="logo" style="font-size: 28px; font-weight: bold; color: #e74c3c; margin-bottom: 5px;">
                    <span style="background: #e74c3c; color: white; padding: 5px 10px; border-radius: 3px;">ELTA</span>
                    <span style="color: #2c3e50; margin-left: 5px;">COURIER</span>
                </div>
                ') . '
                <div style="font-size: 12px; color: #666;">The Bridge To Your Logistics Success</div>
            </div>
            ' . ($options['include_barcode'] ? '
            <div class="barcode-section" style="text-align: center;">
                <div class="barcode-container" style="margin: 10px 0;">
                    <svg id="label-barcode-' . $shipment['id'] . '" style="border: 1px solid #ddd; padding: 5px; background: white;"></svg>
                </div>
                <div class="tracking-number" style="font-size: 14px; font-weight: bold; margin-top: 5px;">
                    ' . htmlspecialchars($shipment['tracking_number']) . '
                </div>
            </div>
            <script>
                // Generate barcode when label is displayed
                if (typeof JsBarcode !== "undefined") {
                    JsBarcode("#label-barcode-' . $shipment['id'] . '", "' . htmlspecialchars($shipment['tracking_number']) . '", {
                        format: "CODE128",
                        lineColor: "#000",
                        width: 2,
                        height: 50,
                        displayValue: false
                    });
                } else {
                    // Fallback if JsBarcode is not loaded
                    document.getElementById("label-barcode-' . $shipment['id'] . '").innerHTML =
                        "<text x=\"50%\" y=\"50%\" text-anchor=\"middle\" dy=\".3em\" style=\"font-family: monospace; font-size: 12px;\">' . htmlspecialchars($shipment['tracking_number']) . '</text>";
                }
            </script>
            ' : '') . '
        </div>

        <!-- Shipper and Receiver Details Section -->
        <div class="addresses-section" style="display: flex; border-bottom: 1px solid #000;">
            <!-- Shipper Details -->
            <div class="shipper-section" style="width: 50%; padding: 20px; border-right: 1px solid #000;">
                <div class="section-title" style="background: #2c3e50; color: white; padding: 8px 12px; margin: -20px -20px 15px -20px; font-weight: bold; font-size: 14px;">
                    SHIPPER DETAILS
                </div>
                <div class="shipper-info" style="font-size: 12px; line-height: 1.6;">
                    <div style="margin-bottom: 8px;"><strong>Shipper Name:</strong> ' . htmlspecialchars($shipment['shipper_name'] ?? 'ELTA Courier Services') . '</div>
                    <div style="margin-bottom: 8px;"><strong>Phone Number:</strong> ' . htmlspecialchars($shipment['shipper_phone'] ?? '+30 210 1234567') . '</div>
                    <div style="margin-bottom: 8px;"><strong>Address:</strong></div>
                    <div style="margin-left: 10px; margin-bottom: 8px;">' . nl2br(htmlspecialchars($shipment['shipper_address'] ?? 'Athens, Greece<br>Postal Code: 10431')) . '</div>
                    <div style="margin-bottom: 8px;"><strong>Email:</strong> ' . htmlspecialchars($shipment['shipper_email'] ?? '<EMAIL>') . '</div>
                </div>
            </div>

            <!-- Receiver Details -->
            <div class="receiver-section" style="width: 50%; padding: 20px;">
                <div class="section-title" style="background: #e74c3c; color: white; padding: 8px 12px; margin: -20px -20px 15px -20px; font-weight: bold; font-size: 14px;">
                    RECEIVER DETAILS
                </div>
                <div class="receiver-info" style="font-size: 12px; line-height: 1.6;">
                    <div style="margin-bottom: 8px;"><strong>Receiver Name:</strong> ' . htmlspecialchars($shipment['receiver_name'] ?? 'N/A') . '</div>
                    <div style="margin-bottom: 8px;"><strong>Phone Number:</strong> ' . htmlspecialchars($shipment['receiver_phone'] ?? 'N/A') . '</div>
                    <div style="margin-bottom: 8px;"><strong>Address:</strong></div>
                    <div style="margin-left: 10px; margin-bottom: 8px;">' . nl2br(htmlspecialchars($shipment['receiver_address'] ?? 'N/A')) . '</div>
                    <div style="margin-bottom: 8px;"><strong>Email:</strong> ' . htmlspecialchars($shipment['receiver_email'] ?? 'N/A') . '</div>
                </div>
            </div>
        </div>

        <!-- Package Details Section -->
        <div class="package-section" style="padding: 20px; border-bottom: 1px solid #000;">
            <div class="section-title" style="background: #27ae60; color: white; padding: 8px 12px; margin: -20px -20px 15px -20px; font-weight: bold; font-size: 14px;">
                PACKAGE DETAILS
            </div>
            <table style="width: 100%; border-collapse: collapse; font-size: 11px;">
                <thead>
                    <tr style="background: #f8f9fa;">
                        <th style="border: 1px solid #ddd; padding: 8px; text-align: left;">Qty</th>
                        <th style="border: 1px solid #ddd; padding: 8px; text-align: left;">Piece Type</th>
                        <th style="border: 1px solid #ddd; padding: 8px; text-align: left;">Description</th>
                        <th style="border: 1px solid #ddd; padding: 8px; text-align: left;">Length(cm)</th>
                        <th style="border: 1px solid #ddd; padding: 8px; text-align: left;">Width(cm)</th>
                        <th style="border: 1px solid #ddd; padding: 8px; text-align: left;">Height(cm)</th>
                        <th style="border: 1px solid #ddd; padding: 8px; text-align: left;">Weight (kg)</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td style="border: 1px solid #ddd; padding: 8px;">1</td>
                        <td style="border: 1px solid #ddd; padding: 8px;">Parcel</td>
                        <td style="border: 1px solid #ddd; padding: 8px;">' . htmlspecialchars($shipment['items'] ?? 'General Merchandise') . '</td>
                        <td style="border: 1px solid #ddd; padding: 8px;">-</td>
                        <td style="border: 1px solid #ddd; padding: 8px;">-</td>
                        <td style="border: 1px solid #ddd; padding: 8px;">-</td>
                        <td style="border: 1px solid #ddd; padding: 8px;">' . htmlspecialchars($shipment['total_weight'] ?? '1.0') . '</td>
                    </tr>
                </tbody>
            </table>
            <div style="margin-top: 15px; display: flex; justify-content: space-between; font-size: 12px;">
                <div><strong>Total Volumetric Weight:</strong> ' . htmlspecialchars($shipment['total_weight'] ?? '1.0') . ' kg</div>
                <div><strong>Total Volume:</strong> 0.001 cu. m.</div>
                <div><strong>Total Actual Weight:</strong> ' . htmlspecialchars($shipment['total_weight'] ?? '1.0') . ' kg</div>
            </div>
        </div>

        <!-- Service Information -->
        <div class="service-section" style="padding: 15px 20px; background: #f8f9fa; font-size: 12px;">
            <div style="display: flex; justify-content: space-between; margin-bottom: 10px;">
                <div><strong>Pickup Date:</strong> ' . $currentDate . '</div>
                <div><strong>Pickup Time:</strong> ' . $currentTime . '</div>
                <div><strong>Delivery Date:</strong> ' . ($shipment['expected_delivery_date'] ? date('Y-m-d', strtotime($shipment['expected_delivery_date'])) : date('Y-m-d', strtotime('+2 days'))) . '</div>
            </div>
            <div style="display: flex; justify-content: space-between; margin-bottom: 10px;">
                <div><strong>Origin:</strong> ' . htmlspecialchars($shipment['origin'] ?? 'Athens, Greece') . '</div>
                <div><strong>Destination:</strong> ' . htmlspecialchars($shipment['destination'] ?? 'N/A') . '</div>
                <div><strong>Service:</strong> ELTA COURIER SERVICE</div>
            </div>
            <div style="display: flex; justify-content: space-between;">
                <div><strong>Carrier:</strong> ELTA</div>
                <div><strong>Carrier Reference No:</strong> ' . htmlspecialchars($shipment['tracking_number']) . '</div>
                <div><strong>Departure Time:</strong> ' . date('H:i', strtotime('+1 hour')) . '</div>
            </div>
        </div>

        <!-- Footer -->
        <div class="footer-section" style="padding: 10px 20px; border-top: 1px solid #000; font-size: 10px; color: #666; text-align: center;">
            <div>Thank you for choosing ELTA Courier - Version 6.5.1</div>
        </div>
    </div>';

    return $html;
}

function generateLabelPdf($html, $shipment) {
    // For now, return a simple PDF placeholder
    // In a real implementation, you would use a library like TCPDF or DomPDF
    $pdf_content = "%PDF-1.4
1 0 obj
<<
/Type /Catalog
/Pages 2 0 R
>>
endobj

2 0 obj
<<
/Type /Pages
/Kids [3 0 R]
/Count 1
>>
endobj

3 0 obj
<<
/Type /Page
/Parent 2 0 R
/MediaBox [0 0 612 792]
/Contents 4 0 R
>>
endobj

4 0 obj
<<
/Length 44
>>
stream
BT
/F1 12 Tf
72 720 Td
(Shipping Label - " . $shipment['tracking_number'] . ") Tj
ET
endstream
endobj

xref
0 5
0000000000 65535 f 
0000000009 00000 n 
0000000058 00000 n 
0000000115 00000 n 
0000000206 00000 n 
trailer
<<
/Size 5
/Root 1 0 R
>>
startxref
300
%%EOF";
    
    return $pdf_content;
}
?>
