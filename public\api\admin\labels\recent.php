<?php
// Define BASE_PATH if not already defined
if (!defined('BASE_PATH')) {
    define('BASE_PATH', dirname(__DIR__, 4));
}

// Register Composer autoloader
require_once BASE_PATH . '/vendor/autoload.php';

use App\Core\App;
use App\Core\Database;
use App\Core\Session;

// Initialize the application
$app = App::getInstance();

// Set JSON header
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// Check if user is authenticated as admin
if (!Session::has('admin_user_id')) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Unauthorized']);
    exit;
}

try {
    if ($_SERVER['REQUEST_METHOD'] === 'GET') {
        $shipment_id = $_GET['shipment_id'] ?? null;
        
        if (!$shipment_id) {
            echo json_encode(['success' => false, 'message' => 'Shipment ID is required']);
            exit;
        }
        
        // For now, return mock data since we don't have a labels table yet
        // In a real implementation, you would query a labels table
        $labels = [
            [
                'id' => 1,
                'shipment_id' => $shipment_id,
                'label_type' => 'Standard Shipping Label',
                'format' => 'PDF',
                'file_path' => '/storage/labels/label_' . $shipment_id . '_1.pdf',
                'created_at' => date('Y-m-d H:i:s', strtotime('-2 hours')),
                'created_by' => Session::get('admin_user_id')
            ],
            [
                'id' => 2,
                'shipment_id' => $shipment_id,
                'label_type' => 'Express Label',
                'format' => 'PDF',
                'file_path' => '/storage/labels/label_' . $shipment_id . '_2.pdf',
                'created_at' => date('Y-m-d H:i:s', strtotime('-1 day')),
                'created_by' => Session::get('admin_user_id')
            ]
        ];
        
        echo json_encode([
            'success' => true,
            'labels' => $labels,
            'shipment_id' => $shipment_id
        ]);
        
    } else {
        http_response_code(405);
        echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    }
    
} catch (Exception $e) {
    error_log("Error in recent labels API: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Server error occurred']);
}
?>
