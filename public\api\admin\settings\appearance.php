<?php
require_once __DIR__ . '/../../../config/app.php';

use App\Core\Database;
use App\Core\Session;

header('Content-Type: application/json');

// Check if user is authenticated
if (!Session::get('user_id') || Session::get('user_role') !== 'admin') {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Unauthorized']);
    exit;
}

try {
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        $primaryColor = $_POST['primary_color'] ?? '#2c3e50';
        $secondaryColor = $_POST['secondary_color'] ?? '#e74c3c';
        $accentColor = $_POST['accent_color'] ?? '#3498db';

        // Handle logo upload
        $logoPath = '';
        if (isset($_FILES['company_logo']) && $_FILES['company_logo']['error'] === UPLOAD_ERR_OK) {
            $logoPath = handleLogoUpload($_FILES['company_logo']);
            if (!$logoPath) {
                echo json_encode(['success' => false, 'message' => 'Error uploading logo']);
                exit;
            }
        }
        
        // Validate hex colors
        if (!preg_match('/^#[a-f0-9]{6}$/i', $primaryColor) ||
            !preg_match('/^#[a-f0-9]{6}$/i', $secondaryColor) ||
            !preg_match('/^#[a-f0-9]{6}$/i', $accentColor)) {
            echo json_encode(['success' => false, 'message' => 'Invalid color format']);
            exit;
        }
        
        // Update or insert appearance settings
        $settings = [
            'primary_color' => $primaryColor,
            'secondary_color' => $secondaryColor,
            'accent_color' => $accentColor
        ];

        // Add logo path if uploaded
        if ($logoPath) {
            $settings['company_logo'] = $logoPath;
        }
        
        foreach ($settings as $key => $value) {
            // Check if setting exists
            Database::prepare("SELECT id FROM settings WHERE category = 'appearance' AND setting_key = :key");
            Database::bindValue(':key', $key);
            Database::execute();
            $existing = Database::fetch();
            
            if ($existing) {
                // Update existing setting
                Database::prepare("UPDATE settings SET setting_value = :value WHERE category = 'appearance' AND setting_key = :key");
                Database::bindValue(':value', $value);
                Database::bindValue(':key', $key);
                Database::execute();
            } else {
                // Insert new setting
                Database::prepare("INSERT INTO settings (category, setting_key, setting_value, description) VALUES ('appearance', :key, :value, :description)");
                Database::bindValue(':key', $key);
                Database::bindValue(':value', $value);
                Database::bindValue(':description', ucfirst(str_replace('_', ' ', $key)) . ' for the admin interface');
                Database::execute();
            }
        }
        
        // Generate CSS file with new colors
        generateCustomCSS($settings);
        
        echo json_encode([
            'success' => true,
            'message' => 'Appearance settings saved successfully',
            'settings' => $settings
        ]);
        
    } else {
        http_response_code(405);
        echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    }
    
} catch (Exception $e) {
    error_log("Error in appearance settings API: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Error saving settings: ' . $e->getMessage()]);
}

function generateCustomCSS($settings) {
    $css = "/* Auto-generated custom theme CSS */
:root {
    --primary-color: {$settings['primary_color']};
    --secondary-color: {$settings['secondary_color']};
    --accent-color: {$settings['accent_color']};
    --primary-rgb: " . hexToRgb($settings['primary_color']) . ";
    --secondary-rgb: " . hexToRgb($settings['secondary_color']) . ";
    --accent-rgb: " . hexToRgb($settings['accent_color']) . ";
}

/* Header and Navigation */
.navbar-brand,
.page-header h1,
.card-header {
    color: var(--primary-color) !important;
}

.navbar {
    background-color: var(--primary-color) !important;
}

/* Buttons */
.btn-primary {
    background-color: var(--secondary-color) !important;
    border-color: var(--secondary-color) !important;
}

.btn-primary:hover {
    background-color: rgba(var(--secondary-rgb), 0.8) !important;
    border-color: rgba(var(--secondary-rgb), 0.8) !important;
}

/* Links and Accents */
a {
    color: var(--accent-color) !important;
}

a:hover {
    color: rgba(var(--accent-rgb), 0.8) !important;
}

/* Status badges and highlights */
.badge-primary {
    background-color: var(--accent-color) !important;
}

/* Invoice and Label Branding */
.invoice .logo,
.shipping-label .logo {
    color: var(--primary-color) !important;
}

.invoice .section-title,
.shipping-label .section-title {
    background-color: var(--primary-color) !important;
}

/* Form elements */
.form-control:focus {
    border-color: var(--accent-color) !important;
    box-shadow: 0 0 0 0.2rem rgba(var(--accent-rgb), 0.25) !important;
}

/* Tables */
.table thead th {
    background-color: var(--primary-color) !important;
    color: white !important;
}

/* Cards */
.card-header {
    background-color: rgba(var(--primary-rgb), 0.1) !important;
    border-bottom-color: var(--primary-color) !important;
}

/* Sidebar */
.sidebar {
    background-color: var(--primary-color) !important;
}

.sidebar .nav-link {
    color: rgba(255, 255, 255, 0.8) !important;
}

.sidebar .nav-link:hover,
.sidebar .nav-link.active {
    color: white !important;
    background-color: rgba(var(--secondary-rgb), 0.2) !important;
}
";

    // Save CSS to file
    $cssPath = __DIR__ . '/../../../css/custom-theme.css';
    file_put_contents($cssPath, $css);
}

function hexToRgb($hex) {
    $hex = ltrim($hex, '#');
    $r = hexdec(substr($hex, 0, 2));
    $g = hexdec(substr($hex, 2, 2));
    $b = hexdec(substr($hex, 4, 2));
    return "$r, $g, $b";
}

function handleLogoUpload($file) {
    // Validate file type
    $allowedTypes = ['image/jpeg', 'image/png', 'image/svg+xml', 'image/gif'];
    if (!in_array($file['type'], $allowedTypes)) {
        return false;
    }

    // Validate file size (2MB)
    if ($file['size'] > 2 * 1024 * 1024) {
        return false;
    }

    // Create uploads directory if it doesn't exist
    $uploadDir = __DIR__ . '/../../../uploads/logos/';
    if (!is_dir($uploadDir)) {
        mkdir($uploadDir, 0755, true);
    }

    // Generate unique filename
    $extension = pathinfo($file['name'], PATHINFO_EXTENSION);
    $filename = 'logo_' . time() . '_' . uniqid() . '.' . $extension;
    $filepath = $uploadDir . $filename;

    // Move uploaded file
    if (move_uploaded_file($file['tmp_name'], $filepath)) {
        return $filename;
    }

    return false;
}
?>
