<?php
require_once __DIR__ . '/../../../config/app.php';

use App\Core\Database;
use App\Core\Session;

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// For development, skip authentication check
// TODO: Re-enable authentication in production
/*
if (!Session::get('user_id') || Session::get('user_role') !== 'admin') {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Unauthorized']);
    exit;
}
*/

try {
    if ($_SERVER['REQUEST_METHOD'] === 'GET') {
        $shipmentId = $_GET['id'] ?? '';
        
        if (empty($shipmentId) || !is_numeric($shipmentId)) {
            echo json_encode(['success' => false, 'message' => 'Valid shipment ID is required']);
            exit;
        }
        
        // Get shipment details (simplified query to avoid JOIN issues)
        $sql = "SELECT * FROM shipments WHERE id = :id";

        Database::prepare($sql);
        Database::bindValue(':id', $shipmentId, PDO::PARAM_INT);
        Database::execute();

        $shipment = Database::fetch();

        // Get client name separately if needed
        $clientName = '';
        if ($shipment && !empty($shipment['client_id'])) {
            try {
                Database::prepare("SELECT name FROM clients WHERE id = :client_id");
                Database::bindValue(':client_id', $shipment['client_id'], PDO::PARAM_INT);
                Database::execute();
                $client = Database::fetch();
                $clientName = $client ? $client['name'] : '';
            } catch (Exception $e) {
                // Client table might not exist, continue without it
                $clientName = $shipment['shipper_name'] ?? '';
            }
        }
        
        if (!$shipment) {
            echo json_encode(['success' => false, 'message' => 'Shipment not found']);
            exit;
        }
        
        // Format the shipment data
        $formattedShipment = [
            'id' => $shipment['id'],
            'tracking_number' => $shipment['tracking_number'],
            'client_name' => $clientName ?: ($shipment['shipper_name'] ?? 'N/A'),
            'employee_name' => $shipment['employee_name'] ?? 'N/A',
            'shipper_name' => $shipment['shipper_name'],
            'shipper_address' => $shipment['shipper_address'],
            'shipper_phone' => $shipment['shipper_phone'],
            'shipper_email' => $shipment['shipper_email'],
            'receiver_name' => $shipment['receiver_name'],
            'receiver_address' => $shipment['receiver_address'],
            'receiver_phone' => $shipment['receiver_phone'],
            'receiver_email' => $shipment['receiver_email'],
            'origin' => $shipment['origin'],
            'destination' => $shipment['destination'],
            'status' => $shipment['status'],
            'shipment_type' => $shipment['shipment_type'],
            'shipment_mode' => $shipment['shipment_mode'],
            'carrier' => $shipment['carrier'],
            'payment_mode' => $shipment['payment_mode'],
            'pickup_date' => $shipment['pickup_date'],
            'departure_date' => $shipment['departure_date'],
            'expected_delivery_date' => $shipment['expected_delivery_date'],
            'total_weight' => $shipment['total_weight'],
            'total_freight' => $shipment['total_freight'],
            'shipping_cost' => $shipment['total_freight'] ?? 0,
            'comments' => $shipment['comments'],
            'created_at' => $shipment['created_at'],
            'updated_at' => $shipment['updated_at']
        ];
        
        echo json_encode([
            'success' => true,
            'shipment' => $formattedShipment
        ]);
        
    } else {
        http_response_code(405);
        echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    }
    
} catch (Exception $e) {
    error_log("Error in shipment get API: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Error retrieving shipment: ' . $e->getMessage()]);
}
?>
