<?php
// Define BASE_PATH if not already defined
if (!defined('BASE_PATH')) {
    define('BASE_PATH', dirname(__DIR__, 4));
}

// Register Composer autoloader
require_once BASE_PATH . '/vendor/autoload.php';

use App\Core\App;
use App\Core\Database;
use App\Core\Session;

// Initialize the application
$app = App::getInstance();

// Set appropriate headers
header('Content-Type: application/json');

// Check authentication (basic check for admin session)
if (!Session::has('admin_user_id')) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Unauthorized']);
    exit;
}

try {
    if ($_SERVER['REQUEST_METHOD'] === 'GET') {
        $shipment_id = $_GET['id'] ?? null;
        
        if (!$shipment_id) {
            echo json_encode(['success' => false, 'message' => 'Shipment ID is required']);
            exit;
        }
        
        // Get shipment data with related information
        Database::prepare("
            SELECT s.*, 
                   c.name as client_name, c.email as client_email, c.phone as client_phone,
                   e.name as employee_name
            FROM shipments s
            LEFT JOIN clients c ON s.client_id = c.id
            LEFT JOIN employees e ON s.employee_id = e.id
            WHERE s.id = :id
        ");
        Database::bindValue(':id', $shipment_id, PDO::PARAM_INT);
        Database::execute();
        $shipment = Database::fetch();
        
        if (!$shipment) {
            echo json_encode(['success' => false, 'message' => 'Shipment not found']);
            exit;
        }
        
        // Get shipment packages
        Database::prepare("SELECT * FROM shipment_packages WHERE shipment_id = :shipment_id");
        Database::bindValue(':shipment_id', $shipment_id, PDO::PARAM_INT);
        Database::execute();
        $packages = Database::fetchAll();
        
        // Get shipment history
        Database::prepare("SELECT * FROM shipment_history WHERE shipment_id = :shipment_id ORDER BY date_time DESC");
        Database::bindValue(':shipment_id', $shipment_id, PDO::PARAM_INT);
        Database::execute();
        $history = Database::fetchAll();
        
        // Format dates for display
        if ($shipment['pickup_date']) {
            $shipment['pickup_date_formatted'] = date('M j, Y', strtotime($shipment['pickup_date']));
        }
        if ($shipment['expected_delivery_date']) {
            $shipment['expected_delivery_date_formatted'] = date('M j, Y', strtotime($shipment['expected_delivery_date']));
        }
        if ($shipment['departure_date']) {
            $shipment['departure_date_formatted'] = date('M j, Y', strtotime($shipment['departure_date']));
        }
        
        // Add packages and history to shipment data
        $shipment['packages'] = $packages;
        $shipment['history'] = $history;
        
        echo json_encode([
            'success' => true,
            'shipment' => $shipment
        ]);
        
    } else {
        http_response_code(405);
        echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    }
    
} catch (Exception $e) {
    error_log("Error in shipment get API: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Error loading shipment: ' . $e->getMessage()]);
}
?>