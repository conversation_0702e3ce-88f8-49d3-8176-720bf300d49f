<?php
require_once __DIR__ . '/../../../config/app.php';

use App\Core\Database;
use App\Core\Session;

header('Content-Type: application/json');

// Check if user is authenticated
if (!Session::get('user_id') || Session::get('user_role') !== 'admin') {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Unauthorized']);
    exit;
}

try {
    if ($_SERVER['REQUEST_METHOD'] === 'GET') {
        $searchTerm = $_GET['q'] ?? '';
        
        if (empty($searchTerm)) {
            echo json_encode(['success' => false, 'message' => 'Search term is required']);
            exit;
        }
        
        // Search shipments by tracking number, shipper name, or receiver name
        $sql = "SELECT 
                    s.id,
                    s.tracking_number,
                    s.shipper_name,
                    s.receiver_name,
                    s.origin,
                    s.destination,
                    s.status,
                    s.total_freight as shipping_cost,
                    s.created_at,
                    c.name as client_name
                FROM shipments s
                LEFT JOIN clients c ON s.client_id = c.id
                WHERE s.tracking_number LIKE :search 
                   OR s.shipper_name LIKE :search 
                   OR s.receiver_name LIKE :search
                   OR c.name LIKE :search
                ORDER BY s.created_at DESC
                LIMIT 20";
        
        Database::prepare($sql);
        Database::bindValue(':search', '%' . $searchTerm . '%', PDO::PARAM_STR);
        Database::execute();
        
        $shipments = Database::fetchAll();
        
        // Format the results
        $formattedShipments = [];
        foreach ($shipments as $shipment) {
            $formattedShipments[] = [
                'id' => $shipment['id'],
                'tracking_number' => $shipment['tracking_number'],
                'client_name' => $shipment['client_name'] ?? $shipment['shipper_name'],
                'origin' => $shipment['origin'],
                'destination' => $shipment['destination'],
                'status' => $shipment['status'],
                'shipping_cost' => $shipment['shipping_cost'] ?? 0,
                'created_at' => $shipment['created_at']
            ];
        }
        
        echo json_encode([
            'success' => true,
            'shipments' => $formattedShipments,
            'count' => count($formattedShipments)
        ]);
        
    } else {
        http_response_code(405);
        echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    }
    
} catch (Exception $e) {
    error_log("Error in shipment search API: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Error searching shipments: ' . $e->getMessage()]);
}
?>
