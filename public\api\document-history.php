<?php
require_once '../config/database.php';
require_once '../src/Core/Database.php';

header('Content-Type: application/json');

try {
    if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
        throw new Exception('Only GET requests are allowed');
    }

    $requestId = $_GET['request_id'] ?? null;

    if (!$requestId) {
        throw new Exception('Request ID is required');
    }

    // Get document request history
    Database::prepare("
        SELECT 
            dr.id,
            dr.status,
            dr.created_at,
            dr.updated_at,
            dr.review_notes,
            dr.due_date,
            u.name as user_name,
            dt.name as document_type_name,
            CASE 
                WHEN dr.status = 'pending' THEN 'Document Requested'
                WHEN dr.status = 'uploaded' THEN 'Document Uploaded'
                WHEN dr.status = 'approved' THEN 'Document Approved'
                WHEN dr.status = 'rejected' THEN 'Document Rejected'
                WHEN dr.status = 'cancelled' THEN 'Request Cancelled'
                ELSE dr.status
            END as status_display
        FROM document_requests dr
        LEFT JOIN users u ON dr.requested_by = u.id
        LEFT JOIN document_types dt ON dr.document_type_id = dt.id
        WHERE dr.id = ?
        ORDER BY dr.created_at DESC
    ");
    
    Database::execute([$requestId]);
    $request = Database::fetch();

    if (!$request) {
        throw new Exception('Document request not found');
    }

    // Get document uploads for this request
    Database::prepare("
        SELECT 
            du.id,
            du.original_filename,
            du.file_size,
            du.uploaded_at,
            du.upload_notes,
            du.status as upload_status,
            u.name as uploaded_by_name
        FROM document_uploads du
        LEFT JOIN users u ON du.uploaded_by = u.id
        WHERE du.request_id = ?
        ORDER BY du.uploaded_at DESC
    ");
    
    Database::execute([$requestId]);
    $uploads = Database::fetchAll();

    // Get document reviews
    Database::prepare("
        SELECT 
            dr.id,
            dr.status,
            dr.review_notes,
            dr.reviewed_at,
            u.name as reviewer_name
        FROM document_reviews dr
        LEFT JOIN users u ON dr.reviewed_by = u.id
        WHERE dr.request_id = ?
        ORDER BY dr.reviewed_at DESC
    ");
    
    Database::execute([$requestId]);
    $reviews = Database::fetchAll();

    // Build comprehensive history
    $history = [];

    // Add initial request
    $history[] = [
        'status' => 'pending',
        'status_display' => 'Document Requested',
        'created_at' => $request['created_at'],
        'created_at_formatted' => date('M j, Y g:i A', strtotime($request['created_at'])),
        'notes' => "Document type: {$request['document_type_name']}" . 
                  ($request['due_date'] ? " | Due: " . date('M j, Y', strtotime($request['due_date'])) : ''),
        'user_name' => $request['user_name']
    ];

    // Add uploads
    foreach ($uploads as $upload) {
        $history[] = [
            'status' => 'uploaded',
            'status_display' => 'Document Uploaded',
            'created_at' => $upload['uploaded_at'],
            'created_at_formatted' => date('M j, Y g:i A', strtotime($upload['uploaded_at'])),
            'notes' => "File: {$upload['original_filename']}" . 
                      ($upload['upload_notes'] ? " | Notes: {$upload['upload_notes']}" : ''),
            'user_name' => $upload['uploaded_by_name']
        ];
    }

    // Add reviews
    foreach ($reviews as $review) {
        $history[] = [
            'status' => $review['status'],
            'status_display' => $review['status'] === 'approved' ? 'Document Approved' : 'Document Rejected',
            'created_at' => $review['reviewed_at'],
            'created_at_formatted' => date('M j, Y g:i A', strtotime($review['reviewed_at'])),
            'notes' => $review['review_notes'],
            'user_name' => $review['reviewer_name']
        ];
    }

    // Sort history by date (newest first)
    usort($history, function($a, $b) {
        return strtotime($b['created_at']) - strtotime($a['created_at']);
    });

    echo json_encode([
        'success' => true,
        'history' => $history,
        'request' => $request
    ]);

} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?>
