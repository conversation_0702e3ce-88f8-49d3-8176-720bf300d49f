<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

try {
    $pdo = new PDO('mysql:host=127.0.0.1;dbname=shipment', 'root', 'root');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    if ($_SERVER['REQUEST_METHOD'] === 'GET') {
        // Get document requests for a specific tracking number
        $tracking_number = $_GET['tracking_number'] ?? '';
        
        if (empty($tracking_number)) {
            echo json_encode(['success' => false, 'message' => 'Tracking number is required']);
            exit;
        }
        
        // Get shipment ID from tracking number
        $stmt = $pdo->prepare('SELECT id FROM shipments WHERE tracking_number = ?');
        $stmt->execute([$tracking_number]);
        $shipment = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$shipment) {
            echo json_encode(['success' => false, 'message' => 'Shipment not found']);
            exit;
        }
        
        // Get document requests for this shipment
        $stmt = $pdo->prepare('
            SELECT 
                dr.id,
                dr.shipment_id,
                dr.document_type_id,
                dr.request_message,
                dr.priority,
                dr.due_date,
                dr.status,
                dr.created_at,
                dt.name as document_type_name,
                dt.description as document_type_description,
                u.name as requested_by_name
            FROM document_requests dr
            JOIN document_types dt ON dr.document_type_id = dt.id
            JOIN users u ON dr.requested_by = u.id
            WHERE dr.shipment_id = ?
            ORDER BY dr.created_at DESC
        ');
        
        $stmt->execute([$shipment['id']]);
        $requests = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // Format dates and add additional info
        foreach ($requests as &$request) {
            $request['created_at_formatted'] = date('M j, Y g:i A', strtotime($request['created_at']));
            if ($request['due_date']) {
                $request['due_date_formatted'] = date('M j, Y g:i A', strtotime($request['due_date']));
                $request['is_overdue'] = strtotime($request['due_date']) < time();
            }
            
            // Check if document has been uploaded
            $uploadStmt = $pdo->prepare('SELECT id, original_filename, uploaded_at FROM document_uploads WHERE document_request_id = ? ORDER BY uploaded_at DESC LIMIT 1');
            $uploadStmt->execute([$request['id']]);
            $upload = $uploadStmt->fetch(PDO::FETCH_ASSOC);
            
            if ($upload) {
                $request['uploaded_document'] = $upload;
                $request['uploaded_document']['uploaded_at_formatted'] = date('M j, Y g:i A', strtotime($upload['uploaded_at']));
            }
            
            // Check approval status
            if ($request['status'] === 'approved' || $request['status'] === 'rejected') {
                $approvalStmt = $pdo->prepare('
                    SELECT da.status, da.review_notes, da.reviewed_at, u.name as reviewed_by_name
                    FROM document_approvals da
                    JOIN users u ON da.reviewed_by = u.id
                    WHERE da.document_request_id = ?
                    ORDER BY da.reviewed_at DESC
                    LIMIT 1
                ');
                $approvalStmt->execute([$request['id']]);
                $approval = $approvalStmt->fetch(PDO::FETCH_ASSOC);
                
                if ($approval) {
                    $request['approval_info'] = $approval;
                    $request['approval_info']['reviewed_at_formatted'] = date('M j, Y g:i A', strtotime($approval['reviewed_at']));
                }
            }
        }
        
        echo json_encode([
            'success' => true,
            'requests' => $requests,
            'shipment_id' => $shipment['id']
        ]);
        
    } else {
        echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    }
    
} catch (PDOException $e) {
    error_log("Database error in document-requests.php: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'Database error occurred']);
} catch (Exception $e) {
    error_log("General error in document-requests.php: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'An error occurred']);
}
?>
