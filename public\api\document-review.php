<?php
header('Content-Type: application/json');

try {
    // Database connection
    $pdo = new PDO('mysql:host=127.0.0.1;dbname=shipment', 'root', 'root');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        $requestId = $_POST['request_id'] ?? '';
        $uploadId = $_POST['upload_id'] ?? '';
        $status = $_POST['status'] ?? '';
        $reviewNotes = $_POST['review_notes'] ?? '';
        
        // Validate input
        if (empty($requestId) || empty($uploadId) || empty($status)) {
            echo json_encode(['success' => false, 'message' => 'Missing required fields']);
            exit;
        }
        
        if (!in_array($status, ['approved', 'rejected'])) {
            echo json_encode(['success' => false, 'message' => 'Invalid status']);
            exit;
        }
        
        // Get current admin user ID (assuming admin user ID is 1 for now)
        $adminUserId = 1;
        
        // Verify document request and upload exist
        $stmt = $pdo->prepare('
            SELECT dr.id, dr.shipment_id, du.id as upload_id
            FROM document_requests dr
            JOIN document_uploads du ON dr.id = du.document_request_id
            WHERE dr.id = ? AND du.id = ?
        ');
        $stmt->execute([$requestId, $uploadId]);
        $document = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$document) {
            echo json_encode(['success' => false, 'message' => 'Document request or upload not found']);
            exit;
        }
        
        // Start transaction
        $pdo->beginTransaction();
        
        try {
            // Insert document approval record
            $stmt = $pdo->prepare('
                INSERT INTO document_approvals 
                (document_request_id, document_upload_id, reviewed_by, status, review_notes) 
                VALUES (?, ?, ?, ?, ?)
            ');
            $stmt->execute([$requestId, $uploadId, $adminUserId, $status, $reviewNotes]);
            
            // Update document request status
            $stmt = $pdo->prepare('UPDATE document_requests SET status = ? WHERE id = ?');
            $stmt->execute([$status, $requestId]);
            
            // Create notification
            $notificationTitle = $status === 'approved' ? 'Document Approved' : 'Document Rejected';
            $notificationMessage = $status === 'approved' 
                ? 'Your document has been approved by the admin.' 
                : 'Your document has been rejected. Please check the review notes and resubmit if necessary.';
            
            $stmt = $pdo->prepare('
                INSERT INTO notifications 
                (shipment_id, document_request_id, type, title, message, recipient_email) 
                VALUES (?, ?, ?, ?, ?, ?)
            ');
            $stmt->execute([
                $document['shipment_id'],
                $requestId,
                'document_' . $status,
                $notificationTitle,
                $notificationMessage,
                '<EMAIL>' // This would be the customer email
            ]);
            
            // Commit transaction
            $pdo->commit();
            
            echo json_encode([
                'success' => true,
                'message' => 'Document ' . $status . ' successfully'
            ]);
            
        } catch (Exception $e) {
            // Rollback transaction
            $pdo->rollback();
            throw $e;
        }
        
    } else {
        http_response_code(405);
        echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    }
    
} catch (Exception $e) {
    error_log("Error in document review API: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Error processing review: ' . $e->getMessage()]);
}
?>