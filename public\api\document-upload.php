<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

try {
    $pdo = new PDO('mysql:host=127.0.0.1;dbname=shipment', 'root', 'root');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        // Validate input
        $request_id = $_POST['request_id'] ?? '';
        $notes = $_POST['notes'] ?? '';
        
        if (empty($request_id)) {
            echo json_encode(['success' => false, 'message' => 'Request ID is required']);
            exit;
        }
        
        if (!isset($_FILES['document']) || $_FILES['document']['error'] !== UPLOAD_ERR_OK) {
            echo json_encode(['success' => false, 'message' => 'No file uploaded or upload error']);
            exit;
        }
        
        $file = $_FILES['document'];
        
        // Validate file
        $allowedTypes = ['application/pdf', 'image/jpeg', 'image/png', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'];
        $maxSize = 10 * 1024 * 1024; // 10MB
        
        if (!in_array($file['type'], $allowedTypes)) {
            echo json_encode(['success' => false, 'message' => 'Invalid file type. Only PDF, JPG, PNG, DOC, and DOCX files are allowed.']);
            exit;
        }
        
        if ($file['size'] > $maxSize) {
            echo json_encode(['success' => false, 'message' => 'File size too large. Maximum size is 10MB.']);
            exit;
        }
        
        // Verify document request exists
        $stmt = $pdo->prepare('SELECT id, shipment_id FROM document_requests WHERE id = ?');
        $stmt->execute([$request_id]);
        $request = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$request) {
            echo json_encode(['success' => false, 'message' => 'Document request not found']);
            exit;
        }
        
        // Create uploads directory if it doesn't exist
        $uploadDir = '../uploads/documents/';
        if (!is_dir($uploadDir)) {
            mkdir($uploadDir, 0755, true);
        }
        
        // Generate unique filename
        $extension = pathinfo($file['name'], PATHINFO_EXTENSION);
        $storedFilename = 'doc_' . $request_id . '_' . time() . '_' . uniqid() . '.' . $extension;
        $filePath = $uploadDir . $storedFilename;
        
        // Move uploaded file
        if (!move_uploaded_file($file['tmp_name'], $filePath)) {
            echo json_encode(['success' => false, 'message' => 'Failed to save uploaded file']);
            exit;
        }
        
        // Save document upload record
        $stmt = $pdo->prepare('
            INSERT INTO document_uploads 
            (document_request_id, original_filename, stored_filename, file_path, file_size, mime_type, upload_notes) 
            VALUES (?, ?, ?, ?, ?, ?, ?)
        ');
        
        $stmt->execute([
            $request_id,
            $file['name'],
            $storedFilename,
            $filePath,
            $file['size'],
            $file['type'],
            $notes
        ]);
        
        $uploadId = $pdo->lastInsertId();
        
        // Update document request status
        $stmt = $pdo->prepare('UPDATE document_requests SET status = ? WHERE id = ?');
        $stmt->execute(['uploaded', $request_id]);
        
        // Create notification for admin
        $stmt = $pdo->prepare('
            INSERT INTO notifications 
            (shipment_id, document_request_id, type, title, message, recipient_email) 
            VALUES (?, ?, ?, ?, ?, ?)
        ');
        
        $stmt->execute([
            $request['shipment_id'],
            $request_id,
            'document_uploaded',
            'Document Uploaded for Review',
            'A document has been uploaded and is awaiting admin review.',
            '<EMAIL>' // This would be the admin email
        ]);
        
        echo json_encode([
            'success' => true, 
            'message' => 'Document uploaded successfully',
            'upload_id' => $uploadId
        ]);
        
    } else {
        echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    }
    
} catch (PDOException $e) {
    error_log("Database error in document-upload.php: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'Database error occurred']);
} catch (Exception $e) {
    error_log("General error in document-upload.php: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'An error occurred: ' . $e->getMessage()]);
}
?>
