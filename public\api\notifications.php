<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

try {
    $pdo = new PDO('mysql:host=127.0.0.1;dbname=shipment', 'root', 'root');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    if ($_SERVER['REQUEST_METHOD'] === 'GET') {
        // Get notifications for a specific tracking number
        $tracking_number = $_GET['tracking_number'] ?? '';
        
        if (empty($tracking_number)) {
            echo json_encode(['success' => false, 'message' => 'Tracking number is required']);
            exit;
        }
        
        // Get shipment ID from tracking number
        $stmt = $pdo->prepare('SELECT id FROM shipments WHERE tracking_number = ?');
        $stmt->execute([$tracking_number]);
        $shipment = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$shipment) {
            echo json_encode(['success' => false, 'message' => 'Shipment not found']);
            exit;
        }
        
        // Get notifications for this shipment
        $stmt = $pdo->prepare('
            SELECT 
                id,
                shipment_id,
                document_request_id,
                type,
                title,
                message,
                recipient_email,
                is_read,
                is_sent,
                sent_at,
                created_at
            FROM notifications 
            WHERE shipment_id = ?
            ORDER BY created_at DESC
        ');
        
        $stmt->execute([$shipment['id']]);
        $notifications = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // Format dates
        foreach ($notifications as &$notification) {
            $notification['created_at_formatted'] = date('M j, Y g:i A', strtotime($notification['created_at']));
            if ($notification['sent_at']) {
                $notification['sent_at_formatted'] = date('M j, Y g:i A', strtotime($notification['sent_at']));
            }
        }
        
        echo json_encode([
            'success' => true,
            'notifications' => $notifications,
            'shipment_id' => $shipment['id']
        ]);
        
    } else {
        echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    }
    
} catch (PDOException $e) {
    error_log("Database error in notifications.php: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'Database error occurred']);
} catch (Exception $e) {
    error_log("General error in notifications.php: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'An error occurred']);
}
?>
