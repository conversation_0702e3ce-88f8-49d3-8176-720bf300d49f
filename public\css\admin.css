/* Admin Panel CSS */

/* Base Styles - Black and White Theme */
:root {
    --primary-color: #000000;
    --secondary-color: #333333;
    --success-color: #444444;
    --danger-color: #666666;
    --warning-color: #888888;
    --info-color: #555555;
    --light-color: #f5f5f5;
    --dark-color: #222222;
    --border-color: #dddddd;
    --text-color: #333333;
    --text-muted: #777777;
    --sidebar-width: 250px;
    --header-height: 60px;
    --font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

body {
    font-family: var(--font-family);
    line-height: 1.6;
    color: var(--text-color);
    background-color: #f5f5f5;
    min-height: 100vh;
}

a {
    color: var(--primary-color);
    text-decoration: none;
}

a:hover {
    text-decoration: underline;
}

/* Layout */
.header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    height: var(--header-height);
    background-color: #000000;
    color: white;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 20px;
    z-index: 1000;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.header h1 {
    font-size: 1.5rem;
    font-weight: 600;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 15px;
}

.user-info button {
    background-color: #333333;
    color: white;
    border: none;
    padding: 8px 15px;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.2s;
}

.user-info button:hover {
    background-color: #555555;
}

.sidebar {
    position: fixed;
    top: var(--header-height);
    left: 0;
    width: var(--sidebar-width);
    height: calc(100vh - var(--header-height));
    background-color: #222222;
    border-right: 1px solid #333333;
    overflow-y: auto;
    padding: 20px 0;
    z-index: 900;
}

.content {
    margin-left: var(--sidebar-width);
    margin-top: var(--header-height);
    padding: 20px;
    min-height: calc(100vh - var(--header-height));
}

/* Navigation */
nav ul {
    list-style: none;
}

nav ul li {
    margin-bottom: 5px;
}

nav ul li a {
    display: block;
    padding: 10px 20px;
    color: #ffffff;
    text-decoration: none;
    transition: background-color 0.2s;
    border-left: 3px solid transparent;
}

nav ul li a:hover,
nav ul li a.active {
    background-color: #333333;
    border-left-color: #ffffff;
}

nav ul li a i {
    margin-right: 10px;
    width: 20px;
    text-align: center;
}

nav ul ul {
    margin-left: 20px;
}

/* Cards */
.card {
    background-color: white;
    border-radius: 5px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
    margin-bottom: 20px;
    overflow: hidden;
}

.card-header {
    padding: 15px 20px;
    border-bottom: 1px solid var(--border-color);
    background-color: #f9f9f9;
    font-weight: 600;
}

.card-body {
    padding: 20px;
}

.card-footer {
    padding: 15px 20px;
    border-top: 1px solid var(--border-color);
    background-color: #f9f9f9;
}

/* Dashboard Widgets */
.dashboard-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background-color: white;
    border-radius: 5px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
    padding: 20px;
    display: flex;
    flex-direction: column;
    transition: transform 0.2s, box-shadow 0.2s;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.stat-card .stat-icon {
    font-size: 2.5rem;
    margin-bottom: 15px;
    color: var(--primary-color);
}

.stat-card .stat-value {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 5px;
}

.stat-card .stat-label {
    color: var(--text-muted);
    font-size: 0.9rem;
}

.stat-card.primary { border-top: 3px solid var(--primary-color); }
.stat-card.success { border-top: 3px solid var(--success-color); }
.stat-card.warning { border-top: 3px solid var(--warning-color); }
.stat-card.danger { border-top: 3px solid var(--danger-color); }

/* Tables */
.table-container {
    overflow-x: auto;
}

table {
    width: 100%;
    border-collapse: collapse;
}

table th,
table td {
    padding: 12px 15px;
    text-align: left;
    border-bottom: 1px solid var(--border-color);
}

table th {
    background-color: #f9f9f9;
    font-weight: 600;
}

table tr:hover {
    background-color: #f5f7fa;
}

/* Forms */
.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
}

.form-control {
    width: 100%;
    padding: 10px;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    font-family: inherit;
    font-size: 1rem;
    transition: border-color 0.2s;
}

.form-control:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

.btn {
    display: inline-block;
    padding: 10px 20px;
    background-color: #000000;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 1rem;
    transition: background-color 0.2s;
}

.btn:hover {
    background-color: #333333;
}

.btn-primary { background-color: #000000; }
.btn-primary:hover { background-color: #333333; }

.btn-success { background-color: #444444; }
.btn-success:hover { background-color: #666666; }

.btn-danger { background-color: #666666; }
.btn-danger:hover { background-color: #888888; }

.btn-warning { background-color: #888888; }
.btn-warning:hover { background-color: #aaaaaa; }

.btn-sm {
    padding: 5px 10px;
    font-size: 0.875rem;
}

.btn-lg {
    padding: 12px 24px;
    font-size: 1.125rem;
}

/* Alerts */
.alert {
    padding: 15px;
    margin-bottom: 20px;
    border: 1px solid transparent;
    border-radius: 4px;
}

.alert-success {
    color: #155724;
    background-color: #d4edda;
    border-color: #c3e6cb;
}

.alert-error, .alert-danger {
    color: #721c24;
    background-color: #f8d7da;
    border-color: #f5c6cb;
}

.alert-warning {
    color: #856404;
    background-color: #fff3cd;
    border-color: #ffeeba;
}

.alert-info {
    color: #0c5460;
    background-color: #d1ecf1;
    border-color: #bee5eb;
}

/* Utilities */
.text-center { text-align: center; }
.text-right { text-align: right; }
.text-muted { color: var(--text-muted); }

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: 0.25rem; }
.mb-2 { margin-bottom: 0.5rem; }
.mb-3 { margin-bottom: 1rem; }
.mb-4 { margin-bottom: 1.5rem; }
.mb-5 { margin-bottom: 3rem; }

.mt-0 { margin-top: 0; }
.mt-1 { margin-top: 0.25rem; }
.mt-2 { margin-top: 0.5rem; }
.mt-3 { margin-top: 1rem; }
.mt-4 { margin-top: 1.5rem; }
.mt-5 { margin-top: 3rem; }

.d-flex { display: flex; }
.justify-content-between { justify-content: space-between; }
.align-items-center { align-items: center; }
.flex-wrap { flex-wrap: wrap; }

/* Badges */
.badge {
    display: inline-block;
    padding: 0.25em 0.6em;
    font-size: 75%;
    font-weight: 700;
    line-height: 1;
    text-align: center;
    white-space: nowrap;
    vertical-align: baseline;
    border-radius: 0.25rem;
    color: white;
}

.bg-primary { background-color: #000000; }
.bg-success { background-color: #444444; }
.bg-danger { background-color: #666666; }
.bg-warning { background-color: #888888; }
.bg-info { background-color: #555555; }

/* Row and Column Layout */
.row {
    display: flex;
    flex-wrap: wrap;
    margin-right: -15px;
    margin-left: -15px;
}

.col-md-6 {
    flex: 0 0 50%;
    max-width: 50%;
    padding-right: 15px;
    padding-left: 15px;
}

/* Margin and Spacing Utilities */
.mr-2 { margin-right: 0.5rem; }
.ml-2 { margin-left: 0.5rem; }

/* Responsive */
@media (max-width: 992px) {
    .sidebar {
        width: 200px;
    }

    .content {
        margin-left: 200px;
    }

    .col-md-6 {
        flex: 0 0 100%;
        max-width: 100%;
    }
}

@media (max-width: 768px) {
    .sidebar {
        transform: translateX(-100%);
        transition: transform 0.3s;
    }

    .sidebar.active {
        transform: translateX(0);
    }

    .content {
        margin-left: 0;
    }

    .dashboard-stats {
        grid-template-columns: 1fr;
    }

    .d-md-none {
        display: block;
    }
}

@media (min-width: 769px) {
    .d-md-none {
        display: none;
    }
}
