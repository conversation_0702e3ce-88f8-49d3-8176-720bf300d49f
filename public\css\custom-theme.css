/* Auto-generated custom theme CSS */
:root {
    --primary-color: #1a1a1a;
    --secondary-color: #a04646;
    --accent-color: #ffffff;
    --sidebar-bg-color: #1a1a1a;
    --sidebar-text-color: #ffffff;
    --sidebar-hover-color: #333333;
    --primary-rgb: 26, 26, 26;
    --secondary-rgb: 160, 70, 70;
    --accent-rgb: 255, 255, 255;
    --sidebar-bg-rgb: 26, 26, 26;
    --sidebar-text-rgb: 255, 255, 255;
    --sidebar-hover-rgb: 51, 51, 51;
}

/* Header and Navigation */
.header {
    background-color: var(--primary-color) !important;
}

.sidebar {
    background-color: var(--sidebar-bg-color) !important;
}

.sidebar nav ul li a {
    color: var(--sidebar-text-color) !important;
}

.sidebar nav ul li a:hover,
.sidebar nav ul li a.active {
    color: var(--sidebar-text-color) !important;
    background-color: var(--sidebar-hover-color) !important;
}

/* Buttons */
.btn-primary {
    background-color: var(--secondary-color) !important;
    border-color: var(--secondary-color) !important;
}

.btn-primary:hover {
    background-color: var(--accent-color) !important;
    border-color: var(--accent-color) !important;
}

/* Links */
a {
    color: var(--accent-color) !important;
}

/* Cards */
.card-header {
    background-color: rgba(var(--primary-rgb), 0.1) !important;
    border-bottom-color: var(--primary-color) !important;
}

/* Tables */
.table thead th {
    background-color: var(--primary-color) !important;
    color: white !important;
}

/* Stats Cards */
.stat-card.primary {
    border-top-color: var(--primary-color) !important;
}

.stat-card .stat-icon {
    color: var(--primary-color) !important;
}

/* Badges */
.bg-primary {
    background-color: var(--primary-color) !important;
}

.bg-secondary {
    background-color: var(--secondary-color) !important;
}
