/* Default custom theme CSS - will be overwritten by appearance settings */
:root {
    --primary-color: #000000;
    --secondary-color: #333333;
    --accent-color: #555555;
    --primary-rgb: 0, 0, 0;
    --secondary-rgb: 51, 51, 51;
    --accent-rgb: 85, 85, 85;
}

/* Header and Navigation */
.header {
    background-color: var(--primary-color) !important;
}

.sidebar {
    background-color: var(--primary-color) !important;
}

.sidebar nav ul li a {
    color: rgba(255, 255, 255, 0.8) !important;
}

.sidebar nav ul li a:hover,
.sidebar nav ul li a.active {
    color: white !important;
    background-color: rgba(var(--secondary-rgb), 0.2) !important;
}

/* Buttons */
.btn-primary {
    background-color: var(--secondary-color) !important;
    border-color: var(--secondary-color) !important;
}

.btn-primary:hover {
    background-color: var(--accent-color) !important;
    border-color: var(--accent-color) !important;
}

/* Links */
a {
    color: var(--accent-color) !important;
}

/* Cards */
.card-header {
    background-color: rgba(var(--primary-rgb), 0.1) !important;
    border-bottom-color: var(--primary-color) !important;
}

/* Tables */
.table thead th {
    background-color: var(--primary-color) !important;
    color: white !important;
}

/* Stats Cards */
.stat-card.primary {
    border-top-color: var(--primary-color) !important;
}

.stat-card .stat-icon {
    color: var(--primary-color) !important;
}

/* Badges */
.bg-primary {
    background-color: var(--primary-color) !important;
}

.bg-secondary {
    background-color: var(--secondary-color) !important;
}

/* Public Tracking Page Styles */
.tracking-button,
.search-button {
    background-color: var(--secondary-color) !important;
}

.tracking-button:hover,
.search-button:hover {
    background-color: var(--accent-color) !important;
}

.tracking-container::before {
    background: linear-gradient(90deg, transparent, rgba(var(--primary-rgb), 0.5), transparent) !important;
}

.nav-item.active {
    color: var(--primary-color) !important;
}

.tracking-title {
    color: var(--primary-color) !important;
}

/* Status badges with theme colors */
.shipment-status {
    border: 1px solid var(--primary-color) !important;
}

.status-in-transit {
    background-color: rgba(var(--primary-rgb), 0.1) !important;
    color: var(--primary-color) !important;
}

.status-delivered {
    background-color: rgba(var(--accent-rgb), 0.1) !important;
    color: var(--accent-color) !important;
}