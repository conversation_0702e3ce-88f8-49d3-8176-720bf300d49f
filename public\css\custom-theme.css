/* Auto-generated custom theme CSS */
:root {
    --primary-color: #1a1a1a;
    --secondary-color: #a04646;
    --accent-color: #ffffff;
    --primary-rgb: 26, 26, 26;
    --secondary-rgb: 160, 70, 70;
    --accent-rgb: 255, 255, 255;
}

/* Header and Navigation */
.header {
    background-color: var(--primary-color) !important;
}

.sidebar {
    background-color: var(--primary-color) !important;
}

.sidebar nav ul li a {
    color: rgba(255, 255, 255, 0.8) !important;
}

.sidebar nav ul li a:hover,
.sidebar nav ul li a.active {
    color: white !important;
    background-color: rgba(var(--secondary-rgb), 0.2) !important;
}

/* Buttons */
.btn-primary {
    background-color: var(--secondary-color) !important;
    border-color: var(--secondary-color) !important;
}

.btn-primary:hover {
    background-color: var(--accent-color) !important;
    border-color: var(--accent-color) !important;
}

/* Links */
a {
    color: var(--accent-color) !important;
}

/* Cards */
.card-header {
    background-color: rgba(var(--primary-rgb), 0.1) !important;
    border-bottom-color: var(--primary-color) !important;
}

/* Tables */
.table thead th {
    background-color: var(--primary-color) !important;
    color: white !important;
}

/* Stats Cards */
.stat-card.primary {
    border-top-color: var(--primary-color) !important;
}

.stat-card .stat-icon {
    color: var(--primary-color) !important;
}

/* Badges */
.bg-primary {
    background-color: var(--primary-color) !important;
}

.bg-secondary {
    background-color: var(--secondary-color) !important;
}
