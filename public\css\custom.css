/* Custom CSS for specific fixes */

/* Hide the Created column on screens up to 1366px (typical 13-14 inch laptops) */
@media (max-width: 1366px) {
    .hide-on-smaller-screens {
        display: none !important;
    }
}

/* Make sure buttons in action column are displayed horizontally */
.action-buttons {
    display: flex !important;
    gap: 5px !important;
}

/* Smooth scrolling for sidebar without visible scrollbar */
.sidebar {
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE and Edge */
    overflow-y: auto;
    scroll-behavior: smooth;
}

/* Hide scrollbar for Chrome, Safari and Opera */
.sidebar::-webkit-scrollbar {
    display: none;
}

/* Ensure content inside sidebar has enough padding at the bottom */
.sidebar nav {
    padding-bottom: 20px;
}

/* Enhance sidebar navigation items */
.sidebar nav ul li a {
    transition: all 0.2s ease-in-out;
    border-left: none !important; /* Remove left border */
}

.sidebar nav ul li a:hover,
.sidebar nav ul li a.active {
    transform: translateX(5px);
    background-color: rgba(0, 0, 0, 0.05);
    border-left: none !important; /* Ensure no left border on hover/active */
}
