/* Modern Dashboard CSS - Black and White Theme */

/* Dashboard Layout */
.dashboard-container {
    display: grid;
    grid-template-columns: 1fr;
    gap: 1.5rem;
    margin-bottom: 2rem;
}

/* Welcome Card */
.welcome-card {
    background-color: #FFFFFF;
    border-radius: 0.5rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    padding: 1.5rem;
    border: 1px solid #E2E8F0;
    margin-bottom: 1.5rem;
}

.welcome-card h2 {
    font-size: 1.25rem;
    font-weight: 600;
    color: #1A202C;
    margin-bottom: 0.5rem;
}

.welcome-card p {
    color: #718096;
    font-size: 0.875rem;
    line-height: 1.5;
}

/* Stats Cards */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.stat-card {
    background-color: #FFFFFF;
    border-radius: 0.5rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    padding: 1rem;
    transition: transform 0.2s, box-shadow 0.2s;
    display: flex;
    flex-direction: column;
    position: relative;
    overflow: hidden;
    height: 120px;
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.stat-card .stat-title {
    font-size: 0.75rem;
    font-weight: 500;
    color: #718096;
    margin-bottom: 0.5rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.stat-card .stat-percent {
    color: #718096;
    font-size: 0.75rem;
    font-weight: 600;
}

.stat-card .stat-percent.up {
    color: #4299E1; /* Blue for increase */
}

.stat-card .stat-percent.down {
    color: #F56565; /* Red for decrease */
}

.stat-card .stat-value {
    font-size: 1.75rem;
    font-weight: 700;
    color: #1A202C;
    margin-bottom: 0.75rem;
}

.stat-card .stat-graph {
    position: absolute;
    bottom: 0;
    right: 0;
    width: 100px;
    height: 40px;
    opacity: 0.5;
}

.stat-card .stat-graph.up {
    color: #4299E1;
}

.stat-card .stat-graph.down {
    color: #F56565;
}

.stat-card .stat-graph.neutral {
    color: #A0AEC0;
}

/* Remove all borders from stat cards */
.stat-card.primary,
.stat-card.warning,
.stat-card.info,
.stat-card.success {
    border: none;
}

/* Charts Section */
.charts-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 1.5rem;
    margin-bottom: 1.5rem;
}

.chart-card {
    background-color: #FFFFFF;
    border-radius: 0.5rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    border: 1px solid #E2E8F0;
    overflow: hidden;
}

.chart-card .chart-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem 1.5rem;
    border-bottom: 1px solid #E2E8F0;
    background-color: #F8FAFC;
}

.chart-card .chart-title {
    font-size: 0.875rem;
    font-weight: 600;
    color: #4A5568;
}

.chart-card .chart-badge {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.5rem;
    border-radius: 9999px;
    background-color: #333333;
    color: #FFFFFF;
    font-size: 0.75rem;
    font-weight: 500;
}

.chart-card .chart-body {
    padding: 1.5rem;
    height: 300px;
    position: relative;
}

/* Recent Shipments Table */
.table-card {
    background-color: #FFFFFF;
    border-radius: 0.5rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    border: 1px solid #E2E8F0;
    overflow: hidden;
    margin-bottom: 1.5rem;
}

.table-card .table-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem 1.5rem;
    border-bottom: 1px solid #E2E8F0;
    background-color: #F8FAFC;
}

.table-card .table-title {
    font-size: 0.875rem;
    font-weight: 600;
    color: #4A5568;
}

.table-card .table-action {
    display: inline-flex;
    align-items: center;
    padding: 0.375rem 0.75rem;
    border-radius: 0.375rem;
    background-color: #333333;
    color: #FFFFFF;
    font-size: 0.75rem;
    font-weight: 500;
    text-decoration: none;
    transition: background-color 0.2s;
}

.table-card .table-action:hover {
    background-color: #1A202C;
    text-decoration: none;
}

.table-card .table-container {
    overflow-x: auto;
}

.modern-table {
    width: 100%;
    border-collapse: collapse;
}

.modern-table th {
    background-color: #F8FAFC;
    color: #4A5568;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    padding: 0.75rem 1rem;
    text-align: left;
    border-bottom: 1px solid #E2E8F0;
}

.modern-table td {
    padding: 1rem;
    font-size: 0.875rem;
    color: #1A202C;
    border-bottom: 1px solid #E2E8F0;
}

.modern-table tr:nth-child(even) {
    background-color: #F8FAFC;
}

.modern-table tr:hover {
    background-color: #EDF2F7;
}

.modern-table .status-badge {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.5rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 500;
}

.modern-table .status-badge.pending {
    background-color: #FEFCBF;
    color: #744210;
}

.modern-table .status-badge.in-transit {
    background-color: #BEE3F8;
    color: #2A4365;
}

.modern-table .status-badge.delivered {
    background-color: #C6F6D5;
    color: #22543D;
}

.modern-table .status-badge.delayed {
    background-color: #FED7D7;
    color: #822727;
}

.modern-table .status-badge.cancelled {
    background-color: #E2E8F0;
    color: #4A5568;
}

.modern-table .action-buttons {
    display: flex;
    gap: 0.5rem;
}

.modern-table .action-button {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 28px;
    height: 28px;
    border-radius: 0.375rem;
    background-color: #EDF2F7;
    color: #4A5568;
    transition: background-color 0.2s, color 0.2s;
}

.modern-table .action-button:hover {
    background-color: #333333;
    color: #FFFFFF;
}

.modern-table .empty-state {
    padding: 3rem 1.5rem;
    text-align: center;
}

.modern-table .empty-state i {
    font-size: 3rem;
    color: #CBD5E0;
    margin-bottom: 1rem;
}

.modern-table .empty-state p {
    color: #718096;
    font-size: 0.875rem;
}

/* Quick Actions */
.actions-card {
    background-color: #FFFFFF;
    border-radius: 0.5rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    border: 1px solid #E2E8F0;
    overflow: hidden;
    margin-bottom: 1.5rem;
}

.actions-card .actions-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem 1.5rem;
    border-bottom: 1px solid #E2E8F0;
    background-color: #F8FAFC;
}

.actions-card .actions-title {
    font-size: 0.875rem;
    font-weight: 600;
    color: #4A5568;
}

.actions-card .actions-badge {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.5rem;
    border-radius: 9999px;
    background-color: #333333;
    color: #FFFFFF;
    font-size: 0.75rem;
    font-weight: 500;
}

.actions-card .actions-body {
    padding: 1.5rem;
    display: flex;
    flex-wrap: wrap;
    gap: 0.75rem;
}

.action-button {
    display: inline-flex;
    align-items: center;
    padding: 0.625rem 1rem;
    border-radius: 0.375rem;
    background-color: #333333;
    color: #FFFFFF;
    font-size: 0.875rem;
    font-weight: 500;
    text-decoration: none;
    transition: background-color 0.2s;
}

.action-button:hover {
    background-color: #1A202C;
    text-decoration: none;
}

.action-button i {
    margin-right: 0.5rem;
}

/* Map and Delivery Progress */
.map-delivery-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1.5rem;
    margin-bottom: 1.5rem;
}

.map-card {
    background-color: #FFFFFF;
    border-radius: 0.5rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    overflow: hidden;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.map-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem 1.5rem;
    border-bottom: 1px solid #E2E8F0;
    background-color: #F8FAFC;
}

.map-title {
    font-size: 0.875rem;
    font-weight: 600;
    color: #4A5568;
}

.map-container {
    flex: 1;
    min-height: 300px;
    position: relative;
    background-color: #F7FAFC;
    overflow: hidden;
    border-radius: 0 0 0.5rem 0.5rem;
    border: 1px solid #E2E8F0;
    background-image: linear-gradient(#E2E8F0 1px, transparent 1px),
                      linear-gradient(90deg, #E2E8F0 1px, transparent 1px);
    background-size: 60px 60px;
    box-shadow: inset 0 0 20px rgba(0, 0, 0, 0.05);
}

.map-marker {
    position: absolute;
    width: 24px;
    height: 24px;
    transform: translate(-50%, -50%); /* Center the marker on the exact point */
    color: #4299E1;
    background-color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 0 0 2px white;
    animation: pulse 1.5s infinite;
    z-index: 10;
}

.map-marker i {
    /* Adjust icon position to account for rotation */
    transform: rotate(90deg); /* Default truck icon faces up, we want it to face right */
    font-size: 14px;
}

@keyframes pulse {
    0% {
        transform: scale(0.95);
        box-shadow: 0 0 0 0 rgba(66, 153, 225, 0.7);
    }

    70% {
        transform: scale(1);
        box-shadow: 0 0 0 10px rgba(66, 153, 225, 0);
    }

    100% {
        transform: scale(0.95);
        box-shadow: 0 0 0 0 rgba(66, 153, 225, 0);
    }
}

.delivery-progress-card {
    background-color: #FFFFFF;
    border-radius: 0.5rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    overflow: hidden;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.progress-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem 1.5rem;
    border-bottom: 1px solid #E2E8F0;
    background-color: #F8FAFC;
}

.progress-title {
    font-size: 0.875rem;
    font-weight: 600;
    color: #4A5568;
}

.progress-body {
    flex: 1;
    padding: 1.5rem;
    overflow-y: auto;
}

.delivery-steps {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.delivery-step {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
}

.step-icon {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background-color: #F7FAFC;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.step-icon.active {
    background-color: #4299E1;
    color: #FFFFFF;
}

.step-icon.completed {
    background-color: #48BB78;
    color: #FFFFFF;
}

.step-content {
    flex: 1;
}

.step-title {
    font-size: 0.875rem;
    font-weight: 600;
    color: #1A202C;
    margin-bottom: 0.25rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.step-tracking {
    font-size: 0.75rem;
    font-weight: 500;
    color: #718096;
    background-color: #EDF2F7;
    padding: 0.125rem 0.375rem;
    border-radius: 0.25rem;
}

.step-location {
    font-size: 0.75rem;
    color: #718096;
    margin-bottom: 0.25rem;
}

.step-time {
    font-size: 0.75rem;
    color: #A0AEC0;
}

.step-line {
    position: relative;
}

.step-line::before {
    content: '';
    position: absolute;
    top: 32px;
    left: 16px;
    width: 2px;
    height: calc(100% - 32px);
    background-color: #E2E8F0;
}

.step-line:last-child::before {
    display: none;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .charts-grid {
        grid-template-columns: 1fr;
    }

    .chart-card .chart-body {
        height: 250px;
    }

    .modern-table th,
    .modern-table td {
        padding: 0.75rem 0.5rem;
    }

    .action-button {
        flex: 1 1 calc(50% - 0.75rem);
    }
}

@media (max-width: 480px) {
    .stats-grid {
        grid-template-columns: 1fr;
    }

    .action-button {
        flex: 1 1 100%;
    }
}
