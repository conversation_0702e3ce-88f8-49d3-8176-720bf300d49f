/* Basic Reset/Defaults */
body, html {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Inter', sans-serif;
}
*, *:before, *:after {
    box-sizing: inherit;
}

.tracking-page-body {
    background-color: #e6e6e6; /* Darker gray background */
    color: #333333;
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    padding: 20px;
    position: relative;
    overflow: hidden;
}

/* Floating background elements */
.bg-elements {
    position: fixed; /* Changed from absolute to fixed for better coverage */
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: -1;
    overflow: hidden;
}

.bg-element {
    position: absolute;
    opacity: 0.2; /* Increased opacity for better visibility */
    color: #333333;
    font-size: 1rem; /* Smaller base size for icons */
}

.bg-element.truck {
    width: 80px;
    height: 80px;
    top: 15%;
    left: 10%;
    animation: float 15s ease-in-out infinite;
}

.bg-element.box {
    width: 60px;
    height: 60px;
    bottom: 20%;
    right: 15%;
    animation: float 12s ease-in-out infinite 2s;
}

.bg-element.plane {
    width: 100px;
    height: 100px;
    top: 70%;
    left: 20%;
    animation: float 20s ease-in-out infinite 1s;
}

.bg-element.ship {
    width: 120px;
    height: 120px;
    top: 30%;
    right: 10%;
    animation: float 18s ease-in-out infinite 3s;
}

.bg-element.circle {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    border: 2px solid #333333;
    top: 25%;
    left: 30%;
    animation: float 10s ease-in-out infinite 1s;
}

.bg-element.triangle {
    width: 0;
    height: 0;
    border-left: 30px solid transparent;
    border-right: 30px solid transparent;
    border-bottom: 50px solid #333333;
    bottom: 15%;
    left: 40%;
    animation: float 14s ease-in-out infinite 2s;
}

.bg-element.square {
    width: 50px;
    height: 50px;
    border: 2px solid #333333;
    transform: rotate(45deg);
    top: 60%;
    right: 25%;
    animation: float 16s ease-in-out infinite;
}

@keyframes float {
    0% {
        transform: translateY(0) rotate(0deg);
    }
    50% {
        transform: translateY(-20px) rotate(5deg);
    }
    100% {
        transform: translateY(0) rotate(0deg);
    }
}

.tracking-container {
    max-width: 500px;
    width: 100%;
    text-align: center;
    padding: 40px 40px 30px;
    background-color: #ffffff; /* White background */
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    border-radius: 8px;
    position: relative;
    border: 1px solid rgba(226, 232, 240, 0.8);
    overflow: hidden;
    margin: 20px;
}

.tracking-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, transparent, rgba(66, 153, 225, 0.5), transparent);
}

/* Removed the blue line that was causing overflow */

/* Container background patterns */
.container-pattern {
    position: absolute;
    opacity: 0.03;
    z-index: 0;
}

.pattern-circle {
    width: 100px;
    height: 100px;
    border: 2px solid #333;
    border-radius: 50%;
    top: 20px;
    right: 20px;
}

.pattern-triangle {
    width: 0;
    height: 0;
    border-left: 50px solid transparent;
    border-right: 50px solid transparent;
    border-bottom: 80px solid #333;
    bottom: 20px;
    left: 20px;
}

.pattern-square {
    width: 60px;
    height: 60px;
    border: 2px solid #333;
    transform: rotate(45deg);
    bottom: 40px;
    right: 40px;
}

.tracking-title {
    font-size: 1.75rem;
    font-weight: 600;
    margin-bottom: 12px;
    color: #1A202C;
}

.tracking-subtitle {
    font-size: 0.9rem;
    color: #718096;
    margin-bottom: 30px;
}

.tracking-form .input-group {
    display: flex;
    border: 1px solid #E2E8F0;
    border-radius: 6px;
    overflow: hidden;
    box-shadow: 0 1px 3px rgba(0,0,0,0.05);
    position: relative;
    z-index: 1;
}

.tracking-input {
    flex-grow: 1;
    padding: 12px 15px 12px 40px;
    font-size: 1rem;
    border: none;
    outline: none;
    color: #333333;
    background-color: #ffffff;
    box-shadow: inset 0 1px 3px rgba(0,0,0,0.05);
}

.tracking-input::placeholder {
    color: #A0AEC0;
}

.input-icon {
    position: absolute;
    left: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: #A0AEC0;
    z-index: 2;
}

.tracking-button {
    padding: 12px 20px;
    font-size: 1rem;
    background-color: #333333;
    color: #ffffff;
    border: none;
    cursor: pointer;
    transition: background-color 0.2s ease;
    display: flex;
    align-items: center;
    gap: 8px;
}

.tracking-button:hover {
    background-color: #555555;
}

.tracking-button .button-text {
    display: inline;
}

.tracking-note {
    font-size: 0.75rem;
    color: #718096;
    margin-top: 10px;
    margin-bottom: 0;
    text-align: left;
    display: flex;
    align-items: center;
    gap: 6px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.tracking-note span {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.note-icon {
    color: #4A5568;
    margin-top: 2px;
    flex-shrink: 0;
}

/* Hide text on smaller screens */
@media (max-width: 480px) {
    .tracking-button .button-text {
        display: none;
    }
    .tracking-button {
        padding: 12px 15px;
    }
    .tracking-container {
        padding: 20px;
    }
}

.tracking-results-area {
    margin-top: 20px;
    text-align: left;
    min-height: 0;
}

/* Loading Spinner */
.loading-spinner {
    border: 4px solid #f3f3f3;
    border-top: 4px solid #555555;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    animation: spin 1s linear infinite;
    margin: 20px auto;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Error Message */
.tracking-error {
    color: #E53E3E;
    background-color: #FFF5F5;
    border: 1px solid #FC8181;
    padding: 15px;
    border-radius: 6px;
    text-align: center;
    margin-top: 20px;
}

/* Tracking Result Page Styling */
.tracking-result-container {
    max-width: 800px;
    padding: 30px;
}

/* Delivery Route Timeline Styling */
.delivery-route-timeline {
    margin: 30px 0;
    padding: 20px;
    background-color: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e2e8f0;
}

.delivery-steps {
    display: flex;
    justify-content: space-between;
    position: relative;
    padding: 0 10px;
}

.delivery-steps::before {
    content: '';
    position: absolute;
    top: 30px;
    left: 50px;
    right: 50px;
    height: 2px;
    background-color: #e2e8f0;
    z-index: 1;
}

.delivery-step {
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
    z-index: 2;
    flex: 1;
    max-width: 150px;
    text-align: center;
}

.step-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background-color: #ffffff;
    border: 2px solid #e2e8f0;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 10px;
    color: #718096;
    font-size: 1.25rem;
    transition: all 0.3s ease;
}

.step-content {
    padding: 10px;
}

.step-label {
    font-weight: 600;
    font-size: 0.9rem;
    margin-bottom: 5px;
    color: #4a5568;
}

.step-description {
    font-size: 0.8rem;
    color: #718096;
    margin-bottom: 5px;
}

.step-status {
    font-size: 0.75rem;
    font-weight: 600;
    color: #3182ce;
    margin-bottom: 5px;
}

.step-date {
    font-size: 0.75rem;
    color: #718096;
}

.step-location {
    font-size: 0.75rem;
    color: #718096;
}

/* Step states */
.delivery-step.completed .step-icon {
    background-color: #48bb78;
    border-color: #48bb78;
    color: #ffffff;
}

.delivery-step.current .step-icon {
    background-color: #ffffff;
    border-color: #3182ce;
    color: #3182ce;
    box-shadow: 0 0 0 4px rgba(49, 130, 206, 0.2);
}

/* Custom step styling */
.delivery-step.custom .step-icon {
    background-color: #f0f4ff;
    border-color: #3182ce;
}

.delivery-step.custom.current .step-icon {
    background-color: #3182ce;
    color: #ffffff;
    box-shadow: 0 0 0 4px rgba(49, 130, 206, 0.2);
}

.tracking-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 25px;
}

.back-link {
    color: #333333;
    text-decoration: none;
    font-size: 0.9rem;
    display: flex;
    align-items: center;
    gap: 5px;
    transition: color 0.2s;
}

.back-link:hover {
    color: #555555;
}

/* Shipment Results Styling */
.shipment-details {
    background-color: #FFFFFF;
    border: 1px solid #E2E8F0;
    border-radius: 8px;
    padding: 0;
    box-shadow: 0 1px 3px rgba(0,0,0,0.05);
}

.shipment-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0;
    padding: 20px;
    border-bottom: 1px solid #E2E8F0;
    background-color: #f8f9fa;
    border-radius: 8px 8px 0 0;
}

.shipment-tracking {
    display: flex;
    flex-direction: column;
}

.tracking-label {
    font-size: 0.75rem;
    color: #718096;
    margin-bottom: 5px;
}

.tracking-value {
    font-size: 1.25rem;
    font-weight: 600;
    color: #1A202C;
}

.shipment-status {
    display: inline-block;
    padding: 6px 12px;
    border-radius: 9999px;
    font-size: 0.875rem;
    font-weight: 500;
}

.status-delivered {
    background-color: #F0FFF4;
    color: #2F855A;
}

.status-in-transit {
    background-color: #EBF8FF;
    color: #2B6CB0;
}

.status-pending {
    background-color: #FFFAF0;
    color: #C05621;
}

.status-delayed {
    background-color: #FFF5F5;
    color: #C53030;
}

.status-customs {
    background-color: #F0F4FF;
    color: #3182CE;
}

.shipment-info {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 0;
    padding: 20px;
    border-bottom: 1px solid #E2E8F0;
}

.info-column {
    padding: 0 10px;
}

.info-section {
    margin-bottom: 20px;
}

.section-title {
    font-size: 1rem;
    font-weight: 600;
    color: #1A202C;
    margin-bottom: 15px;
    padding-bottom: 5px;
    border-bottom: 1px solid #E2E8F0;
}

.info-group {
    margin-bottom: 15px;
}

.info-label {
    font-size: 0.75rem;
    text-transform: uppercase;
    color: #718096;
    margin-bottom: 5px;
}

.info-value {
    font-size: 0.875rem;
    color: #1A202C;
    font-weight: 500;
}

.shipment-details-section {
    padding: 20px;
    border-bottom: 1px solid #E2E8F0;
}

.details-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
}

/* Timeline Styling */
.shipment-timeline {
    padding: 20px;
    position: relative;
    padding-left: 50px;
}

.timeline-track {
    position: absolute;
    left: 30px;
    top: 60px;
    bottom: 20px;
    width: 2px;
    background-color: #E2E8F0;
}

.timeline-event {
    position: relative;
    margin-bottom: 25px;
    padding-bottom: 5px;
}

.timeline-event:last-child {
    margin-bottom: 0;
    padding-bottom: 0;
}

.event-dot {
    position: absolute;
    left: -30px;
    top: 0;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background-color: #FFFFFF;
    border: 2px solid #CBD5E0;
    z-index: 1;
}

.event-dot.active {
    border-color: #333333;
    background-color: #333333;
}

.event-content {
    padding-left: 10px;
}

.event-date {
    font-size: 0.75rem;
    color: #718096;
    margin-bottom: 5px;
}

.event-title {
    font-size: 0.875rem;
    font-weight: 600;
    color: #1A202C;
    margin-bottom: 5px;
}

.event-location {
    font-size: 0.75rem;
    color: #718096;
    margin-bottom: 5px;
}

.event-message {
    font-size: 0.75rem;
    color: #718096;
    font-style: italic;
}

.no-events {
    color: #718096;
    font-style: italic;
    margin-top: 15px;
}

/* Tracking Link */
.tracking-link {
    margin-top: 20px;
    text-align: center;
}

.view-details-link {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 10px 20px;
    background-color: #333333;
    color: #ffffff;
    text-decoration: none;
    border-radius: 4px;
    font-size: 0.9rem;
    transition: background-color 0.2s;
}

.view-details-link:hover {
    background-color: #555555;
}
