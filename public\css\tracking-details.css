/**
 * Tracking Details Page Styles
 * Contains styles for timeline animations, status charts, and detailed tracking view
 */

/* Timeline Animation Styles */
.shipment-timeline {
    position: relative;
    padding: 30px 20px;
    margin-top: 20px;
}

.timeline-track {
    position: absolute;
    left: 30px;
    top: 60px;
    bottom: 30px;
    width: 3px;
    background-color: #e2e8f0;
    z-index: 1;
}

.timeline-event {
    position: relative;
    margin-bottom: 30px;
    padding-left: 50px;
    opacity: 0;
    transform: translateY(20px);
    transition: opacity 0.5s ease, transform 0.5s ease;
}

.timeline-event.animate-in {
    opacity: 1;
    transform: translateY(0);
}

.event-dot {
    position: absolute;
    left: 20px;
    top: 0;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background-color: #fff;
    border: 3px solid #cbd5e0;
    z-index: 2;
    transition: all 0.3s ease;
}

.event-dot.active {
    border-color: #4c51bf;
    background-color: #4c51bf;
    box-shadow: 0 0 0 4px rgba(76, 81, 191, 0.2);
}

.event-dot.animate-pulse {
    animation: pulse 1.5s infinite;
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(76, 81, 191, 0.4);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(76, 81, 191, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(76, 81, 191, 0);
    }
}

.timeline-connection {
    position: absolute;
    width: 3px;
    background-color: #4c51bf;
    opacity: 0;
    transition: height 0.5s ease, opacity 0.5s ease;
    z-index: 1;
}

.event-content {
    background-color: #f8fafc;
    border-radius: 8px;
    padding: 15px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    border: 1px solid #e2e8f0;
}

.event-date {
    font-size: 0.75rem;
    color: #718096;
    margin-bottom: 5px;
}

.event-title {
    font-size: 1rem;
    font-weight: 600;
    color: #1a202c;
    margin-bottom: 8px;
}

.event-location {
    font-size: 0.875rem;
    color: #4a5568;
    margin-bottom: 8px;
    display: flex;
    align-items: center;
    gap: 5px;
}

.event-message {
    font-size: 0.875rem;
    color: #718096;
    font-style: italic;
}

/* Status Chart Styles */
.shipment-status-section {
    margin-top: 30px;
    padding: 20px;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    border: 1px solid #e2e8f0;
}

.section-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: #1a202c;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid #e2e8f0;
}

.status-chart {
    display: flex;
    justify-content: space-between;
    align-items: flex-end;
    height: 200px;
    margin-top: 20px;
}

.chart-column {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    margin: 0 5px;
}

.chart-bar-container {
    width: 100%;
    height: 180px;
    display: flex;
    align-items: flex-end;
    justify-content: center;
}

.chart-bar {
    width: 30px;
    background-color: #4c51bf;
    border-radius: 4px 4px 0 0;
    position: relative;
    opacity: 0;
    transform: scaleY(0);
    transform-origin: bottom;
    transition: opacity 0.5s ease, transform 0.5s ease;
}

.bar-value {
    position: absolute;
    top: -25px;
    left: 50%;
    transform: translateX(-50%);
    font-size: 0.75rem;
    color: #4a5568;
    font-weight: 600;
}

.chart-label {
    margin-top: 10px;
    font-size: 0.75rem;
    color: #718096;
}

/* Delivery Route Map */
.delivery-map-section {
    margin-top: 30px;
    padding: 20px;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    border: 1px solid #e2e8f0;
}

.route-map-placeholder {
    height: 150px;
    background-color: #f8fafc;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 40px;
    margin-top: 20px;
}

.map-point {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 5px;
    z-index: 2;
}

.map-point i {
    font-size: 1.5rem;
}

.map-point span {
    font-size: 0.75rem;
    color: #4a5568;
}

.map-point.origin i {
    color: #38a169;
}

.map-point.current i {
    color: #4c51bf;
}

.map-point.destination i {
    color: #e53e3e;
}

.map-route-line {
    flex-grow: 1;
    height: 3px;
    background: linear-gradient(90deg, #38a169, #4c51bf, #e53e3e);
    position: relative;
}

.map-route-line::before {
    content: '';
    position: absolute;
    top: -3px;
    left: 0;
    width: 70%;
    height: 9px;
    background-color: rgba(76, 81, 191, 0.2);
    border-radius: 4px;
}

/* Shipment Details Card */
.shipment-details-card {
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    border: 1px solid #e2e8f0;
    margin-top: 30px;
    overflow: hidden;
}

.card-header {
    padding: 15px 20px;
    background-color: #f8fafc;
    border-bottom: 1px solid #e2e8f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.card-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: #1a202c;
}

.card-body {
    padding: 20px;
}

.details-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.detail-item {
    margin-bottom: 15px;
}

.detail-label {
    font-size: 0.75rem;
    text-transform: uppercase;
    color: #718096;
    margin-bottom: 5px;
}

.detail-value {
    font-size: 0.875rem;
    color: #1a202c;
    font-weight: 500;
}

/* Status Badge Styles */
.status-badge {
    display: inline-flex;
    align-items: center;
    padding: 6px 12px;
    border-radius: 9999px;
    font-size: 0.875rem;
    font-weight: 500;
    gap: 5px;
}

.status-badge i {
    font-size: 0.75rem;
}

.status-delivered {
    background-color: #f0fff4;
    color: #2f855a;
}

.status-in-transit {
    background-color: #ebf8ff;
    color: #2b6cb0;
}

.status-pending {
    background-color: #fffaf0;
    color: #c05621;
}

.status-delayed {
    background-color: #fff5f5;
    color: #c53030;
}

/* View Full Details Button */
.view-details-button {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 12px 24px;
    background-color: #333333;
    color: #ffffff;
    border: none;
    border-radius: 6px;
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.2s ease;
    margin-top: 20px;
}

.view-details-button:hover {
    background-color: #555555;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .timeline-event {
        padding-left: 40px;
    }
    
    .event-dot {
        left: 10px;
    }
    
    .timeline-track {
        left: 20px;
    }
    
    .status-chart {
        overflow-x: auto;
        padding-bottom: 10px;
    }
    
    .chart-column {
        min-width: 60px;
    }
}

@media (max-width: 480px) {
    .details-grid {
        grid-template-columns: 1fr;
    }
    
    .route-map-placeholder {
        padding: 0 20px;
    }
    
    .map-point i {
        font-size: 1.25rem;
    }
    
    .map-point span {
        font-size: 0.7rem;
    }
}
