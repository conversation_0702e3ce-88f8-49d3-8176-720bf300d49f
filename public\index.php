<?php

use App\Core\App;

// Define BASE_PATH relative to this file (public/index.php -> go up one level)
define('BASE_PATH', dirname(__DIR__));

// Define other paths relative to BASE_PATH
define('VIEWS_PATH', BASE_PATH . '/src/Views');
define('DEBUG_PATH', BASE_PATH . '/debug');

// Create debug directory if it doesn't exist
if (!is_dir(DEBUG_PATH)) {
    mkdir(DEBUG_PATH, 0777, true);
}

// Register Composer autoloader
require_once BASE_PATH . '/vendor/autoload.php';

// Get the application instance (which also bootstraps it)
$app = App::getInstance();

// Get request URI and Method
$requestUri = $_SERVER['REQUEST_URI'] ?? '/';
$requestMethod = $_SERVER['REQUEST_METHOD'] ?? 'GET';

// Debug log incoming request
$logMessage = sprintf(
    "[%s] Incoming Request - URI: %s, Method: %s\n",
    date('Y-m-d H:i:s'),
    $requestUri,
    $requestMethod
);
error_log($logMessage, 3, DEBUG_PATH . '/app.log');

// Run the application
$app->run($requestUri, $requestMethod);
