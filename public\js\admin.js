/**
 * Admin Panel JavaScript
 */

document.addEventListener('DOMContentLoaded', function() {
    // Mobile sidebar toggle
    const sidebarToggle = document.getElementById('sidebar-toggle');
    const sidebar = document.querySelector('.sidebar');
    
    if (sidebarToggle && sidebar) {
        sidebarToggle.addEventListener('click', function() {
            sidebar.classList.toggle('active');
        });
    }
    
    // Auto-hide alerts after 5 seconds
    const alerts = document.querySelectorAll('.alert');
    if (alerts.length > 0) {
        setTimeout(function() {
            alerts.forEach(function(alert) {
                alert.style.opacity = '0';
                alert.style.transition = 'opacity 0.5s';
                setTimeout(function() {
                    alert.style.display = 'none';
                }, 500);
            });
        }, 5000);
    }
    
    // Confirm delete actions
    const deleteButtons = document.querySelectorAll('.delete-confirm');
    deleteButtons.forEach(function(button) {
        button.addEventListener('click', function(e) {
            if (!confirm('Are you sure you want to delete this item? This action cannot be undone.')) {
                e.preventDefault();
            }
        });
    });
    
    // Initialize any date pickers
    const datePickers = document.querySelectorAll('.date-picker');
    if (datePickers.length > 0 && typeof flatpickr !== 'undefined') {
        datePickers.forEach(function(picker) {
            flatpickr(picker, {
                dateFormat: 'Y-m-d',
                allowInput: true
            });
        });
    }
    
    // Initialize any charts
    const chartElements = document.querySelectorAll('.chart-container');
    if (chartElements.length > 0 && typeof Chart !== 'undefined') {
        initializeCharts();
    }
});

/**
 * Initialize dashboard charts
 */
function initializeCharts() {
    // Shipment Status Chart
    const statusChartEl = document.getElementById('shipment-status-chart');
    if (statusChartEl) {
        const statusChart = new Chart(statusChartEl, {
            type: 'doughnut',
            data: {
                labels: ['Pending', 'In Transit', 'Delivered', 'Delayed', 'Cancelled'],
                datasets: [{
                    data: [12, 19, 8, 5, 2],
                    backgroundColor: [
                        '#f39c12', // warning - pending
                        '#3498db', // primary - in transit
                        '#2ecc71', // success - delivered
                        '#e74c3c', // danger - delayed
                        '#95a5a6'  // muted - cancelled
                    ]
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });
    }
    
    // Shipment Timeline Chart
    const timelineChartEl = document.getElementById('shipment-timeline-chart');
    if (timelineChartEl) {
        const timelineChart = new Chart(timelineChartEl, {
            type: 'line',
            data: {
                labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
                datasets: [{
                    label: 'Shipments',
                    data: [65, 59, 80, 81, 56, 55],
                    fill: false,
                    borderColor: '#3498db',
                    tension: 0.1
                }]
            },
            options: {
                responsive: true,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
    }
}

/**
 * Toggle password visibility in forms
 */
function togglePasswordVisibility(inputId) {
    const passwordInput = document.getElementById(inputId);
    if (passwordInput) {
        if (passwordInput.type === 'password') {
            passwordInput.type = 'text';
        } else {
            passwordInput.type = 'password';
        }
    }
}

/**
 * Handle form submission with AJAX
 */
function submitFormAjax(formId, successCallback, errorCallback) {
    const form = document.getElementById(formId);
    if (!form) return;
    
    form.addEventListener('submit', function(e) {
        e.preventDefault();
        
        const formData = new FormData(form);
        const url = form.getAttribute('action');
        const method = form.getAttribute('method') || 'POST';
        
        fetch(url, {
            method: method,
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (typeof successCallback === 'function') {
                successCallback(data);
            }
        })
        .catch(error => {
            if (typeof errorCallback === 'function') {
                errorCallback(error);
            } else {
                console.error('Form submission error:', error);
            }
        });
    });
}
