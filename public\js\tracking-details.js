/**
 * Tracking Details Page JavaScript
 * Handles timeline animations and shipment status visualization
 */

document.addEventListener('DOMContentLoaded', function() {
    // Initialize timeline animation
    initTimelineAnimation();
    
    // Initialize shipment status chart
    initShipmentStatusChart();
    
    // Handle view full details button
    const viewDetailsBtn = document.getElementById('view-full-details');
    if (viewDetailsBtn) {
        viewDetailsBtn.addEventListener('click', function(e) {
            e.preventDefault();
            const trackingNumber = this.getAttribute('data-tracking');
            if (trackingNumber) {
                window.location.href = `/courier/public/track/${trackingNumber}`;
            }
        });
    }
});

/**
 * Initialize timeline animation with progress indicators
 */
function initTimelineAnimation() {
    const timeline = document.querySelector('.shipment-timeline');
    if (!timeline) return;
    
    // Add animation class to timeline events with a delay
    const events = timeline.querySelectorAll('.timeline-event');
    events.forEach((event, index) => {
        setTimeout(() => {
            event.classList.add('animate-in');
            
            // Animate the dot
            const dot = event.querySelector('.event-dot');
            if (dot) dot.classList.add('animate-pulse');
            
            // Connect the line
            if (index > 0) {
                const prevDot = events[index - 1].querySelector('.event-dot');
                const currentDot = dot;
                if (prevDot && currentDot) {
                    drawConnectionLine(prevDot, currentDot);
                }
            }
        }, 300 * index);
    });
}

/**
 * Draw connection line between timeline dots
 */
function drawConnectionLine(fromDot, toDot) {
    // Implementation for drawing connection line animation
    const fromRect = fromDot.getBoundingClientRect();
    const toRect = toDot.getBoundingClientRect();
    
    const line = document.createElement('div');
    line.className = 'timeline-connection';
    document.body.appendChild(line);
    
    // Position and animate the line
    const fromY = fromRect.top + fromRect.height / 2;
    const toY = toRect.top + toRect.height / 2;
    const height = toY - fromY;
    
    line.style.top = fromY + 'px';
    line.style.left = fromRect.left + fromRect.width / 2 + 'px';
    line.style.height = '0';
    
    // Animate the line growing
    setTimeout(() => {
        line.style.height = height + 'px';
        line.style.opacity = '1';
    }, 50);
}

/**
 * Initialize shipment status chart visualization
 */
function initShipmentStatusChart() {
    const chartContainer = document.getElementById('shipment-status-chart');
    if (!chartContainer) return;
    
    // Sample data - in production this would come from the server
    const statusData = [
        { month: 'Jan', count: 45, color: '#4C51BF' },
        { month: 'Feb', count: 38, color: '#4C51BF' },
        { month: 'Mar', count: 52, color: '#4C51BF' },
        { month: 'Apr', count: 65, color: '#4C51BF' },
        { month: 'May', count: 48, color: '#4C51BF' },
        { month: 'Jun', count: 40, color: '#4C51BF' },
        { month: 'Jul', count: 55, color: '#4C51BF' },
        { month: 'Aug', count: 60, color: '#4C51BF' }
    ];
    
    // Find the maximum value for scaling
    const maxValue = Math.max(...statusData.map(item => item.count));
    
    // Create the chart HTML
    let chartHTML = '<div class="status-chart">';
    
    // Add bars
    statusData.forEach(item => {
        const heightPercentage = (item.count / maxValue) * 100;
        chartHTML += `
            <div class="chart-column">
                <div class="chart-bar-container">
                    <div class="chart-bar" style="height: ${heightPercentage}%; background-color: ${item.color};">
                        <span class="bar-value">${item.count}</span>
                    </div>
                </div>
                <div class="chart-label">${item.month}</div>
            </div>
        `;
    });
    
    chartHTML += '</div>';
    chartContainer.innerHTML = chartHTML;
    
    // Animate the bars
    const bars = chartContainer.querySelectorAll('.chart-bar');
    bars.forEach((bar, index) => {
        setTimeout(() => {
            bar.style.opacity = '1';
            bar.style.transform = 'scaleY(1)';
        }, 100 * index);
    });
}

/**
 * Create delivery route map visualization
 */
function initDeliveryRouteMap() {
    const mapContainer = document.getElementById('delivery-route-map');
    if (!mapContainer) return;
    
    // This would be implemented with a mapping library like Leaflet or Google Maps
    // For now, we'll just add a placeholder
    mapContainer.innerHTML = `
        <div class="route-map-placeholder">
            <div class="map-point origin">
                <i class="fas fa-map-marker-alt"></i>
                <span>Origin</span>
            </div>
            <div class="map-route-line"></div>
            <div class="map-point current">
                <i class="fas fa-truck"></i>
                <span>Current</span>
            </div>
            <div class="map-route-line"></div>
            <div class="map-point destination">
                <i class="fas fa-flag-checkered"></i>
                <span>Destination</span>
            </div>
        </div>
    `;
}
