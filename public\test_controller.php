<?php

// Define BASE_PATH relative to this file (public/index.php -> go up one level)
define('BASE_PATH', dirname(__DIR__));

// Register Composer autoloader
require_once BASE_PATH . '/vendor/autoload.php';

// Try to load the ClientController class
$controllerClass = "App\\Controllers\\Admin\\ClientController";

echo "Checking if class exists: {$controllerClass}\n";
var_dump(class_exists($controllerClass));

if (class_exists($controllerClass)) {
    echo "Class exists!\n";
    $controller = new $controllerClass();
    echo "Controller instance created successfully.\n";
} else {
    echo "Class does not exist.\n";
    
    // Check if the file exists
    $filePath = BASE_PATH . '/src/Controllers/Admin/ClientController.php';
    echo "Checking if file exists: {$filePath}\n";
    var_dump(file_exists($filePath));
    
    if (file_exists($filePath)) {
        echo "File exists but class is not loaded.\n";
        
        // Try to include the file directly
        echo "Trying to include the file directly...\n";
        include_once $filePath;
        
        echo "After include, checking if class exists: {$controllerClass}\n";
        var_dump(class_exists($controllerClass));
    }
}
