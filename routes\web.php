<?php

// Web Routes
// This file defines routes that are typically stateful and use sessions, cookies, etc.

/** @var \App\Core\Router $router */

// Public Routes
$router->get('/', 'PublicController@home');

// Public Tracking Pages Routes
$router->get('/track', 'PublicController@trackPage');
$router->get('/track/{tracking_number}', 'PublicController@trackResult');

// Contact/Support Routes
$router->get('/contact', 'PublicController@contact');
$router->post('/contact/submit', 'PublicController@contactSubmit');

// Admin Routes Group
$router->group(['prefix' => 'admin', /* 'middleware' => 'auth' */], function ($router) {
    /** @var \App\Core\Router $router */

    // Login
    $router->get('/', 'Admin\AuthController@showLoginForm')->name('admin.login.form'); // Redirect to login if not auth
    $router->get('/login', 'Admin\AuthController@showLoginForm')->name('admin.login.form');
    $router->post('/login', 'Admin\AuthController@login')->name('admin.login.submit');
    $router->post('/logout', 'Admin\AuthController@logout')->name('admin.logout'); // Use POST for logout

    // Dashboard (Requires Auth Middleware later)
    $router->get('/dashboard', 'Admin\DashboardController@index')->name('admin.dashboard');

    // Shipments (Requires Auth Middleware later)
    $router->get('/shipments', 'Admin\ShipmentController@index')->name('admin.shipments.list');
    $router->get('/shipments/create', 'Admin\ShipmentController@create')->name('admin.shipments.create');
    $router->post('/shipments/create', 'Admin\ShipmentController@store')->name('admin.shipments.store');
    $router->get('/shipments/edit/{id}', 'Admin\ShipmentController@edit')->name('admin.shipments.edit');
    $router->post('/shipments/edit/{id}', 'Admin\ShipmentController@update')->name('admin.shipments.update');
    $router->post('/shipments/delete/{id}', 'Admin\ShipmentController@delete')->name('admin.shipments.delete'); // Use POST for delete
    $router->get('/shipments/view/{id}', 'Admin\ShipmentController@viewShipment')->name('admin.shipments.view');
    $router->get('/shipments/updateStatus/{id}', 'Admin\ShipmentController@updateStatusForm')->name('admin.shipments.updateStatusForm');
    $router->post('/shipments/updateStatus/{id}', 'Admin\ShipmentController@updateStatus')->name('admin.shipments.updateStatus');
    $router->post('/shipments/add-history/{id}', 'Admin\ShipmentController@updateStatus')->name('admin.shipments.addHistory');
    $router->get('/shipments/getClientAddresses/{clientId}', 'Admin\ShipmentController@getClientAddresses')->name('admin.shipments.getClientAddresses');
    $router->get('/shipments/getEditForm/{id}', 'Admin\ShipmentController@getEditForm')->name('admin.shipments.getEditForm');
    $router->post('/shipments/request-document/{id}', 'Admin\ShipmentController@requestDocument')->name('admin.shipments.requestDocument');
    $router->get('/shipments/invoice/generate', 'Admin\ShipmentController@invoiceGenerate')->name('admin.shipments.invoice');
    $router->get('/shipments/{id}/invoice', 'Admin\ShipmentController@invoiceGenerate')->name('admin.shipments.invoice.id');
    $router->get('/shipments/{id}/label', 'Admin\ShipmentController@generateLabel')->name('admin.shipments.label');

    // Clients (Requires Auth Middleware later)
    $router->get('/clients', 'Admin\ClientController@index')->name('admin.clients.list');
    $router->get('/clients/create', 'Admin\ClientController@create')->name('admin.clients.create');
    $router->post('/clients/create', 'Admin\ClientController@store')->name('admin.clients.store');
    $router->post('/clients/store', 'Admin\ClientController@store')->name('admin.clients.store.alt'); // Alternative route for form compatibility
    $router->get('/clients/edit/{id}', 'Admin\ClientController@edit')->name('admin.clients.edit');
    $router->post('/clients/edit/{id}', 'Admin\ClientController@update')->name('admin.clients.update');
    $router->post('/clients/delete/{id}', 'Admin\ClientController@delete')->name('admin.clients.delete');

    // Status Options (Requires Auth Middleware later)
    $router->get('/status-options', 'Admin\StatusOptionController@index')->name('admin.status-options.list');
    $router->get('/status-options/create', 'Admin\StatusOptionController@create')->name('admin.status-options.create');
    $router->post('/status-options/create', 'Admin\StatusOptionController@store')->name('admin.status-options.store');
    $router->get('/status-options/edit/{id}', 'Admin\StatusOptionController@edit')->name('admin.status-options.edit');
    $router->post('/status-options/edit/{id}', 'Admin\StatusOptionController@update')->name('admin.status-options.update');
    $router->post('/status-options/delete/{id}', 'Admin\StatusOptionController@delete')->name('admin.status-options.delete');

    // Shipment Options (Requires Auth Middleware later)
    $router->get('/shipment-options', 'Admin\ShipmentOptionController@index')->name('admin.shipment-options.list');
    $router->post('/shipment-options', 'Admin\ShipmentOptionController@update')->name('admin.shipment-options.update');

    // Settings (Requires Auth Middleware later)
    $router->get('/settings/general', 'Admin\SettingsController@general')->name('admin.settings.general');
    $router->post('/settings/general', 'Admin\SettingsController@updateGeneral')->name('admin.settings.general.update');
    $router->get('/settings/packages', 'Admin\SettingsController@packages')->name('admin.settings.packages');
    $router->post('/settings/packages', 'Admin\SettingsController@updatePackages')->name('admin.settings.packages.update');
    $router->get('/settings/emails', 'Admin\SettingsController@emails')->name('admin.settings.emails');
    $router->post('/settings/emails', 'Admin\SettingsController@updateEmails')->name('admin.settings.emails.update');
    $router->get('/settings/status', 'Admin\SettingsController@status')->name('admin.settings.status');
    $router->post('/settings/add-status', 'Admin\SettingsController@addStatus')->name('admin.settings.addStatus');
    $router->post('/settings/update-status', 'Admin\SettingsController@updateStatus')->name('admin.settings.updateStatus');
    $router->post('/settings/toggle-status/{id}', 'Admin\SettingsController@toggleStatusActive')->name('admin.settings.toggleStatus');
    $router->get('/settings/shipmentOptions', 'Admin\SettingsController@shipmentOptions')->name('admin.settings.shipmentOptions');
    $router->post('/settings/add-option', 'Admin\SettingsController@addOption')->name('admin.settings.addOption');
    $router->post('/settings/update-option', 'Admin\SettingsController@updateOption')->name('admin.settings.updateOption');
    $router->post('/settings/delete-option', 'Admin\SettingsController@deleteOption')->name('admin.settings.deleteOption');
    $router->get('/settings/document-types', 'Admin\SettingsController@documentTypes')->name('admin.settings.documentTypes');
    $router->get('/settings/appearance', 'Admin\SettingsController@appearance')->name('admin.settings.appearance');
    $router->post('/settings/appearance', 'Admin\SettingsController@updateAppearance')->name('admin.settings.appearance.update');

    // Users (Requires Auth Middleware later)
    $router->get('/users', 'Admin\UserController@index')->name('admin.users.list');
    $router->get('/users/create', 'Admin\UserController@create')->name('admin.users.create');
    $router->post('/users/store', 'Admin\UserController@store')->name('admin.users.store');
    $router->get('/users/edit/{id}', 'Admin\UserController@edit')->name('admin.users.edit');
    $router->post('/users/update/{id}', 'Admin\UserController@update')->name('admin.users.update');
    $router->post('/users/delete/{id}', 'Admin\UserController@delete')->name('admin.users.delete');
});

// API Routes Group (Stateless)
$router->group(['prefix' => 'api'], function ($router) {
    /** @var \App\Core\Router $router */
    $router->post('/track', 'Api\TrackingApiController@track')->name('api.track');
});
