<?php

namespace App\Controllers\Admin;

use App\Core\Controller;
use App\Core\Session;
use App\Models\Address;
use App\Models\Client;

class AddressController extends Controller
{
    /**
     * Check if user is authenticated as admin
     *
     * @return bool
     */
    private function checkAuth(): bool
    {
        if (!Session::get('user') || Session::get('user')['role'] !== 'admin') {
            Session::flash('error', 'You must be logged in as an admin to access this page.');
            $this->redirect('/admin/login');
            return false;
        }
        return true;
    }

    /**
     * List all addresses for a client
     * 
     * @param int $clientId
     */
    public function list($clientId)
    {
        if (!$this->checkAuth()) return;

        try {
            // Get client
            $client = Client::find($clientId);
            if (!$client) {
                Session::flash('error', 'Client not found.');
                return $this->redirect('/admin/clients');
            }

            // Get addresses
            $addresses = Address::findByClientId($clientId);

            return $this->view('admin.addresses.list', [
                'pageTitle' => 'Addresses for ' . $client['name'],
                'client' => $client,
                'addresses' => $addresses
            ], 'admin.layouts.main');
        } catch (\Exception $e) {
            // Log the error
            error_log('Address List Error: ' . $e->getMessage());

            // Show error message
            Session::flash('error', 'Error loading addresses: ' . $e->getMessage());
            return $this->redirect('/admin/clients');
        }
    }

    /**
     * Show form to create a new address
     * 
     * @param int $clientId
     */
    public function create($clientId)
    {
        if (!$this->checkAuth()) return;

        try {
            // Get client
            $client = Client::find($clientId);
            if (!$client) {
                Session::flash('error', 'Client not found.');
                return $this->redirect('/admin/clients');
            }

            return $this->view('admin.addresses.form', [
                'pageTitle' => 'Add Address for ' . $client['name'],
                'client' => $client,
                'address' => null,
                'formAction' => '/admin/clients/' . $clientId . '/addresses/store'
            ], 'admin.layouts.main');
        } catch (\Exception $e) {
            // Log the error
            error_log('Address Create Error: ' . $e->getMessage());

            // Show error message
            Session::flash('error', 'Error loading address form: ' . $e->getMessage());
            return $this->redirect('/admin/clients/' . $clientId . '/addresses');
        }
    }

    /**
     * Store a new address
     * 
     * @param int $clientId
     */
    public function store($clientId)
    {
        if (!$this->checkAuth()) return;

        try {
            // Verify CSRF token
            $this->verifyCsrf();

            // Get client
            $client = Client::find($clientId);
            if (!$client) {
                Session::flash('error', 'Client not found.');
                return $this->redirect('/admin/clients');
            }

            // Validate input
            $validator = $this->validate($this->input(), [
                'name' => 'required',
                'address_line1' => 'required',
                'city' => 'required'
            ]);

            if ($validator->fails()) {
                return $this->redirectBackWithError($validator->errors(), $this->input());
            }

            // Prepare data
            $data = [
                'client_id' => $clientId,
                'name' => $this->input('name'),
                'company' => $this->input('company'),
                'address_line1' => $this->input('address_line1'),
                'address_line2' => $this->input('address_line2'),
                'city' => $this->input('city'),
                'state' => $this->input('state'),
                'postal_code' => $this->input('postal_code'),
                'country' => $this->input('country'),
                'phone' => $this->input('phone'),
                'email' => $this->input('email'),
                'is_default' => $this->input('is_default') ? 1 : 0,
                'type' => $this->input('type', 'shipping')
            ];

            // Create address
            $addressId = Address::create($data);

            if (!$addressId) {
                Session::flash('error', 'Failed to create address.');
                return $this->redirectBackWithError([], $this->input());
            }

            // Success message
            Session::flash('success', 'Address created successfully.');
            return $this->redirect('/admin/clients/' . $clientId . '/addresses');
        } catch (\Exception $e) {
            // Log the error
            error_log('Address Store Error: ' . $e->getMessage());

            // Show error message
            Session::flash('error', 'Error creating address: ' . $e->getMessage());
            return $this->redirectBackWithError([], $this->input());
        }
    }

    /**
     * Show form to edit an address
     * 
     * @param int $clientId
     * @param int $id
     */
    public function edit($clientId, $id)
    {
        if (!$this->checkAuth()) return;

        try {
            // Get client
            $client = Client::find($clientId);
            if (!$client) {
                Session::flash('error', 'Client not found.');
                return $this->redirect('/admin/clients');
            }

            // Get address
            $address = Address::find($id);
            if (!$address || $address['client_id'] != $clientId) {
                Session::flash('error', 'Address not found.');
                return $this->redirect('/admin/clients/' . $clientId . '/addresses');
            }

            return $this->view('admin.addresses.form', [
                'pageTitle' => 'Edit Address for ' . $client['name'],
                'client' => $client,
                'address' => $address,
                'formAction' => '/admin/clients/' . $clientId . '/addresses/update/' . $id
            ], 'admin.layouts.main');
        } catch (\Exception $e) {
            // Log the error
            error_log('Address Edit Error: ' . $e->getMessage());

            // Show error message
            Session::flash('error', 'Error loading address form: ' . $e->getMessage());
            return $this->redirect('/admin/clients/' . $clientId . '/addresses');
        }
    }

    /**
     * Update an address
     * 
     * @param int $clientId
     * @param int $id
     */
    public function update($clientId, $id)
    {
        if (!$this->checkAuth()) return;

        try {
            // Verify CSRF token
            $this->verifyCsrf();

            // Get client
            $client = Client::find($clientId);
            if (!$client) {
                Session::flash('error', 'Client not found.');
                return $this->redirect('/admin/clients');
            }

            // Get address
            $address = Address::find($id);
            if (!$address || $address['client_id'] != $clientId) {
                Session::flash('error', 'Address not found.');
                return $this->redirect('/admin/clients/' . $clientId . '/addresses');
            }

            // Validate input
            $validator = $this->validate($this->input(), [
                'name' => 'required',
                'address_line1' => 'required',
                'city' => 'required'
            ]);

            if ($validator->fails()) {
                return $this->redirectBackWithError($validator->errors(), $this->input());
            }

            // Prepare data
            $data = [
                'client_id' => $clientId,
                'name' => $this->input('name'),
                'company' => $this->input('company'),
                'address_line1' => $this->input('address_line1'),
                'address_line2' => $this->input('address_line2'),
                'city' => $this->input('city'),
                'state' => $this->input('state'),
                'postal_code' => $this->input('postal_code'),
                'country' => $this->input('country'),
                'phone' => $this->input('phone'),
                'email' => $this->input('email'),
                'is_default' => $this->input('is_default') ? 1 : 0,
                'type' => $this->input('type', 'shipping')
            ];

            // Update address
            $success = Address::update($id, $data);

            if (!$success) {
                Session::flash('error', 'Failed to update address.');
                return $this->redirectBackWithError([], $this->input());
            }

            // Success message
            Session::flash('success', 'Address updated successfully.');
            return $this->redirect('/admin/clients/' . $clientId . '/addresses');
        } catch (\Exception $e) {
            // Log the error
            error_log('Address Update Error: ' . $e->getMessage());

            // Show error message
            Session::flash('error', 'Error updating address: ' . $e->getMessage());
            return $this->redirectBackWithError([], $this->input());
        }
    }

    /**
     * Delete an address
     * 
     * @param int $clientId
     * @param int $id
     */
    public function delete($clientId, $id)
    {
        if (!$this->checkAuth()) return;

        try {
            // Verify CSRF token
            $this->verifyCsrf();

            // Get client
            $client = Client::find($clientId);
            if (!$client) {
                Session::flash('error', 'Client not found.');
                return $this->redirect('/admin/clients');
            }

            // Get address
            $address = Address::find($id);
            if (!$address || $address['client_id'] != $clientId) {
                Session::flash('error', 'Address not found.');
                return $this->redirect('/admin/clients/' . $clientId . '/addresses');
            }

            // Delete address
            $success = Address::delete($id);

            if (!$success) {
                Session::flash('error', 'Failed to delete address.');
                return $this->redirect('/admin/clients/' . $clientId . '/addresses');
            }

            // Success message
            Session::flash('success', 'Address deleted successfully.');
            return $this->redirect('/admin/clients/' . $clientId . '/addresses');
        } catch (\Exception $e) {
            // Log the error
            error_log('Address Delete Error: ' . $e->getMessage());

            // Show error message
            Session::flash('error', 'Error deleting address: ' . $e->getMessage());
            return $this->redirect('/admin/clients/' . $clientId . '/addresses');
        }
    }
}
