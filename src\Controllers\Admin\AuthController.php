<?php

namespace App\Controllers\Admin;

use App\Core\Controller;
use App\Core\Session;
use App\Utils\UrlHelper;
// Config is not used anymore since we're using the User model
use App\Models\User;

class AuthController extends Controller
{
    /**
     * Show the admin login form.
     */
    public function showLoginForm()
    {
        // If user is already logged in, redirect to dashboard
        if (Session::has('admin_user_id')) {
            return $this->redirect(UrlHelper::url('/admin/dashboard'));
        }

        try {
            // Get flash data for errors and old input
            $errors = Session::pull('_errors', []);
            $old = Session::pull('_old_input', []);

            // Assuming a view exists at src/Views/admin/auth/login.php
            // Pass null as layout to render only the login form view
            return $this->view('admin.auth.login', [
                'errors' => $errors,
                'old' => $old
            ], null);
        } catch (\Exception $e) {
            return "Admin Login Page (View not found: admin/auth/login.php)";
        }
    }

    /**
     * Handle admin login attempt.
     * Placeholder implementation.
     */
    public function login()
    {
        // 1. Verify CSRF token
        try {
            $this->verifyCsrf();
        } catch (\Exception) {
             // Redirect back with error if CSRF fails
             return $this->redirectBackWithError(['csrf' => 'Invalid security token. Please try again.']);
        }

        // 2. Get input
        $email = $this->input('email');
        $password = $this->input('password');
        $remember = $this->input('remember'); // Checkbox value

        // 3. Validate input
        $validator = $this->validate(
            ['email' => $email, 'password' => $password],
            [
                'email' => 'required|email',
                'password' => 'required'
            ]
        );

        if ($validator->fails()) {
            return $this->redirectBackWithError($validator->errors(), $this->input());
        }

        // 4. Attempt to authenticate the user from database
        $user = User::findByEmail($email);

        if ($user && User::verifyPassword($password, $user['password'])) {
            // 5. Authentication successful: Regenerate session, store user ID
            Session::regenerate(true);
            Session::set('admin_user_id', $user['id']);
            Session::set('admin_user_email', $user['email']);
            Session::set('admin_user_name', $user['name']);
            Session::set('admin_user_role', $user['role']);

            // Update last login timestamp
            User::updateLastLogin($user['id']);

            // TODO: Handle 'remember me' functionality if needed

            // 6. Redirect to admin dashboard
            return $this->redirectWithSuccess(UrlHelper::url('/admin/dashboard'), 'Login successful!');
        } else {
            // 7. Authentication failed: Redirect back with error
            return $this->redirectBackWithError(
                ['auth' => 'Invalid email or password.'],
                ['email' => $email] // Only send back email, not password
            );
        }
        // --- End Authentication Logic Placeholder ---
    }

    /**
     * Handle admin logout.
     */
    public function logout()
    {
         // Verify CSRF token if logout is triggered by a form POST
         // If it's a simple link (GET), CSRF might not be applicable, but POST is safer.
         try {
            $this->verifyCsrf();
        } catch (\Exception) {
             // Redirect back with error if CSRF fails
             return $this->redirectBackWithError(['csrf' => 'Invalid security token. Please try again.']);
        }

        Session::destroy();
        return $this->redirectWithSuccess(UrlHelper::url('/admin/login'), 'You have been logged out.');
    }
}
