<?php

namespace App\Controllers\Admin;

use App\Core\Controller;
use App\Core\Session;
use App\Models\Client;
use App\Models\Setting;

class ClientController extends Controller
{
    /**
     * Check if user is authenticated as admin
     *
     * @return bool
     */
    private function checkAuth(): bool
    {
        // Standardized Auth Check
        if (!Session::has('admin_user_id')) {
             Session::flash('error', 'Please log in to access this section.');
             $this->redirect('/admin/login');
             return false;
        }
        return true;
    }

    /**
     * Display a list of all clients
     */
    public function index()
    {
        if (!$this->checkAuth()) return;

        try {
            // Get all clients
            $clients = Client::all();

            return $this->view('admin.clients.index', [
                'pageTitle' => 'Clients',
                'clients' => $clients
            ], 'admin.layouts.main');
        } catch (\Exception $e) {
            // Log the error
            error_log('Client List Error: ' . $e->getMessage());

            // Show error message
            Session::flash('error', 'Error loading clients: ' . $e->getMessage());
            return $this->redirect('/admin/dashboard');
        }
    }

    /**
     * Show form to create a new client
     */
    public function create()
    {
        if (!$this->checkAuth()) return;

        // Get countries from settings
        $countries = [];
        if (class_exists('\App\Models\Setting')) {
            $countries = Setting::getAsArray('locations', []);
        }

        // If countries are empty, provide some defaults
        if (empty($countries)) {
            $countries = [
                'United States', 'United Kingdom', 'Canada', 'Australia', 'Germany', 'France', 'Spain', 'Italy', 'Japan', 'China'
            ];
        }

        return $this->view('admin.clients.form', [
            'pageTitle' => 'Add New Client',
            'client' => null,
            'countries' => $countries,
            'formAction' => '/admin/clients/store'
        ], 'admin.layouts.main');
    }

    /**
     * Store a new client
     */
    public function store()
    {
        if (!$this->checkAuth()) return;

        try {
            // Verify CSRF token
            $this->verifyCsrf();

            // Validate input
            $validator = $this->validate($this->input(), [
                'name' => 'required',
                'email' => 'required|email',
                'phone' => 'required'
            ]);

            if ($validator->fails()) {
                return $this->redirectBackWithError($validator->errors(), $this->input());
            }

            // Debug input data
            $inputData = $this->input();
            error_log('Client input data: ' . print_r($inputData, true));

            // Create client
            try {
                $clientId = Client::create($inputData);

                if (!$clientId) {
                    Session::flash('error', 'Failed to create client.');
                    return $this->redirectBackWithError([], $inputData);
                }
            } catch (\Exception $e) {
                error_log('Client creation error: ' . $e->getMessage());
                Session::flash('error', 'Error creating client: ' . $e->getMessage());
                return $this->redirectBackWithError([], $inputData);
            }

            // Success message
            Session::flash('success', 'Client created successfully.');
            return $this->redirect('/admin/clients');
        } catch (\Exception $e) {
            // Log the error
            error_log('Client Store Error: ' . $e->getMessage());

            // Show error message
            Session::flash('error', 'Error creating client: ' . $e->getMessage());
            return $this->redirectBackWithError([], $this->input());
        }
    }

    /**
     * Show form to edit a client
     *
     * @param int $id
     */
    public function edit($id)
    {
        if (!$this->checkAuth()) return;

        try {
            // Get client
            $client = Client::find($id);

            if (!$client) {
                Session::flash('error', 'Client not found.');
                return $this->redirect('/admin/clients');
            }

            // Get countries from settings
            $countries = [];
            if (class_exists('\App\Models\Setting')) {
                $countries = Setting::getAsArray('locations', []);
            }

            // If countries are empty, provide some defaults
            if (empty($countries)) {
                $countries = [
                    'United States', 'United Kingdom', 'Canada', 'Australia', 'Germany', 'France', 'Spain', 'Italy', 'Japan', 'China'
                ];
            }

            return $this->view('admin.clients.form', [
                'pageTitle' => 'Edit Client',
                'client' => $client,
                'countries' => $countries,
                'formAction' => '/admin/clients/update/' . $id
            ], 'admin.layouts.main');
        } catch (\Exception $e) {
            // Log the error
            error_log('Client Edit Error: ' . $e->getMessage());

            // Show error message
            Session::flash('error', 'Error loading client: ' . $e->getMessage());
            return $this->redirect('/admin/clients');
        }
    }

    /**
     * Update a client
     *
     * @param int $id
     */
    public function update($id)
    {
        if (!$this->checkAuth()) return;

        try {
            // Verify CSRF token
            $this->verifyCsrf();

            // Get client
            $client = Client::find($id);

            if (!$client) {
                Session::flash('error', 'Client not found.');
                return $this->redirect('/admin/clients');
            }

            // Validate input
            $validator = $this->validate($this->input(), [
                'name' => 'required',
                'email' => 'required|email',
                'phone' => 'required'
            ]);

            if ($validator->fails()) {
                return $this->redirectBackWithError($validator->errors(), $this->input());
            }

            // Update client
            $success = Client::update($id, $this->input());

            if (!$success) {
                Session::flash('error', 'Failed to update client.');
                return $this->redirectBackWithError([], $this->input());
            }

            // Success message
            Session::flash('success', 'Client updated successfully.');
            return $this->redirect('/admin/clients');
        } catch (\Exception $e) {
            // Log the error
            error_log('Client Update Error: ' . $e->getMessage());

            // Show error message
            Session::flash('error', 'Error updating client: ' . $e->getMessage());
            return $this->redirectBackWithError([], $this->input());
        }
    }

    /**
     * Delete a client
     *
     * @param int $id
     */
    public function delete($id)
    {
        if (!$this->checkAuth()) return;

        try {
            // Verify CSRF token
            $this->verifyCsrf();

            // Get client
            $client = Client::find($id);

            if (!$client) {
                Session::flash('error', 'Client not found.');
                return $this->redirect('/admin/clients');
            }

            // Delete client
            $success = Client::delete($id);

            if (!$success) {
                Session::flash('error', 'Failed to delete client.');
                return $this->redirect('/admin/clients');
            }

            // Success message
            Session::flash('success', 'Client deleted successfully.');
            return $this->redirect('/admin/clients');
        } catch (\Exception $e) {
            // Log the error
            error_log('Client Delete Error: ' . $e->getMessage());

            // Show error message
            Session::flash('error', 'Error deleting client: ' . $e->getMessage());
            return $this->redirect('/admin/clients');
        }
    }
}
