<?php

namespace App\Controllers\Admin;

use App\Core\Controller;
use App\Core\Session;
use App\Core\View;
use App\Models\Shipment;

class DashboardController extends Controller
{
    /**
     * Display the admin dashboard.
     * Placeholder implementation. Requires auth middleware later.
     */
    public function index()
    {
        // --- Auth Check Placeholder ---
        // In a real app, middleware would handle this, but for now:
        if (!Session::has('admin_user_id')) {
             Session::flash('error', 'Please log in to access the dashboard.');
             return $this->redirect('/admin/login');
        }
        // --- End Auth Check ---

        try {
            // Get dashboard data from database
            $totalShipments = Shipment::count();
            $statusCounts = Shipment::countByStatus();
            $deliveredToday = count(Shipment::deliveredToday());
            $monthlyShipments = Shipment::countByMonth();

            // Get recent shipments
            $recentShipments = Shipment::all(5);

            // Prepare data for the view
            $data = [
                'pageTitle' => 'Admin Dashboard',
                'userName' => Session::get('admin_user_name', 'Admin'),
                'totalShipments' => $totalShipments,
                'pendingShipments' => $statusCounts['pending'] ?? 0,
                'inTransitShipments' => $statusCounts['in_transit'] ?? 0,
                'deliveredShipments' => $statusCounts['delivered'] ?? 0,
                'deliveredToday' => $deliveredToday,
                'monthlyShipments' => $monthlyShipments,
                'recentShipments' => $recentShipments,
                'statusCounts' => $statusCounts
            ];

            // Render the dashboard view with the data
            return $this->view('admin.dashboard', $data, 'admin.layouts.main');
        } catch (\Exception $e) {
            // Log the error
            error_log('Dashboard Error: ' . $e->getMessage());

            // Show error message
            Session::flash('error', 'Error loading dashboard: ' . $e->getMessage());
            return $this->view('admin.dashboard', [
                'pageTitle' => 'Admin Dashboard',
                'userName' => Session::get('admin_user_name', 'Admin'),
                'error' => $e->getMessage()
            ], 'admin.layouts.main');
        }
    }
}
