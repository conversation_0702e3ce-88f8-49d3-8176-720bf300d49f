<?php

namespace App\Controllers\Admin;

use App\Core\Controller;
use App\Core\Session;
use App\Tools\DiagnosticTool;

// Define ROOT_DIR if not already defined
if (!defined('ROOT_DIR')) {
    define('ROOT_DIR', dirname(dirname(dirname(__DIR__))));
}

class DiagnosticController extends Controller
{
    /**
     * Check if user is authenticated as admin
     *
     * @return bool
     */
    private function checkAuth(): bool
    {
        // Standardized Auth Check
        if (!Session::has('admin_user_id')) {
             Session::flash('error', 'Please log in to access this section.');
             $this->redirect('/admin/login');
             return false;
        }
        return true;
    }

    /**
     * Run diagnostic tests and display results
     */
    public function runTests()
    {
        if (!$this->checkAuth()) return;

        try {
            // Run all diagnostic tests
            $testResults = DiagnosticTool::runAllTests();

            // Format results as HTML
            $resultsHtml = DiagnosticTool::formatResultsAsHtml($testResults);

            return $this->view('admin.diagnostic.results', [
                'pageTitle' => 'Diagnostic Test Results',
                'resultsHtml' => $resultsHtml,
                'testResults' => $testResults
            ], 'admin.layouts.main');
        } catch (\Exception $e) {
            // Log the error
            error_log('Diagnostic Test Error: ' . $e->getMessage());

            // Show error message
            Session::flash('error', 'Error running diagnostic tests: ' . $e->getMessage());
            return $this->redirect('/admin/dashboard');
        }
    }

    /**
     * Run specific diagnostic test
     *
     * @param string $test Test name
     */
    public function runSpecificTest($test)
    {
        if (!$this->checkAuth()) return;

        try {
            // Run specific test
            $method = 'test' . ucfirst($test);

            if (!method_exists(DiagnosticTool::class, $method)) {
                Session::flash('error', "Test '{$test}' not found.");
                return $this->redirect('/admin/diagnostic/run');
            }

            DiagnosticTool::$method();
            $testResults = [
                'results' => DiagnosticTool::getResults(),
                'errors' => DiagnosticTool::getErrors(),
                'warnings' => DiagnosticTool::getWarnings(),
                'info' => DiagnosticTool::getInfo()
            ];

            // Format results as HTML
            $resultsHtml = DiagnosticTool::formatResultsAsHtml($testResults);

            return $this->view('admin.diagnostic.results', [
                'pageTitle' => "Diagnostic Test Results: {$test}",
                'resultsHtml' => $resultsHtml,
                'testResults' => $testResults
            ], 'admin.layouts.main');
        } catch (\Exception $e) {
            // Log the error
            error_log("Diagnostic Test Error ({$test}): " . $e->getMessage());

            // Show error message
            Session::flash('error', "Error running diagnostic test '{$test}': " . $e->getMessage());
            return $this->redirect('/admin/diagnostic/run');
        }
    }

    /**
     * Apply a fix for a specific issue
     *
     * @param string $fix Fix name
     */
    public function applyFix($fix)
    {
        if (!$this->checkAuth()) return;

        try {
            $success = false;
            $message = "Unknown fix type: {$fix}";

            switch ($fix) {
                case 'namespace':
                    // Fix namespace issue in ClientController
                    $controllerFile = ROOT_DIR . '/src/Controllers/Admin/ClientController.php';
                    if (file_exists($controllerFile)) {
                        $content = file_get_contents($controllerFile);
                        $content = str_replace(
                            'namespace App\\Controllers\\Admin;',
                            'namespace App\\Controllers\\Admin;',
                            $content
                        );
                        file_put_contents($controllerFile, $content);
                        $success = true;
                        $message = "Fixed namespace in ClientController.";
                    } else {
                        $message = "ClientController.php file not found.";
                    }
                    break;

                case 'router':
                    // Fix router class to handle namespaces correctly
                    $routerFile = ROOT_DIR . '/src/Core/Router.php';
                    if (file_exists($routerFile)) {
                        $content = file_get_contents($routerFile);
                        $content = str_replace(
                            "// Handle both App\\Controllers and App\\Controllers\\Admin namespaces\n            if (strpos(\$controller, 'Admin\\\\') === 0) {\n                \$controllerClass = \"App\\\\Controllers\\\\{\$controller}\";\n            } else {\n                \$controllerClass = \"App\\\\Controllers\\\\{\$controller}\";\n            }",
                            "// Always use App\\Controllers namespace\n            \$controllerClass = \"App\\\\Controllers\\\\{\$controller}\";",
                            $content
                        );
                        file_put_contents($routerFile, $content);
                        $success = true;
                        $message = "Fixed Router class to handle namespaces correctly.";
                    } else {
                        $message = "Router.php file not found.";
                    }
                    break;

                case 'complete':
                    // Comprehensive fix for client page
                    $success = true;
                    $message = "";

                    // 1. Fix namespace in ClientController
                    $controllerFile = ROOT_DIR . '/src/Controllers/Admin/ClientController.php';
                    if (file_exists($controllerFile)) {
                        $content = file_get_contents($controllerFile);
                        $content = str_replace(
                            'namespace App\\Controllers\\Admin;',
                            'namespace App\\Controllers\\Admin;',
                            $content
                        );
                        file_put_contents($controllerFile, $content);
                        $message .= "Fixed namespace in ClientController. ";
                    } else {
                        $message .= "ClientController.php file not found. ";
                    }

                    // 2. Fix Router class
                    $routerFile = ROOT_DIR . '/src/Core/Router.php';
                    if (file_exists($routerFile)) {
                        $content = file_get_contents($routerFile);
                        $content = str_replace(
                            "// Handle both App\\Controllers and App\\Controllers\\Admin namespaces\n            if (strpos(\$controller, 'Admin\\\\') === 0) {\n                \$controllerClass = \"App\\\\Controllers\\\\{\$controller}\";\n            } else {\n                \$controllerClass = \"App\\\\Controllers\\\\{\$controller}\";\n            }",
                            "// Always use App\\Controllers namespace\n            \$controllerClass = \"App\\\\Controllers\\\\{\$controller}\";",
                            $content
                        );
                        file_put_contents($routerFile, $content);
                        $message .= "Fixed Router class. ";
                    } else {
                        $message .= "Router.php file not found. ";
                    }

                    // 3. Clear any cached data
                    if (function_exists('opcache_reset')) {
                        opcache_reset();
                        $message .= "Cleared opcache. ";
                    }

                    $message .= "Applied comprehensive fix for client page.";
                    break;
            }

            if ($success) {
                Session::flash('success', $message);
            } else {
                Session::flash('error', $message);
            }

            return $this->redirect('/admin/diagnostic/run');
        } catch (\Exception $e) {
            // Log the error
            error_log("Fix Application Error ({$fix}): " . $e->getMessage());

            // Show error message
            Session::flash('error', "Error applying fix '{$fix}': " . $e->getMessage());
            return $this->redirect('/admin/diagnostic/run');
        }
    }
}
