<?php

namespace App\Controllers\Admin;

use App\Core\Controller;
use App\Core\Session;
use App\Core\View;
use App\Models\Setting;
use App\Models\StatusOption;
use App\Models\Client;
use App\Models\Address;

class SettingsController extends Controller
{
    // Placeholder for auth check - apply middleware later
    public function __construct()
    {
        parent::__construct(); // Call parent constructor for request sanitization
        // Add auth check in each method for now
    }

    private function checkAuth(): bool
    {
        if (!Session::has('admin_user_id')) {
             Session::flash('error', 'Please log in to access this section.');
             $this->redirect('/admin/login');
             return false; // Indicate redirection happened
        }
        return true;
    }

    /**
     * Show general settings form.
     */
    public function general()
    {
        if (!$this->checkAuth()) return;

        // Load settings from database
        $settings = Setting::getByGroup('general');

        try {
            return $this->view('admin.settings.general', [
                'pageTitle' => 'General Settings',
                'settings' => $settings
            ], 'admin.layouts.main');
        } catch (\Exception $e) {
            // Log the error
            error_log('General Settings Error: ' . $e->getMessage());

            // Show error message
            Session::flash('error', 'Error loading settings: ' . $e->getMessage());
            return $this->redirect('/admin/dashboard');
        }
    }

    /**
     * Update general settings.
     */
    public function updateGeneral()
    {
        if (!$this->checkAuth()) return;

        try {
            $this->verifyCsrf();
        } catch (\Exception $e) {
             return $this->redirectBackWithError(['csrf' => 'Invalid security token. Please try again.']);
        }

        // Get input data
        $data = $this->input();

        // Update settings
        foreach ($data as $key => $value) {
            // Skip CSRF token
            if ($key === '_csrf_token') {
                continue;
            }

            Setting::set($key, $value, 'general');
        }

        // Redirect with success message
        Session::flash('success', 'Settings updated successfully.');
        return $this->redirect('/admin/settings/general');
    }

    /**
     * Show package settings form.
     */
    public function packages()
    {
        if (!$this->checkAuth()) return;

        // Load settings from database
        $settings = Setting::getByGroup('packages');

        try {
            return $this->view('admin.settings.packages', [
                'pageTitle' => 'Package Settings',
                'settings' => $settings
            ], 'admin.layouts.main');
        } catch (\Exception $e) {
            // Log the error
            error_log('Package Settings Error: ' . $e->getMessage());

            // Show error message
            Session::flash('error', 'Error loading settings: ' . $e->getMessage());
            return $this->redirect('/admin/dashboard');
        }
    }

    /**
     * Update package settings.
     */
    public function updatePackages()
    {
        if (!$this->checkAuth()) return;

        try {
            $this->verifyCsrf();
        } catch (\Exception) {
            return $this->redirectBackWithError(['csrf' => 'Invalid security token. Please try again.']);
        }

        // Get input data
        $data = $this->input();

        // Update settings
        foreach ($data as $key => $value) {
            // Skip CSRF token
            if ($key === '_csrf_token') {
                continue;
            }

            Setting::set($key, $value, 'packages');
        }

        // Redirect with success message
        Session::flash('success', 'Package settings updated successfully.');
        return $this->redirect('/admin/settings/packages');
    }

    /**
     * Show email settings form.
     */
    public function emails()
    {
         if (!$this->checkAuth()) return;

        // Load settings from database
        $settings = Setting::getByGroup('emails');

        try {
            return $this->view('admin.settings.emails', [
                'pageTitle' => 'Email Settings',
                'settings' => $settings
            ], 'admin.layouts.main');
        } catch (\Exception $e) {
            // Log the error
            error_log('Email Settings Error: ' . $e->getMessage());

            // Show error message
            Session::flash('error', 'Error loading settings: ' . $e->getMessage());
            return $this->redirect('/admin/dashboard');
        }
    }

    /**
     * Update email settings.
     */
    public function updateEmails()
    {
        if (!$this->checkAuth()) return;

        try {
            $this->verifyCsrf();
        } catch (\Exception) {
            return $this->redirectBackWithError(['csrf' => 'Invalid security token. Please try again.']);
        }

        // Get input data
        $data = $this->input();

        // Update settings
        foreach ($data as $key => $value) {
            // Skip CSRF token
            if ($key === '_csrf_token') {
                continue;
            }

            // Handle password specially - don't save if it's just asterisks (masked)
            if ($key === 'mail_password' && $value === '********') {
                continue;
            }

            Setting::set($key, $value, 'emails');
        }

        // Redirect with success message
        Session::flash('success', 'Email settings updated successfully.');
        return $this->redirect('/admin/settings/emails');
    }

    /**
     * Show status settings form.
     */
    public function status()
    {
        if (!$this->checkAuth()) return;

        try {
            // Get all status options
            $statusOptions = StatusOption::all();

            return $this->view('admin.settings.status', [
                'pageTitle' => 'Status Settings',
                'statusOptions' => $statusOptions
            ], 'admin.layouts.main');
        } catch (\Exception $e) {
            // Log the error
            error_log('Status Settings Error: ' . $e->getMessage());

            // Show error message
            Session::flash('error', 'Error loading status settings: ' . $e->getMessage());
            return $this->redirect('/admin/dashboard');
        }
    }

    /**
     * Add a new status option
     */
    public function addStatus()
    {
        if (!$this->checkAuth()) return;

        try {
            // Verify CSRF token
            $this->verifyCsrf();

            // Validate input
            $validator = $this->validate($this->input(), [
                'name' => 'required',
                'color' => 'required'
            ]);

            if ($validator->fails()) {
                return $this->redirectBackWithError($validator->errors(), $this->input());
            }

            // Check if status already exists
            $existingStatus = StatusOption::findByName($this->input('name'));
            if ($existingStatus) {
                Session::flash('error', 'A status with this name already exists.');
                return $this->redirectBackWithError([], $this->input());
            }

            // Create new status
            $statusId = StatusOption::create([
                'name' => $this->input('name'),
                'description' => $this->input('description'),
                'color' => $this->input('color'),
                'is_active' => 1
            ]);

            if (!$statusId) {
                Session::flash('error', 'Failed to create status.');
                return $this->redirectBackWithError([], $this->input());
            }

            // Success message
            Session::flash('success', 'Status created successfully.');

            // Redirect to the specified page or default to status settings
            $redirect = $this->input('redirect') ?? '/admin/settings/status';
            return $this->redirect($redirect);
        } catch (\Exception $e) {
            // Log the error
            error_log('Add Status Error: ' . $e->getMessage());

            // Show error message
            Session::flash('error', 'Error creating status: ' . $e->getMessage());
            return $this->redirectBackWithError([], $this->input());
        }
    }

    /**
     * Update an existing status option
     */
    public function updateStatus()
    {
        if (!$this->checkAuth()) return;

        try {
            // Verify CSRF token
            $this->verifyCsrf();

            // Validate input
            $validator = $this->validate($this->input(), [
                'id' => 'required|numeric',
                'name' => 'required',
                'color' => 'required'
            ]);

            if ($validator->fails()) {
                return $this->redirectBackWithError($validator->errors(), $this->input());
            }

            $id = (int)$this->input('id');

            // Check if status exists
            $status = StatusOption::find($id);
            if (!$status) {
                Session::flash('error', 'Status not found.');
                return $this->redirect('/admin/settings/status');
            }

            // Check if name already exists for another status
            $existingStatus = StatusOption::findByName($this->input('name'));
            if ($existingStatus && $existingStatus['id'] != $id) {
                Session::flash('error', 'A status with this name already exists.');
                return $this->redirectBackWithError([], $this->input());
            }

            // Update status
            $success = StatusOption::update($id, [
                'name' => $this->input('name'),
                'description' => $this->input('description'),
                'color' => $this->input('color'),
                'is_active' => $this->input('is_active') ? 1 : 0
            ]);

            if (!$success) {
                Session::flash('error', 'Failed to update status.');
                return $this->redirectBackWithError([], $this->input());
            }

            // Success message
            Session::flash('success', 'Status updated successfully.');
            return $this->redirect('/admin/settings/status');
        } catch (\Exception $e) {
            // Log the error
            error_log('Update Status Error: ' . $e->getMessage());

            // Show error message
            Session::flash('error', 'Error updating status: ' . $e->getMessage());
            return $this->redirectBackWithError([], $this->input());
        }
    }

    /**
     * Toggle status active state
     *
     * @param int $id Status ID
     */
    public function toggleStatusActive($id)
    {
        if (!$this->checkAuth()) return;

        try {
            // Verify CSRF token
            $this->verifyCsrf();

            // Check if status exists
            $status = StatusOption::find($id);
            if (!$status) {
                Session::flash('error', 'Status not found.');
                return $this->redirect('/admin/settings/status');
            }

            // Toggle active state
            $newState = $status['is_active'] ? 0 : 1;
            $success = StatusOption::update($id, ['is_active' => $newState]);

            if (!$success) {
                Session::flash('error', 'Failed to update status.');
                return $this->redirect('/admin/settings/status');
            }

            // Success message
            $message = $newState ? 'Status activated successfully.' : 'Status deactivated successfully.';
            Session::flash('success', $message);
            return $this->redirect('/admin/settings/status');
        } catch (\Exception $e) {
            // Log the error
            error_log('Toggle Status Error: ' . $e->getMessage());

            // Show error message
            Session::flash('error', 'Error updating status: ' . $e->getMessage());
            return $this->redirect('/admin/settings/status');
        }
    }

    /**
     * Initialize shipment options settings if they don't exist
     */
    private function initializeShipmentOptions()
    {
        // Default shipment types
        $shipmentTypes = Setting::get('shipment_types', []);
        if (empty($shipmentTypes)) {
            $shipmentTypes = [
                ['name' => 'Air Freight', 'description' => 'Air freight shipments'],
                ['name' => 'International Shipping', 'description' => 'International shipments'],
                ['name' => 'Truckload', 'description' => 'Full truckload shipments'],
                ['name' => 'Van Move', 'description' => 'Van transportation'],
                ['name' => 'Sea Freight', 'description' => 'Sea freight shipments'],
                ['name' => 'Ground Transport', 'description' => 'Ground transportation'],
                ['name' => 'Next Day', 'description' => 'Next day delivery']
            ];
            Setting::set('shipment_types', $shipmentTypes, 'shipment_options');
        }

        // Default shipment modes
        $shipmentModes = Setting::get('shipment_modes', []);
        if (empty($shipmentModes)) {
            $shipmentModes = [
                ['name' => 'Sea Transport', 'description' => 'Sea transport'],
                ['name' => 'Land Shipping', 'description' => 'Land shipping'],
                ['name' => 'Air Freight', 'description' => 'Air freight'],
                ['name' => 'Ground Transport', 'description' => 'Ground transportation']
            ];
            Setting::set('shipment_modes', $shipmentModes, 'shipment_options');
        }

        // Default payment modes
        $paymentModes = Setting::get('payment_modes', []);
        if (empty($paymentModes)) {
            $paymentModes = [
                ['name' => 'Cash', 'description' => 'Cash payment'],
                ['name' => 'Cheque', 'description' => 'Cheque payment'],
                ['name' => 'Credit Card', 'description' => 'Credit card payment'],
                ['name' => 'Wire Transfer', 'description' => 'Wire transfer payment'],
                ['name' => 'BACS', 'description' => 'BACS payment']
            ];
            Setting::set('payment_modes', $paymentModes, 'shipment_options');
        }

        // Delete old payment_modes setting if it exists in 'shipment' category
        $oldPaymentModes = Setting::get('payment_modes', null, 'shipment');
        if ($oldPaymentModes !== null) {
            // Delete the old setting
            // We can't directly delete, so we'll update it to mark it as deprecated
            Setting::set('payment_modes_deprecated', $oldPaymentModes, 'shipment');
        }

        // Default carriers
        $carriers = Setting::get('carriers', []);
        if (empty($carriers)) {
            $carriers = [
                ['name' => 'DHL', 'description' => 'DHL Express'],
                ['name' => 'USPS', 'description' => 'United States Postal Service'],
                ['name' => 'FedEx', 'description' => 'FedEx'],
                ['name' => 'TreasureSwap', 'description' => 'TreasureSwap Delivery']
            ];
            Setting::set('carriers', $carriers, 'shipment_options');
        }

        // Default locations
        $locations = Setting::get('locations', []);
        if (empty($locations)) {
            $locations = [
                'Afghanistan', 'Albania', 'Algeria', 'American Samoa', 'Andorra', 'Angola', 'Anguilla',
                'Antigua & Barbuda', 'Argentina', 'Armenia', 'Aruba', 'Australia', 'Austria', 'Azerbaijan',
                'Bahamas', 'Bahrain', 'Bangladesh', 'Barbados', 'Belarus', 'Belgium', 'Belize', 'Benin',
                'Bermuda', 'Bhutan', 'Bolivia', 'Bosnia & Herzegovina', 'Botswana', 'Brazil', 'British Virgin Is.',
                'Brunei', 'Bulgaria', 'Burkina Faso', 'Burma', 'Burundi', 'Cambodia', 'Cameroon', 'Canada',
                'Cape Verde', 'Cayman Islands', 'Central African Rep.', 'Chad', 'Chile', 'China', 'Colombia',
                'Comoros', 'Congo, Dem. Rep.', 'Congo, Repub. of the', 'Cook Islands', 'Costa Rica', 'Cote d\'Ivoire',
                'Croatia', 'Cuba', 'Cyprus', 'Czech Republic', 'Denmark', 'Djibouti', 'Dominica', 'Dominican Republic',
                'East Timor', 'Ecuador', 'Egypt', 'El Salvador', 'Equatorial Guinea', 'Eritrea', 'Estonia',
                'Ethiopia', 'Faroe Islands', 'Fiji', 'Finland', 'France', 'French Guiana', 'French Polynesia',
                'Gabon', 'Gambia, The', 'Gaza Strip', 'Georgia', 'Germany', 'Ghana', 'Gibraltar', 'Greece',
                'Greenland', 'Grenada', 'Guadeloupe', 'Guam', 'Guatemala', 'Guernsey', 'Guinea', 'Guinea-Bissau',
                'Guyana', 'Haiti', 'Honduras', 'Hong Kong', 'Hungary', 'Iceland', 'India', 'Indonesia', 'Iran',
                'Iraq', 'Ireland', 'Isle of Man', 'Israel', 'Italy', 'Jamaica', 'Japan', 'Jersey', 'Jordan',
                'Kazakhstan', 'Kenya', 'Kiribati', 'Korea, North', 'Korea, South', 'Kuwait', 'Kyrgyzstan',
                'Laos', 'Latvia', 'Lebanon', 'Lesotho', 'Liberia', 'Libya', 'Liechtenstein', 'Lithuania',
                'Luxembourg', 'Macau', 'Macedonia', 'Madagascar', 'Malawi', 'Malaysia', 'Maldives', 'Mali',
                'Malta', 'Marshall Islands', 'Martinique', 'Mauritania', 'Mauritius', 'Mayotte', 'Mexico',
                'Micronesia, Fed. St.', 'Moldova', 'Monaco', 'Mongolia', 'Montserrat', 'Morocco', 'Mozambique',
                'Namibia', 'Nauru', 'Nepal', 'Netherlands', 'Netherlands Antilles', 'New Caledonia', 'New Zealand',
                'Nicaragua', 'Niger', 'Nigeria', 'N. Mariana Islands', 'Norway', 'Oman', 'Pakistan', 'Palau',
                'Panama', 'Papua New Guinea', 'Paraguay', 'Peru', 'Philippines', 'Poland', 'Portugal', 'Puerto Rico',
                'Qatar', 'Reunion', 'Romania', 'Russia', 'Rwanda', 'Saint Helena', 'Saint Kitts & Nevis', 'Saint Lucia',
                'St Pierre & Miquelon', 'Saint Vincent and the Grenadines', 'Samoa', 'San Marino', 'Sao Tome & Principe',
                'Saudi Arabia', 'Senegal', 'Serbia', 'Seychelles', 'Sierra Leone', 'Singapore', 'Slovakia', 'Slovenia',
                'Solomon Islands', 'Somalia', 'South Africa', 'Spain', 'Sri Lanka', 'Sudan', 'Suriname', 'Swaziland',
                'Sweden', 'Switzerland', 'Syria', 'Taiwan', 'Tajikistan', 'Tanzania', 'Thailand', 'Togo', 'Tonga',
                'Trinidad & Tobago', 'Tunisia', 'Turkey', 'Turkmenistan', 'Turks & Caicos Is', 'Tuvalu', 'Uganda',
                'Ukraine', 'United Arab Emirates', 'United Kingdom', 'United States', 'Uruguay', 'Uzbekistan',
                'Vanuatu', 'Venezuela', 'Vietnam', 'Virgin Islands', 'Wallis and Futuna', 'West Bank', 'Western Sahara',
                'Yemen', 'Zambia', 'Zimbabwe'
            ];
            Setting::set('locations', $locations, 'shipment_options');
        }
    }

    /**
     * Show document types management page.
     */
    public function documentTypes()
    {
        if (!$this->checkAuth()) return;

        try {
            return $this->view('admin.settings.document-types', [
                'pageTitle' => 'Document Types Management'
            ], 'admin.layouts.main');
        } catch (\Exception $e) {
            // Log the error
            error_log('Document Types Settings Error: ' . $e->getMessage());

            // Show error message
            Session::flash('error', 'Error loading document types settings: ' . $e->getMessage());
            return $this->redirect('/admin/dashboard');
        }
    }

    /**
     * Show shipment options settings form.
     */
    public function shipmentOptions()
    {
        if (!$this->checkAuth()) return;

        try {
            // Initialize shipment options settings if they don't exist
            $this->initializeShipmentOptions();

            // Get all shipment options from settings
            $shipmentTypes = Setting::get('shipment_types', []);
            $shipmentModes = Setting::get('shipment_modes', []);
            $paymentModes = Setting::get('payment_modes', []);
            $carriers = Setting::get('carriers', []);
            $locations = Setting::get('locations', []);

            return $this->view('admin.settings.shipment_options', [
                'pageTitle' => 'Shipment Options Settings',
                'shipmentTypes' => $shipmentTypes,
                'shipmentModes' => $shipmentModes,
                'paymentModes' => $paymentModes,
                'carriers' => $carriers,
                'locations' => $locations
            ], 'admin.layouts.main');
        } catch (\Exception $e) {
            // Log the error
            error_log('Shipment Options Settings Error: ' . $e->getMessage());

            // Show error message
            Session::flash('error', 'Error loading shipment options settings: ' . $e->getMessage());
            return $this->redirect('/admin/dashboard');
        }
    }

    /**
     * Add a new option
     */
    public function addOption()
    {
        if (!$this->checkAuth()) return;

        try {
            // Verify CSRF token
            $this->verifyCsrf();

            // Validate input
            $validator = $this->validate($this->input(), [
                'option_type' => 'required',
                'name' => 'required'
            ]);

            if ($validator->fails()) {
                return $this->redirectBackWithError($validator->errors(), $this->input());
            }

            $optionType = $this->input('option_type');
            $name = $this->input('name');
            $description = $this->input('description') ?? '';

            // Get current options
            $settingKey = $this->getSettingKeyFromOptionType($optionType);
            $options = Setting::get($settingKey, []);

            // Check if option already exists
            foreach ($options as $option) {
                if (is_array($option) && isset($option['name'])) {
                    if ($option['name'] === $name) {
                        Session::flash('error', 'An option with this name already exists.');
                        return $this->redirectBackWithError([], $this->input());
                    }
                } elseif ($option === $name) {
                    Session::flash('error', 'An option with this name already exists.');
                    return $this->redirectBackWithError([], $this->input());
                }
            }

            // Add new option
            $options[] = [
                'name' => $name,
                'description' => $description
            ];

            // Save options
            Setting::set($settingKey, $options);

            // Success message
            Session::flash('success', 'Option added successfully.');
            return $this->redirect('/admin/settings/shipmentOptions');
        } catch (\Exception $e) {
            // Log the error
            error_log('Add Option Error: ' . $e->getMessage());

            // Show error message
            Session::flash('error', 'Error adding option: ' . $e->getMessage());
            return $this->redirectBackWithError([], $this->input());
        }
    }

    /**
     * Update an existing option
     */
    public function updateOption()
    {
        if (!$this->checkAuth()) return;

        try {
            // Verify CSRF token
            $this->verifyCsrf();

            // Validate input
            $validator = $this->validate($this->input(), [
                'option_type' => 'required',
                'name' => 'required',
                'old_name' => 'required'
            ]);

            if ($validator->fails()) {
                return $this->redirectBackWithError($validator->errors(), $this->input());
            }

            $optionType = $this->input('option_type');
            $oldName = $this->input('old_name');
            $newName = $this->input('name');
            $description = $this->input('description') ?? '';

            // Get current options
            $settingKey = $this->getSettingKeyFromOptionType($optionType);
            $options = Setting::get($settingKey, []);

            // Check if new name already exists (if different from old name)
            if ($oldName !== $newName) {
                foreach ($options as $option) {
                    if (is_array($option) && isset($option['name'])) {
                        if ($option['name'] === $newName) {
                            Session::flash('error', 'An option with this name already exists.');
                            return $this->redirectBackWithError([], $this->input());
                        }
                    } elseif ($option === $newName) {
                        Session::flash('error', 'An option with this name already exists.');
                        return $this->redirectBackWithError([], $this->input());
                    }
                }
            }

            // Update option
            $updated = false;
            foreach ($options as $key => $option) {
                if (is_array($option) && isset($option['name'])) {
                    if ($option['name'] === $oldName) {
                        $options[$key]['name'] = $newName;
                        $options[$key]['description'] = $description;
                        $updated = true;
                        break;
                    }
                } elseif ($option === $oldName) {
                    $options[$key] = [
                        'name' => $newName,
                        'description' => $description
                    ];
                    $updated = true;
                    break;
                }
            }

            if (!$updated) {
                Session::flash('error', 'Option not found.');
                return $this->redirect('/admin/settings/shipmentOptions');
            }

            // Save options
            Setting::set($settingKey, $options);

            // Success message
            Session::flash('success', 'Option updated successfully.');
            return $this->redirect('/admin/settings/shipmentOptions');
        } catch (\Exception $e) {
            // Log the error
            error_log('Update Option Error: ' . $e->getMessage());

            // Show error message
            Session::flash('error', 'Error updating option: ' . $e->getMessage());
            return $this->redirectBackWithError([], $this->input());
        }
    }

    /**
     * Delete an option
     */
    public function deleteOption()
    {
        if (!$this->checkAuth()) return;

        try {
            // Verify CSRF token
            $this->verifyCsrf();

            // Validate input
            $validator = $this->validate($this->input(), [
                'option_type' => 'required',
                'name' => 'required'
            ]);

            if ($validator->fails()) {
                return $this->redirectBackWithError($validator->errors(), $this->input());
            }

            $optionType = $this->input('option_type');
            $name = $this->input('name');

            // Get current options
            $settingKey = $this->getSettingKeyFromOptionType($optionType);
            $options = Setting::get($settingKey, []);

            // Remove option
            $deleted = false;
            foreach ($options as $key => $option) {
                if (is_array($option) && isset($option['name'])) {
                    if ($option['name'] === $name) {
                        unset($options[$key]);
                        $deleted = true;
                        break;
                    }
                } elseif ($option === $name) {
                    unset($options[$key]);
                    $deleted = true;
                    break;
                }
            }

            if (!$deleted) {
                Session::flash('error', 'Option not found.');
                return $this->redirect('/admin/settings/shipmentOptions');
            }

            // Reindex array
            $options = array_values($options);

            // Save options
            Setting::set($settingKey, $options);

            // Success message
            Session::flash('success', 'Option deleted successfully.');
            return $this->redirect('/admin/settings/shipmentOptions');
        } catch (\Exception $e) {
            // Log the error
            error_log('Delete Option Error: ' . $e->getMessage());

            // Show error message
            Session::flash('error', 'Error deleting option: ' . $e->getMessage());
            return $this->redirect('/admin/settings/shipmentOptions');
        }
    }

    /**
     * Get setting key from option type
     *
     * @param string $optionType
     * @return string
     */
    private function getSettingKeyFromOptionType(string $optionType): string
    {
        switch ($optionType) {
            case 'shipment_type':
                return 'shipment_types';
            case 'shipment_mode':
                return 'shipment_modes';
            case 'payment_mode':
                return 'payment_modes';
            case 'carrier':
                return 'carriers';
            default:
                return $optionType . 's';
        }
    }
}
