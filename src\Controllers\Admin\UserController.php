<?php

namespace App\Controllers\Admin;

use App\Core\Controller;
use App\Core\Session;
use App\Core\View;
use App\Models\User;

class UserController extends Controller
{
    /**
     * Constructor - Check authentication
     */
    public function __construct()
    {
        parent::__construct();
        
        // Check if user is logged in
        if (!Session::has('admin_user_id')) {
            Session::flash('error', 'Please log in to access this section.');
            $this->redirect('/admin/login');
        }
        
        // Check if user is admin
        if (Session::get('admin_user_role') !== 'admin') {
            Session::flash('error', 'You do not have permission to access this section.');
            $this->redirect('/admin/dashboard');
        }
    }
    
    /**
     * Display list of users
     */
    public function index()
    {
        try {
            // Get all users
            $users = User::all();
            
            // Render the view
            return $this->view('admin.users.index', [
                'pageTitle' => 'Manage Users',
                'users' => $users
            ], 'admin.layouts.main');
        } catch (\Exception $e) {
            // Log the error
            error_log('User List Error: ' . $e->getMessage());
            
            // Show error message
            Session::flash('error', 'Error loading users: ' . $e->getMessage());
            return $this->redirect('/admin/dashboard');
        }
    }
    
    /**
     * Display form to create a new user
     */
    public function create()
    {
        try {
            // Render the view
            return $this->view('admin.users.form', [
                'pageTitle' => 'Create User',
                'formAction' => '/admin/users/store',
                'user' => null
            ], 'admin.layouts.main');
        } catch (\Exception $e) {
            // Log the error
            error_log('User Create Form Error: ' . $e->getMessage());
            
            // Show error message
            Session::flash('error', 'Error loading create form: ' . $e->getMessage());
            return $this->redirect('/admin/users');
        }
    }
    
    /**
     * Store a new user
     */
    public function store()
    {
        try {
            // Verify CSRF token
            $this->verifyCsrf();
            
            // Validate input
            $validator = $this->validate($this->input(), [
                'username' => 'required|min:3',
                'email' => 'required|email',
                'password' => 'required|min:6',
                'name' => 'required',
                'role' => 'required'
            ]);
            
            if ($validator->fails()) {
                return $this->redirectBackWithError($validator->errors(), $this->input());
            }
            
            // Check if username or email already exists
            $existingUser = User::findByUsername($this->input('username'));
            if ($existingUser) {
                return $this->redirectBackWithError(['username' => 'Username already exists.'], $this->input());
            }
            
            $existingUser = User::findByEmail($this->input('email'));
            if ($existingUser) {
                return $this->redirectBackWithError(['email' => 'Email already exists.'], $this->input());
            }
            
            // Create the user
            $userId = User::create($this->input());
            
            if (!$userId) {
                Session::flash('error', 'Failed to create user.');
                return $this->redirectBackWithError([], $this->input());
            }
            
            // Redirect to the user list with success message
            Session::flash('success', 'User created successfully.');
            return $this->redirect('/admin/users');
        } catch (\Exception $e) {
            // Log the error
            error_log('User Store Error: ' . $e->getMessage());
            
            // Show error message
            Session::flash('error', 'Error creating user: ' . $e->getMessage());
            return $this->redirectBackWithError([], $this->input());
        }
    }
    
    /**
     * Display form to edit a user
     */
    public function edit($id)
    {
        try {
            // Get the user
            $user = User::find($id);
            
            if (!$user) {
                Session::flash('error', 'User not found.');
                return $this->redirect('/admin/users');
            }
            
            // Render the view
            return $this->view('admin.users.form', [
                'pageTitle' => 'Edit User',
                'formAction' => '/admin/users/update/' . $id,
                'user' => $user
            ], 'admin.layouts.main');
        } catch (\Exception $e) {
            // Log the error
            error_log('User Edit Form Error: ' . $e->getMessage());
            
            // Show error message
            Session::flash('error', 'Error loading edit form: ' . $e->getMessage());
            return $this->redirect('/admin/users');
        }
    }
    
    /**
     * Update a user
     */
    public function update($id)
    {
        try {
            // Verify CSRF token
            $this->verifyCsrf();
            
            // Get the user
            $user = User::find($id);
            
            if (!$user) {
                Session::flash('error', 'User not found.');
                return $this->redirect('/admin/users');
            }
            
            // Validate input
            $rules = [
                'username' => 'required|min:3',
                'email' => 'required|email',
                'name' => 'required',
                'role' => 'required'
            ];
            
            // Only validate password if it's provided
            if (!empty($this->input('password'))) {
                $rules['password'] = 'min:6';
            }
            
            $validator = $this->validate($this->input(), $rules);
            
            if ($validator->fails()) {
                return $this->redirectBackWithError($validator->errors(), $this->input());
            }
            
            // Check if username or email already exists (excluding current user)
            $existingUser = User::findByUsername($this->input('username'));
            if ($existingUser && $existingUser['id'] != $id) {
                return $this->redirectBackWithError(['username' => 'Username already exists.'], $this->input());
            }
            
            $existingUser = User::findByEmail($this->input('email'));
            if ($existingUser && $existingUser['id'] != $id) {
                return $this->redirectBackWithError(['email' => 'Email already exists.'], $this->input());
            }
            
            // Update the user
            $data = $this->input();
            if (empty($data['password'])) {
                unset($data['password']);
            }
            
            $success = User::update($id, $data);
            
            if (!$success) {
                Session::flash('error', 'Failed to update user.');
                return $this->redirectBackWithError([], $this->input());
            }
            
            // Redirect to the user list with success message
            Session::flash('success', 'User updated successfully.');
            return $this->redirect('/admin/users');
        } catch (\Exception $e) {
            // Log the error
            error_log('User Update Error: ' . $e->getMessage());
            
            // Show error message
            Session::flash('error', 'Error updating user: ' . $e->getMessage());
            return $this->redirectBackWithError([], $this->input());
        }
    }
    
    /**
     * Delete a user
     */
    public function delete($id)
    {
        try {
            // Verify CSRF token
            $this->verifyCsrf();
            
            // Get the user
            $user = User::find($id);
            
            if (!$user) {
                Session::flash('error', 'User not found.');
                return $this->redirect('/admin/users');
            }
            
            // Prevent deleting self
            if ($user['id'] == Session::get('admin_user_id')) {
                Session::flash('error', 'You cannot delete your own account.');
                return $this->redirect('/admin/users');
            }
            
            // Delete the user
            $success = User::delete($id);
            
            if (!$success) {
                Session::flash('error', 'Failed to delete user.');
                return $this->redirect('/admin/users');
            }
            
            // Redirect to the user list with success message
            Session::flash('success', 'User deleted successfully.');
            return $this->redirect('/admin/users');
        } catch (\Exception $e) {
            // Log the error
            error_log('User Delete Error: ' . $e->getMessage());
            
            // Show error message
            Session::flash('error', 'Error deleting user: ' . $e->getMessage());
            return $this->redirect('/admin/users');
        }
    }
}
