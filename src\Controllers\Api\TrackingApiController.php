<?php

namespace App\Controllers\Api;

use App\Core\Controller;
use App\Utils\Validator;
use App\Models\Shipment;
use App\Models\ShipmentHistory;

class TrackingApiController extends Controller
{
    /**
     * Handle API request to track a shipment.
     * Returns JSON response.
     */
    public function track()
    {
        // API controllers are typically stateless, no session checks needed here.
        // CSRF protection might not be needed if using token-based auth or simple public API.

        $input = $this->input(); // Get POST data
        $trackingNumber = $input['tracking_number'] ?? null;

        // Validate input
        $validator = $this->validate(['tracking_number' => $trackingNumber], [
            'tracking_number' => 'required|string|max:50'
        ]);

        if ($validator->fails()) {
            return $this->jsonResponse([
                'success' => false,
                'message' => 'Validation failed.',
                'errors' => $validator->errors()
            ], 422); // 422 Unprocessable Entity
        }

        // Find shipment by tracking number
        $shipment = Shipment::findByTrackingNumber($trackingNumber);

        if ($shipment) {
            // Get shipment history
            $history = ShipmentHistory::findByShipmentId($shipment['id']);

            // Get shipment packages
            $packages = [];
            if (class_exists('\App\Models\ShipmentPackage')) {
                $packages = \App\Models\ShipmentPackage::findByShipmentId($shipment['id']);
            }

            // Add package information to shipment data
            if (!empty($packages)) {
                // Get the first package for basic info
                $firstPackage = $packages[0];

                // Add dimensions if available
                if (!empty($firstPackage['length']) && !empty($firstPackage['width']) && !empty($firstPackage['height'])) {
                    $shipment['dimensions'] = $firstPackage['length'] . ' x ' . $firstPackage['width'] . ' x ' . $firstPackage['height'] . ' cm';
                }

                // Add package type
                if (!empty($firstPackage['piece_type'])) {
                    $shipment['package_type'] = $firstPackage['piece_type'];
                }

                // Add package description as items if not already set
                if (empty($shipment['items']) && !empty($firstPackage['description'])) {
                    $shipment['items'] = $firstPackage['description'];
                }

                // Add weight if not already set
                if (empty($shipment['total_weight']) && !empty($firstPackage['weight'])) {
                    $shipment['weight'] = $firstPackage['weight'];
                } else if (!empty($shipment['total_weight'])) {
                    $shipment['weight'] = $shipment['total_weight'];
                }

                // Store all packages
                $shipment['packages'] = $packages;
            }

            // Ensure all shipment details are included
            // Type of shipment
            if (!empty($shipment['shipment_type'])) {
                $shipment['service_type'] = $shipment['shipment_type'];
            }

            // Carrier
            if (!empty($shipment['carrier'])) {
                $shipment['carrier_name'] = $shipment['carrier'];
            }

            // Payment method
            if (!empty($shipment['payment_mode'])) {
                $shipment['payment_method'] = $shipment['payment_mode'];
            }

            // Current location
            if (!empty($shipment['current_location'])) {
                $shipment['current_location_name'] = $shipment['current_location'];
            } else {
                // If no current location, use the latest history entry location
                if (!empty($history)) {
                    $latestHistory = $history[0]; // Assuming history is sorted by date_time desc
                    $shipment['current_location_name'] = $latestHistory['location'];
                }
            }

            // Pickup date and time
            if (!empty($shipment['pickup_date'])) {
                $shipment['pickup_date_formatted'] = date('F j, Y', strtotime($shipment['pickup_date']));
            }

            if (!empty($shipment['pickup_time'])) {
                $shipment['pickup_time_formatted'] = date('g:i A', strtotime($shipment['pickup_time']));
            }

            // Departure date
            if (!empty($shipment['departure_date'])) {
                $shipment['departure_date_formatted'] = date('F j, Y', strtotime($shipment['departure_date']));
            }

            // Expected delivery date
            if (!empty($shipment['expected_delivery_date'])) {
                $shipment['estimated_delivery'] = date('F j, Y', strtotime($shipment['expected_delivery_date']));
            }

            // Total freight
            if (!empty($shipment['total_freight'])) {
                $shipment['shipping_cost'] = $shipment['total_freight'];
            }

            // Carrier reference number
            if (!empty($shipment['carrier_reference_no'])) {
                $shipment['carrier_reference'] = $shipment['carrier_reference_no'];
            }

            // Format dates for display
            if (!empty($shipment['pickup_date'])) {
                $shipment['pickup_date_formatted'] = date('F j, Y', strtotime($shipment['pickup_date']));
            }

            if (!empty($shipment['expected_delivery_date'])) {
                $shipment['expected_delivery_date_formatted'] = date('F j, Y', strtotime($shipment['expected_delivery_date']));
            }

            // Format history dates
            foreach ($history as &$event) {
                $event['date'] = date('F j, Y', strtotime($event['date_time']));
                $event['time'] = date('g:i A', strtotime($event['date_time']));

                // Include remarks if available
                if (!empty($event['remarks'])) {
                    $event['remarks'] = $event['remarks'];
                }
            }

            $shipment['events'] = $history;

            return $this->jsonResponse([
                'success' => true,
                'data' => $shipment
            ]);
        } else {
            return $this->jsonResponse([
                'success' => false,
                'message' => 'Tracking number not found.'
            ], 404); // 404 Not Found
        }
    }

    /**
     * Helper function to return a JSON response.
     *
     * @param array $data Data to encode.
     * @param int $statusCode HTTP status code.
     * @return string JSON encoded string.
     */
    protected function jsonResponse(array $data, int $statusCode = 200): string
    {
        http_response_code($statusCode);
        header('Content-Type: application/json');
        return json_encode($data);
    }
}
