<?php

namespace App\Controllers;

use App\Core\Controller;
use App\Core\View;
use App\Models\Shipment;
use App\Models\ShipmentHistory;

class PublicController extends Controller
{
    /**
     * Show the homepage
     */
    public function home()
    {
        return View::render('public.home');
    }

    /**
     * Show the tracking page
     */
    public function trackPage()
    {
        return View::render('public.track');
    }

    /**
     * Show the tracking result page
     *
     * @param string $tracking_number
     */
    public function trackResult(string $tracking_number)
    {
        // Find shipment by tracking number
        $shipment = Shipment::findByTrackingNumber($tracking_number);

        if (!$shipment) {
            // Redirect to tracking page with error message
            // In a future enhancement, we could add flash messages
            $this->redirectWithError('/track', 'Tracking number not found. Please check and try again.');
            exit;
        }

        // Get shipment history
        $history = ShipmentHistory::findByShipmentId($shipment['id']);

        // Format history dates for display
        foreach ($history as &$event) {
            $event['date'] = date('F j, Y', strtotime($event['date_time']));
            $event['time'] = date('g:i A', strtotime($event['date_time']));
            $event['status'] = ucwords(str_replace('_', ' ', $event['status']));
        }

        // Add events to shipment data
        $shipment['events'] = $history;

        // Get shipment packages
        $packages = [];
        if (class_exists('\App\Models\ShipmentPackage')) {
            $packages = \App\Models\ShipmentPackage::findByShipmentId($shipment['id']);
        }

        // Format dates for display
        if (!empty($shipment['pickup_date'])) {
            $shipment['pickup_date_formatted'] = date('F j, Y', strtotime($shipment['pickup_date']));
        }

        if (!empty($shipment['expected_delivery_date'])) {
            $shipment['expected_delivery_date_formatted'] = date('F j, Y', strtotime($shipment['expected_delivery_date']));
        }

        // Get client information if available
        $client = null;
        if (!empty($shipment['client_id']) && class_exists('\App\Models\Client')) {
            $client = \App\Models\Client::find($shipment['client_id']);
        }

        // Get employee information if available
        $employee = null;
        if (!empty($shipment['employee_id']) && class_exists('\App\Models\Employee')) {
            $employee = \App\Models\Employee::find($shipment['employee_id']);
        }

        // Pass data to view
        return View::render('public.tracking-details', [
            'shipment' => $shipment,
            'packages' => $packages,
            'client' => $client,
            'employee' => $employee,
            'page_title' => 'Tracking: ' . $tracking_number
        ]);
    }

    /**
     * Show the contact/support page
     */
    public function contact()
    {
        return View::render('public.contact');
    }

    /**
     * Handle contact form submission
     */
    public function contactSubmit()
    {
        // Validate CSRF token
        $this->verifyCsrf();

        // Validate input
        $validator = $this->validate($this->input(), [
            'inquiry_type' => 'required',
            'name' => 'required|min:2',
            'email' => 'required|email',
            'message' => 'required|min:10'
        ]);

        if ($validator->fails()) {
            return $this->redirectBackWithError('Please fill in all required fields correctly.');
        }

        // Here you would typically save to database and/or send email
        // For now, we'll just redirect back with success message
        return $this->redirectBackWithSuccess('Thank you for your message! We will get back to you within 24 hours.');
    }
}
