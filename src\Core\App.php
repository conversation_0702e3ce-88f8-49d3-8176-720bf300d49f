<?php

namespace App\Core;

use Dotenv\Dotenv;
use Exception;
use Throwable; // Import Throwable for catching errors/exceptions

class App
{
    protected static ?App $instance = null;
    protected Router $router;
    protected array $config = [];

    /**
     * Private constructor to enforce singleton pattern.
     */
    private function __construct()
    {
        // Bootstrap the application
        $this->bootstrap();
    }

    /**
     * Get the singleton instance of the App.
     *
     * @return App
     */
    public static function getInstance(): App
    {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Bootstrap the application: load env, config, paths, error handling.
     */
    protected function bootstrap(): void
    {
        try {
            // Define BASE_PATH if not already defined (e.g., by public/index.php)
            if (!defined('BASE_PATH')) {
                // Assume App.php is in src/Core, so go up two levels
                define('BASE_PATH', dirname(__DIR__, 2));
            }

            // Load environment variables
            $dotenv = Dotenv::createImmutable(BASE_PATH);
            $dotenv->safeLoad(); // Use safeLoad to avoid errors if .env doesn't exist

            // Load path definitions
            require_once BASE_PATH . '/config/paths.php';

            // Load configuration
            Config::load(CONFIG_PATH);
            $this->config = Config::all();

            // Set error reporting and display based on config
            error_reporting(Config::get('app.error_reporting', E_ALL));
            ini_set('display_errors', Config::get('app.display_errors', '0'));
            ini_set('log_errors', '1'); // Always log errors
            ini_set('error_log', DEBUG_PATH . '/app.log'); // Ensure debug dir exists and is writable

            // Set default timezone
            date_default_timezone_set(Config::get('app.timezone', 'UTC'));

            // Set custom error and exception handlers
            set_exception_handler([$this, 'handleException']);
            set_error_handler([$this, 'handleError']);

            // Initialize the router - routes will be loaded in run()
            $this->router = new Router();

        } catch (Exception $e) {
            // Handle critical bootstrap errors
            $this->handleBootstrapError($e);
        }
    }

    /**
     * Run the application: load routes and dispatch the request.
     *
     * @param string $requestUri
     * @param string $requestMethod
     * @return void
     */
    public function run(string $requestUri, string $requestMethod): void
    {
        try {
            // Load routes from the defined routes file
            $this->router = Router::load(BASE_PATH . '/routes/web.php');

            // Start the session *after* loading routes but *before* dispatching
            // This ensures session is available for controllers/middleware
            Session::start(); // Use settings from config/session.php if it exists, or defaults

            // Dispatch the request
            echo $this->router->direct($requestUri, $requestMethod);

        } catch (Exception $e) {
            // Catch exceptions during routing/controller execution
            $this->handleException($e);
        } catch (Throwable $e) {
            // Catch PHP 7+ errors
            $this->handleException($e);
        }
    }

    /**
     * Custom exception handler.
     *
     * @param Throwable $exception The exception/error object.
     */
    public function handleException(Throwable $exception): void
    {
        // Log the exception
        $this->logError(
            $exception->getMessage(),
            $exception->getFile(),
            $exception->getLine(),
            $exception->getTraceAsString()
        );

        // Show error page based on debug mode
        http_response_code(500); // Internal Server Error

        if (Config::get('app.debug', false)) {
            // Detailed error for development
            echo "<h1>500 Internal Server Error</h1>";
            echo "<p><b>Exception:</b> " . get_class($exception) . "</p>";
            echo "<p><b>Message:</b> " . htmlspecialchars($exception->getMessage()) . "</p>";
            echo "<p><b>File:</b> " . $exception->getFile() . " on line " . $exception->getLine() . "</p>";
            echo "<h2>Stack Trace:</h2><pre>" . htmlspecialchars($exception->getTraceAsString()) . "</pre>";
        } else {
            // Generic error for production
            // Attempt to load a generic 500 error view
            $viewPath = VIEWS_PATH . "/error/500.php"; // Assuming a 500.php view exists
            if (file_exists($viewPath)) {
                require $viewPath;
            } else {
                echo "<h1>Internal Server Error</h1><p>An unexpected error occurred. Please try again later.</p>";
            }
        }
        exit;
    }

    /**
     * Custom error handler (for non-exception errors like warnings, notices).
     *
     * @param int $errno The error level.
     * @param string $errstr The error message.
     * @param string $errfile The file where the error occurred.
     * @param int $errline The line number where the error occurred.
     * @return bool True if the error was handled, false otherwise.
     */
    public function handleError(int $errno, string $errstr, string $errfile, int $errline): bool
    {
        // Respect error_reporting level set in config/bootstrap
        if (!(error_reporting() & $errno)) {
            return false; // Error reporting is turned off for this type of error
        }

        $this->logError($errstr, $errfile, $errline);

        // In debug mode, you might want to display these errors too,
        // but be careful not to break output if headers are already sent.
        if (Config::get('app.debug', false) && !headers_sent()) {
             echo "<br><b>Error [{$errno}]:</b> {$errstr} in <b>{$errfile}</b> on line <b>{$errline}</b><br>";
        }

        // Don't execute PHP internal error handler
        return true;
    }

    /**
     * Log an error message.
     *
     * @param string $message
     * @param string|null $file
     * @param int|null $line
     * @param string|null $trace
     */
    protected function logError(string $message, ?string $file = null, ?int $line = null, ?string $trace = null): void
    {
        $logMessage = "[" . date('Y-m-d H:i:s') . "] " . $message;
        if ($file) {
            $logMessage .= " in " . $file;
        }
        if ($line) {
            $logMessage .= " on line " . $line;
        }
        if ($trace && Config::get('app.debug', false)) { // Only log trace in debug mode? Or always?
             $logMessage .= "\nStack trace:\n" . $trace;
        }
        $logMessage .= "\n";

        // Use configured log path
        $logPath = DEBUG_PATH . '/app.log';
        error_log($logMessage, 3, $logPath); // 3 means append to file
    }

    /**
     * Handle critical errors during the bootstrap phase.
     *
     * @param Exception $e
     */
    protected function handleBootstrapError(Exception $e): void
    {
        // Try to log the error if possible
        $logPath = defined('DEBUG_PATH') ? DEBUG_PATH . '/app.log' : __DIR__ . '/../../debug/app.log'; // Best guess path
        $logMessage = "[" . date('Y-m-d H:i:s') . "] BOOTSTRAP ERROR: " . $e->getMessage() . " in " . $e->getFile() . " on line " . $e->getLine() . "\n";
        @error_log($logMessage, 3, $logPath); // Use @ to suppress errors if logging fails

        // Display a simple error message
        http_response_code(500);
        echo "<h1>Application Error</h1>";
        echo "<p>A critical error occurred during application startup. Please check the logs for details.</p>";

        // Optionally display details if explicitly allowed (e.g., via a separate env var)
        // if (getenv('SHOW_BOOTSTRAP_ERRORS') === 'true') {
        //     echo "<hr><pre>" . htmlspecialchars($e->__toString()) . "</pre>";
        // }

        exit;
    }

    /**
     * Get the Router instance.
     *
     * @return Router
     */
    public function getRouter(): Router
    {
        return $this->router;
    }

    /**
     * Get a configuration value.
     *
     * @param string $key
     * @param mixed $default
     * @return mixed
     */
    public function getConfig(string $key, mixed $default = null): mixed
    {
        return Config::get($key, $default);
    }
}
