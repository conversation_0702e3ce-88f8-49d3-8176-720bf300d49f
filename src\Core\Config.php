<?php

namespace App\Core;

use Exception;

class Config
{
    private static array $config = [];

    /**
     * Load configuration files from the config directory.
     *
     * @param string $configPath Path to the configuration directory.
     * @return void
     */
    public static function load(string $configPath): void
    {
        if (!is_dir($configPath)) {
            throw new Exception("Configuration directory not found: {$configPath}");
        }

        $files = glob($configPath . '/*.php');

        foreach ($files as $file) {
            $key = basename($file, '.php');
            self::$config[$key] = require $file;
        }
    }

    /**
     * Get a configuration value using dot notation.
     *
     * Example: Config::get('database.default')
     *
     * @param string $key The configuration key (e.g., 'app.name', 'database.connections.mysql.host').
     * @param mixed $default Default value to return if the key is not found.
     * @return mixed The configuration value or the default.
     */
    public static function get(string $key, mixed $default = null): mixed
    {
        $keys = explode('.', $key);
        $value = self::$config;

        foreach ($keys as $k) {
            if (isset($value[$k])) {
                $value = $value[$k];
            } else {
                return $default;
            }
        }

        return $value;
    }

    /**
     * Get all loaded configuration values.
     *
     * @return array
     */
    public static function all(): array
    {
        return self::$config;
    }
}
