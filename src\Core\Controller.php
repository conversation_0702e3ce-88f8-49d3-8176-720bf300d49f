<?php

namespace App\Core;

use App\Utils\Sanitizer;
use App\Utils\Validator;
use App\Utils\UrlHelper;

/**
 * Base Controller Class
 * Provides common functionalities for controllers.
 */
abstract class Controller
{
    protected array $requestData = [];

    public function __construct()
    {
        // Automatically sanitize GET and POST data upon controller instantiation
        $this->requestData['GET'] = Sanitizer::sanitizeArray($_GET);
        $this->requestData['POST'] = Sanitizer::sanitizeArray($_POST);
        // Note: For file uploads ($_FILES), specific handling is needed, not general sanitization.
        // Note: For JSON payloads (php://input), decode and sanitize separately.
    }

    /**
     * Render a view.
     *
     * @param string $view View name (dot notation).
     * @param array $data Data to pass to the view.
     * @param string|null $layout Optional layout file.
     * @return string Rendered HTML.
     */
    protected function view(string $view, array $data = [], ?string $layout = null): string
    {
        // Add flash messages to view data automatically
        // Example: Check for common flash keys
        if (Session::has('success')) {
            $data['flash_success'] = Session::pull('success');
        }
        if (Session::has('error')) {
            $data['flash_error'] = Session::pull('error');
        }
        if (Session::has('info')) {
            $data['flash_info'] = Session::pull('info');
        }
        // Pass validation errors if they exist from a previous redirect
        if (Session::has('_errors')) {
            $data['errors'] = Session::pull('_errors');
        }
        // Pass old input if it exists from a previous redirect
        if (Session::has('_old_input')) {
            $data['old'] = Session::pull('_old_input');
        }


        return View::render($view, $data, $layout);
    }

    /**
     * Get sanitized input data from the request (GET or POST).
     *
     * @param string|null $key The specific key to retrieve. If null, returns all data for the method.
     * @param mixed $default Default value if the key is not found.
     * @param string $method 'POST' or 'GET'. Defaults to 'POST'.
     * @return mixed The sanitized input value or array.
     */
    protected function input(?string $key = null, mixed $default = null, string $method = 'POST'): mixed
    {
        $source = strtoupper($method) === 'GET' ? $this->requestData['GET'] : $this->requestData['POST'];

        if ($key === null) {
            return $source;
        }

        return $source[$key] ?? $default;
    }

    /**
     * Get all sanitized input data from POST and GET.
     * POST data takes precedence over GET data for the same key.
     *
     * @return array
     */
    protected function allInput(): array
    {
        return array_merge($this->requestData['GET'], $this->requestData['POST']);
    }


    /**
     * Validate the given request data against rules.
     *
     * @param array $data Data to validate (usually from input()).
     * @param array $rules Validation rules.
     * @param array $messages Custom error messages.
     * @return Validator The validator instance.
     */
    protected function validate(array $data, array $rules, array $messages = []): Validator
    {
        return Validator::make($data, $rules, $messages);
    }

    /**
     * Redirect to a given path.
     *
     * @param string $path Relative path or absolute URL.
     * @param int $statusCode HTTP status code.
     */
    protected function redirect(string $path, int $statusCode = 302): void
    {
        UrlHelper::redirect($path, $statusCode);
    }

    /**
     * Redirect back to the previous page with optional data.
     * Stores errors and old input in the session for display after redirect.
     *
     * @param array $errors Validation errors.
     * @param array $input Old input data.
     */
    protected function redirectBackWithError(array $errors = [], array $input = []): void
    {
        if (!empty($errors)) {
            Session::flash('_errors', $errors);
        }
        if (!empty($input)) {
             // Use original (unsanitized?) input if needed, or the sanitized version
            Session::flash('_old_input', $input);
        }

        $referrer = $_SERVER['HTTP_REFERER'] ?? '/'; // Fallback to root
        $this->redirect($referrer);
    }

     /**
     * Redirect to a path with a success flash message.
     *
     * @param string $path
     * @param string $message
     */
    protected function redirectWithSuccess(string $path, string $message): void
    {
        Session::flash('success', $message);
        $this->redirect($path);
    }

     /**
     * Redirect to a path with an error flash message.
     *
     * @param string $path
     * @param string $message
     */
    protected function redirectWithError(string $path, string $message): void
    {
        Session::flash('error', $message);
        $this->redirect($path);
    }

    /**
     * Verify CSRF token. Call this in POST/PUT/DELETE handlers.
     *
     * @throws \Exception If token is invalid or missing.
     */
    protected function verifyCsrf(): void
    {
        $tokenFromSession = Session::get('_csrf_token');
        $tokenFromInput = $this->input('_csrf_token', null, 'POST'); // Check POST data

        if (!$tokenFromSession || !$tokenFromInput || !hash_equals($tokenFromSession, $tokenFromInput)) {
             Session::flash('error', 'Invalid security token. Please try again.');
             // Decide action: throw exception, redirect back, show error view
             throw new \Exception('CSRF token mismatch.');
             // Or redirect back:
             // $this->redirectBackWithError(['csrf' => 'Invalid security token.']);
        }
    }
}
