<?php

namespace App\Core;

class Currency
{
    private static $supportedCurrencies = [
        'USD' => ['symbol' => '$', 'name' => 'US Dollar'],
        'EUR' => ['symbol' => '€', 'name' => 'Euro'],
        'GBP' => ['symbol' => '£', 'name' => 'British Pound'],
        'CAD' => ['symbol' => 'C$', 'name' => 'Canadian Dollar'],
        'AUD' => ['symbol' => 'A$', 'name' => 'Australian Dollar'],
        'JPY' => ['symbol' => '¥', 'name' => 'Japanese Yen'],
        'CHF' => ['symbol' => 'CHF', 'name' => 'Swiss Franc'],
        'SEK' => ['symbol' => 'kr', 'name' => 'Swedish Krona'],
        'NOK' => ['symbol' => 'kr', 'name' => 'Norwegian Krone'],
        'DKK' => ['symbol' => 'kr', 'name' => 'Danish Krone']
    ];

    /**
     * Get all supported currencies
     */
    public static function getSupportedCurrencies()
    {
        return self::$supportedCurrencies;
    }

    /**
     * Get currency symbol
     */
    public static function getSymbol($currencyCode)
    {
        return self::$supportedCurrencies[$currencyCode]['symbol'] ?? $currencyCode;
    }

    /**
     * Get currency name
     */
    public static function getName($currencyCode)
    {
        return self::$supportedCurrencies[$currencyCode]['name'] ?? $currencyCode;
    }

    /**
     * Format amount with currency
     */
    public static function format($amount, $currencyCode = 'EUR', $decimals = 2)
    {
        $symbol = self::getSymbol($currencyCode);
        $formattedAmount = number_format($amount, $decimals);
        
        // For some currencies, symbol goes after the amount
        if (in_array($currencyCode, ['SEK', 'NOK', 'DKK'])) {
            return $formattedAmount . ' ' . $symbol;
        }
        
        return $symbol . $formattedAmount;
    }

    /**
     * Get default currency from settings
     */
    public static function getDefaultCurrency()
    {
        try {
            Database::prepare("SELECT setting_value FROM settings WHERE category = 'currency' AND setting_key = 'default_currency'");
            Database::execute();
            $result = Database::fetch();
            return $result ? $result['setting_value'] : 'EUR';
        } catch (Exception $e) {
            return 'EUR';
        }
    }

    /**
     * Get exchange rate between currencies
     */
    public static function getExchangeRate($fromCurrency, $toCurrency)
    {
        if ($fromCurrency === $toCurrency) {
            return 1.0;
        }

        try {
            Database::prepare("SELECT rate FROM exchange_rates WHERE from_currency = :from AND to_currency = :to");
            Database::bindValue(':from', $fromCurrency);
            Database::bindValue(':to', $toCurrency);
            Database::execute();
            $result = Database::fetch();
            
            if ($result) {
                return floatval($result['rate']);
            }
            
            // Try reverse rate
            Database::prepare("SELECT rate FROM exchange_rates WHERE from_currency = :to AND to_currency = :from");
            Database::bindValue(':from', $fromCurrency);
            Database::bindValue(':to', $toCurrency);
            Database::execute();
            $result = Database::fetch();
            
            if ($result) {
                return 1.0 / floatval($result['rate']);
            }
            
            return 1.0; // Fallback
        } catch (Exception $e) {
            return 1.0;
        }
    }

    /**
     * Convert amount between currencies
     */
    public static function convert($amount, $fromCurrency, $toCurrency)
    {
        $rate = self::getExchangeRate($fromCurrency, $toCurrency);
        return $amount * $rate;
    }

    /**
     * Check if currency is supported
     */
    public static function isSupported($currencyCode)
    {
        return isset(self::$supportedCurrencies[$currencyCode]);
    }

    /**
     * Get currency options for HTML select
     */
    public static function getSelectOptions($selectedCurrency = null)
    {
        $options = '';
        foreach (self::$supportedCurrencies as $code => $info) {
            $selected = ($selectedCurrency === $code) ? 'selected' : '';
            $options .= "<option value=\"{$code}\" {$selected}>{$code} - {$info['name']}</option>";
        }
        return $options;
    }
}
?>
