<?php

namespace App\Core;

use PDO;
use PDOException;
use Exception;

class Database
{
    private static ?PDO $pdo = null;
    private static ?\PDOStatement $statement = null;

    /**
     * Establish a database connection using configuration.
     *
     * @return PDO
     * @throws Exception If connection fails or config is missing.
     */
    public static function connect(): PDO
    {
        if (self::$pdo === null) {
            $config = Config::get('database');
            if (!$config) {
                throw new Exception("Database configuration not loaded.");
            }

            $default = $config['default'];
            $connectionConfig = $config['connections'][$default] ?? null;

            if (!$connectionConfig) {
                throw new Exception("Default database connection configuration ('{$default}') not found.");
            }

            $driver = $connectionConfig['driver'];
            $host = $connectionConfig['host'];
            $port = $connectionConfig['port'];
            $db = $connectionConfig['database'];
            $user = $connectionConfig['username'];
            $pass = $connectionConfig['password'];
            $charset = $connectionConfig['charset'];
            $options = $connectionConfig['options'] ?? [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
            ];

            $dsn = "";
            if ($driver === 'mysql') {
                $dsn = "mysql:host={$host};port={$port};dbname={$db};charset={$charset}";
            } elseif ($driver === 'pgsql') {
                $dsn = "pgsql:host={$host};port={$port};dbname={$db}";
                // Note: charset is often handled differently in pgsql DSN or connection settings
            } else {
                throw new Exception("Unsupported database driver: {$driver}");
            }

            try {
                error_log("Connecting to database: {$dsn}");
                self::$pdo = new PDO($dsn, $user, $pass, $options);
                error_log("Database connection successful");
            } catch (PDOException $e) {
                // Log the error properly in a real application
                error_log("Database connection failed: " . $e->getMessage());
                throw new Exception("Database connection failed: " . $e->getMessage());
            }
        }
        return self::$pdo;
    }

    /**
     * Prepare a SQL statement for execution.
     *
     * @param string $sql The SQL query string.
     * @return \PDOStatement|false
     */
    public static function prepare(string $sql): \PDOStatement|false
    {
        self::$statement = self::connect()->prepare($sql);
        return self::$statement;
    }

    /**
     * Bind a value to a parameter in the prepared statement.
     *
     * @param string|int $param Parameter identifier.
     * @param mixed $value The value to bind.
     * @param int|null $type Explicit data type for the parameter using PDO::PARAM_* constants. Auto-detects if null.
     * @return bool True on success, false on failure.
     */
    public static function bind(string|int $param, mixed $value, ?int $type = null): bool
    {
        if (is_null($type)) {
            switch (true) {
                case is_int($value):
                    $type = PDO::PARAM_INT;
                    break;
                case is_bool($value):
                    $type = PDO::PARAM_BOOL;
                    break;
                case is_null($value):
                    $type = PDO::PARAM_NULL;
                    break;
                default:
                    $type = PDO::PARAM_STR;
            }
        }
        return self::$statement->bindValue($param, $value, $type);
    }

    /**
     * Bind a value to a parameter in the prepared statement.
     * This is an alias for the bind() method for compatibility with PDO's bindValue().
     *
     * @param string|int $param Parameter identifier.
     * @param mixed $value The value to bind.
     * @param int|null $type Explicit data type for the parameter using PDO::PARAM_* constants. Auto-detects if null.
     * @return bool True on success, false on failure.
     */
    public static function bindValue(string|int $param, mixed $value, ?int $type = null): bool
    {
        return self::bind($param, $value, $type);
    }

    /**
     * Execute the prepared statement.
     *
     * @param array|null $params Optional array of parameters to bind and execute.
     * @return bool True on success, false on failure.
     */
    public static function execute(?array $params = null): bool
    {
        if ($params) {
            return self::$statement->execute($params);
        }
        return self::$statement->execute();
    }

    /**
     * Fetch a single row from the result set.
     *
     * @param int $fetchStyle Controls how the next row will be returned to the caller.
     * @return mixed The row, or false if no more rows.
     */
    public static function fetch(int $fetchStyle = PDO::FETCH_ASSOC): mixed
    {
        self::execute();
        return self::$statement->fetch($fetchStyle);
    }

    /**
     * Fetch all rows from the result set.
     *
     * @param int $fetchStyle Controls how the next row will be returned to the caller.
     * @return array An array containing all of the remaining rows in the result set.
     */
    public static function fetchAll(int $fetchStyle = PDO::FETCH_ASSOC): array
    {
        self::execute();
        return self::$statement->fetchAll($fetchStyle);
    }

    /**
     * Get the number of rows affected by the last SQL statement.
     *
     * @return int The number of rows.
     */
    public static function rowCount(): int
    {
        return self::$statement->rowCount();
    }

    /**
     * Get the ID of the last inserted row or sequence value.
     *
     * @param string|null $name Name of the sequence object from which the ID should be returned (for some drivers).
     * @return string|false The ID, or false if the driver does not support this capability.
     */
    public static function lastInsertId(?string $name = null): string|false
    {
        return self::connect()->lastInsertId($name);
    }

    /**
     * Initiates a transaction.
     *
     * @return bool True on success, false on failure.
     */
    public static function beginTransaction(): bool
    {
        return self::connect()->beginTransaction();
    }

    /**
     * Commits a transaction.
     *
     * @return bool True on success, false on failure.
     */
    public static function commit(): bool
    {
        return self::connect()->commit();
    }

    /**
     * Rolls back a transaction.
     *
     * @return bool True on success, false on failure.
     */
    public static function rollBack(): bool
    {
        return self::connect()->rollBack();
    }

    /**
     * Checks if inside a transaction.
     *
     * @return bool True if a transaction is currently active, false otherwise.
     */
    public static function inTransaction(): bool
    {
        return self::connect()->inTransaction();
    }

    /**
     * A convenience method to prepare, bind (optional), execute, and fetch all results.
     *
     * @param string $sql The SQL query.
     * @param array $bindings An array of parameter bindings.
     * @param int $fetchStyle PDO fetch style.
     * @return array The result set.
     */
    public static function queryAll(string $sql, array $bindings = [], int $fetchStyle = PDO::FETCH_ASSOC): array
    {
        self::prepare($sql);
        self::execute($bindings);
        return self::fetchAll($fetchStyle);
    }

     /**
     * A convenience method to prepare, bind (optional), execute, and fetch a single result.
     *
     * @param string $sql The SQL query.
     * @param array $bindings An array of parameter bindings.
     * @param int $fetchStyle PDO fetch style.
     * @return mixed The single row result or false if not found.
     */
    public static function queryOne(string $sql, array $bindings = [], int $fetchStyle = PDO::FETCH_ASSOC): mixed
    {
        self::prepare($sql);
        self::execute($bindings);
        return self::fetch($fetchStyle);
    }

    /**
     * A convenience method to prepare, bind (optional), and execute a statement (INSERT, UPDATE, DELETE).
     * Returns the number of affected rows.
     *
     * @param string $sql The SQL query.
     * @param array $bindings An array of parameter bindings.
     * @return int Number of affected rows.
     */
    public static function executeStatement(string $sql, array $bindings = []): int
    {
        self::prepare($sql);
        self::execute($bindings);
        return self::rowCount();
    }
}
