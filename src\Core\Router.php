<?php

namespace App\Core;

use Exception;

class Router
{
    protected array $routes = [
        'GET' => [],
        'POST' => [],
        'PUT' => [],
        'DELETE' => [],
        // Add other methods as needed (PATCH, OPTIONS, etc.)
    ];

    protected array $middleware = [];
    protected string $prefix = '';
    protected array $namedRoutes = []; // To store named routes [name => uri]
    protected ?string $lastRouteUri = null; // Track the last added route URI for naming
    protected ?string $lastRouteMethod = null; // Track the last added route method for naming

    /**
     * Load route definitions from files.
     *
     * @param string $routesFile Path to the routes file.
     * @return static
     */
    public static function load(string $routesFile): static
    {
        $router = new static;
        if (!file_exists($routesFile)) {
            throw new Exception("Routes file not found: {$routesFile}");
        }
        require $routesFile;
        return $router;
    }

    /**
     * Register a GET route.
     *
     * @param string $uri
     * @param string|array|\Closure $action Controller@method string, callable array, or Closure.
     * @return $this
     */
    public function get(string $uri, string|array|\Closure $action): static
    {
        $this->addRoute('GET', $uri, $action);
        return $this;
    }

    /**
     * Register a POST route.
     *
     * @param string $uri
     * @param string|array|\Closure $action Controller@method string, callable array, or Closure.
     * @return $this
     */
    public function post(string $uri, string|array|\Closure $action): static
    {
        $this->addRoute('POST', $uri, $action);
        return $this;
    }

    /**
     * Register a PUT route.
     *
     * @param string $uri
     * @param string|array|\Closure $action Controller@method string, callable array, or Closure.
     * @return $this
     */
    public function put(string $uri, string|array|\Closure $action): static
    {
        $this->addRoute('PUT', $uri, $action);
        return $this;
    }

    /**
     * Register a DELETE route.
     *
     * @param string $uri
     * @param string|array|\Closure $action Controller@method string, callable array, or Closure.
     * @return $this
     */
    public function delete(string $uri, string|array|\Closure $action): static
    {
        $this->addRoute('DELETE', $uri, $action);
        return $this;
    }

    /**
     * Add a route to the routes array.
     *
     * @param string $method HTTP method.
     * @param string $uri URI pattern.
     * @param string|array|\Closure $action Controller@method string, callable array, or Closure.
     */
    protected function addRoute(string $method, string $uri, string|array|\Closure $action): void
    {
        $uri = rtrim($this->prefix . '/' . ltrim($uri, '/'), '/');
        $uri = $uri ?: '/'; // Handle root URI

        $this->routes[$method][$uri] = [
            'action' => $action,
            'middleware' => $this->middleware, // Apply group middleware
            'name' => null // Placeholder for name
        ];

        // Store last added route info for potential naming
        $this->lastRouteMethod = $method;
        $this->lastRouteUri = $uri;
    }

     /**
     * Assign a name to the last added route.
     *
     * @param string $name The name for the route.
     * @return $this
     */
    public function name(string $name): static
    {
        if ($this->lastRouteMethod && $this->lastRouteUri) {
            // Check if the route exists before assigning name
            if (isset($this->routes[$this->lastRouteMethod][$this->lastRouteUri])) {
                 $this->routes[$this->lastRouteMethod][$this->lastRouteUri]['name'] = $name;
                 $this->namedRoutes[$name] = $this->lastRouteUri; // Store name => uri mapping
            }
            // Reset last route info
            $this->lastRouteMethod = null;
            $this->lastRouteUri = null;
        }
        return $this;
    }

    /**
     * Group routes with a common prefix and/or middleware.
     *
     * @param array $attributes Attributes like 'prefix' or 'middleware'.
     * @param callable $callback A function containing route definitions.
     */
    public function group(array $attributes, callable $callback): void
    {
        $originalPrefix = $this->prefix;
        $originalMiddleware = $this->middleware;

        // Apply prefix
        if (isset($attributes['prefix'])) {
            $this->prefix = rtrim($originalPrefix . '/' . trim($attributes['prefix'], '/'), '/');
        }

        // Apply middleware (merge with existing group middleware)
        if (isset($attributes['middleware'])) {
            $middlewareToAdd = is_array($attributes['middleware']) ? $attributes['middleware'] : [$attributes['middleware']];
            $this->middleware = array_merge($originalMiddleware, $middlewareToAdd);
        }

        // Execute the callback to define routes within the group
        call_user_func($callback, $this);

        // Restore original prefix and middleware
        $this->prefix = $originalPrefix;
        $this->middleware = $originalMiddleware;
    }

    /**
     * Direct the incoming request to the appropriate controller action.
     *
     * @param string $uri The request URI.
     * @param string $method The request method.
     * @return mixed The result of the controller action.
     * @throws Exception If no route matches or controller/method is invalid.
     */
    public function direct(string $uri, string $method)
    {
        $originalUri = $uri; // Keep original for debugging
        $uri = $this->normalizeUri($uri);
        $method = strtoupper($method);

        // --- DEBUGGING ---
        // Ensure the debug directory is writable by the web server
        $logFile = DEBUG_PATH . '/app.log';
        @error_log("Router::direct - Original URI: {$originalUri}, Normalized URI: {$uri}, Method: {$method}\n", 3, $logFile);
        if (isset($this->routes[$method])) {
             @error_log("Router::direct - Available {$method} routes: " . print_r(array_keys($this->routes[$method]), true) . "\n", 3, $logFile);
        } else {
             @error_log("Router::direct - No routes registered for method {$method}.\n", 3, $logFile);
        }
        // --- END DEBUGGING ---

        // Simple direct match first
        if (isset($this->routes[$method][$uri])) {
            @error_log("Router::direct - Direct match found for {$method} {$uri}\n", 3, $logFile); // Debug
            return $this->callAction(
                $this->routes[$method][$uri]['action'],
                [], // No parameters for direct match
                $this->routes[$method][$uri]['middleware'] ?? []
            );
        } else { // Added else for clarity in debugging
             @error_log("Router::direct - No direct match for {$method} {$uri}\n", 3, $logFile); // Debug
        }

        // Check for routes with parameters
        // Make sure $this->routes[$method] exists before iterating
        if (isset($this->routes[$method])) {
            foreach ($this->routes[$method] as $routeUri => $routeDetails) {
                // Convert route URI like /users/{id} to a regex
                $pattern = preg_replace('/\{([a-zA-Z0-9_]+)\}/', '(?P<$1>[^/]+)', $routeUri);
                $pattern = '#^' . $pattern . '$#';

                if (preg_match($pattern, $uri, $matches)) {
                    @error_log("Router::direct - Parameter match found: {$routeUri} for {$method} {$uri}\n", 3, $logFile); // Debug
                    // Extract parameters (remove full match and numeric keys)
                    $parameters = array_filter($matches, 'is_string', ARRAY_FILTER_USE_KEY);
                    return $this->callAction(
                        $routeDetails['action'],
                        $parameters,
                        $routeDetails['middleware'] ?? []
                    );
                }
            }
        } else {
             // This case was already logged at the start
             // @error_log("Router::direct - No routes defined for method {$method}\n", 3, $logFile);
        }

        // No route matched
        @error_log("Router::direct - No route matched at all for {$method} {$originalUri} (normalized: {$uri})\n", 3, $logFile); // Debug
        $this->abort(404);
    }

    /**
     * Normalize the request URI.
     *
     * @param string $uri
     * @return string
     */
    protected function normalizeUri(string $uri): string
    {
        // Get the base path from the APP_URL config, removing the domain part
        $basePath = parse_url(Config::get('app.url', ''), PHP_URL_PATH) ?? '';
        $basePath = rtrim($basePath, '/'); // Remove trailing slash if exists

        $requestPath = parse_url($uri, PHP_URL_PATH) ?? '';

        // Remove the base path from the beginning of the request path
        if ($basePath && str_starts_with($requestPath, $basePath)) {
            $requestPath = substr($requestPath, strlen($basePath));
        }

        // Normalize the remaining path
        $normalizedUri = trim($requestPath, '/');
        return $normalizedUri === '' ? '/' : '/' . $normalizedUri;
    }

    /**
     * Call the controller action associated with the route.
     *
     * @param string|array|\Closure $action Controller@method string, callable array, or Closure.
     * @param array $parameters Route parameters.
     * @param array $middleware Middleware classes/aliases to apply.
     * @return mixed
     * @throws Exception
     */
    protected function callAction(string|array|\Closure $action, array $parameters = [], array $middleware = [])
    {
        // --- Basic Middleware Handling Placeholder ---
        // In a real app, you'd resolve middleware classes and run them here.
        // For simplicity, we'll just check if any exist for now.
        if (!empty($middleware)) {
            // Example: Loop through $middleware, instantiate, and call a 'handle' method.
            // If any middleware returns a response, return it immediately.
            // echo "Applying Middleware: " . implode(', ', $middleware) . "<br>";
        }
        // --- End Middleware Placeholder ---

        if (is_callable($action)) {
            return call_user_func_array($action, $parameters);
        }

        if (is_string($action) && str_contains($action, '@')) {
            [$controller, $method] = explode('@', $action);
            // Always use App\Controllers namespace
            $controllerClass = "App\\Controllers\\{$controller}";

            // Debug information
            error_log("Controller string: {$controller}");
            error_log("Looking for controller class: {$controllerClass}");

            if (!class_exists($controllerClass)) {
                throw new Exception("Controller class {$controllerClass} not found.");
            }

            $controllerInstance = new $controllerClass();

            if (!method_exists($controllerInstance, $method)) {
                throw new Exception("Method {$method} not found on controller {$controllerClass}.");
            }

            return call_user_func_array([$controllerInstance, $method], $parameters);
        }

        throw new Exception("Invalid route action specified.");
    }

    /**
     * Abort the request with a specific HTTP status code.
     *
     * @param int $code HTTP status code (e.g., 404, 403).
     */
    protected function abort(int $code = 404): void
    {
        http_response_code($code);

        // Attempt to load a view for the error code
        $viewPath = VIEWS_PATH . "/error/{$code}.php";
        if (file_exists($viewPath)) {
            require $viewPath;
        } else {
            echo "Error {$code}"; // Fallback message
        }
        exit; // Stop script execution
    }
}
