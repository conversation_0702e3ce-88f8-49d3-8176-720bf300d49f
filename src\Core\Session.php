<?php

namespace App\Core;

class Session
{
    /**
     * Start or resume a secure session.
     *
     * @param array $options Session configuration options.
     * @return bool
     */
    public static function start(array $options = []): bool
    {
        if (session_status() === PHP_SESSION_ACTIVE) {
            return true;
        }

        // Sensible defaults, can be overridden by config/session.php later
        $defaultOptions = [
            'name' => 'ELTA_SESSION', // Custom session name
            'cookie_lifetime' => Config::get('session.lifetime', 7200), // Default 2 hours (in seconds)
            'cookie_path' => '/',
            'cookie_domain' => '', // Set explicitly if needed
            'cookie_secure' => isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on', // Use secure cookies on HTTPS
            'cookie_httponly' => true, // Prevent JS access to cookie
            'cookie_samesite' => 'Lax', // Mitigate CSRF
            'use_strict_mode' => true, // Prevent session fixation via uninitialized session ID
            'use_cookies' => true,
            'use_only_cookies' => true, // Ensure session ID is only passed via cookies
            'gc_maxlifetime' => Config::get('session.lifetime', 7200) + 300, // Garbage collection slightly longer than lifetime
        ];

        $sessionOptions = array_merge($defaultOptions, $options);

        // Apply session settings
        foreach ($sessionOptions as $key => $value) {
            ini_set("session.{$key}", (string)$value);
        }

        // Start the session
        if (session_start()) {
            // Optional: Regenerate ID periodically to enhance security
            // self::regeneratePeriodically();
            return true;
        }

        return false;
    }

    /**
     * Set a session variable.
     *
     * @param string $key
     * @param mixed $value
     */
    public static function set(string $key, mixed $value): void
    {
        self::ensureStarted();
        $_SESSION[$key] = $value;
    }

    /**
     * Get a session variable.
     *
     * @param string $key
     * @param mixed $default Default value if key not found.
     * @return mixed
     */
    public static function get(string $key, mixed $default = null): mixed
    {
        self::ensureStarted();
        return $_SESSION[$key] ?? $default;
    }

    /**
     * Check if a session variable exists.
     *
     * @param string $key
     * @return bool
     */
    public static function has(string $key): bool
    {
        self::ensureStarted();
        return isset($_SESSION[$key]);
    }

    /**
     * Remove a session variable.
     *
     * @param string $key
     */
    public static function forget(string $key): void
    {
        self::ensureStarted();
        unset($_SESSION[$key]);
    }

    /**
     * Get and remove a session variable (flash message).
     *
     * @param string $key
     * @param mixed $default Default value if key not found.
     * @return mixed
     */
    public static function pull(string $key, mixed $default = null): mixed
    {
        $value = self::get($key, $default);
        self::forget($key);
        return $value;
    }

    /**
     * Set a flash message (retrieved once then removed).
     *
     * @param string $key
     * @param mixed $value
     */
    public static function flash(string $key, mixed $value): void
    {
        self::set($key, $value);
        // Optionally mark it for removal on next request if needed,
        // but pull() handles the removal.
    }

    /**
     * Regenerate the session ID.
     *
     * @param bool $deleteOldSession Whether to delete the old session file.
     * @return bool
     */
    public static function regenerate(bool $deleteOldSession = true): bool
    {
        self::ensureStarted();
        return session_regenerate_id($deleteOldSession);
    }

    /**
     * Destroy the current session.
     *
     * @return bool
     */
    public static function destroy(): bool
    {
        if (session_status() !== PHP_SESSION_ACTIVE) {
            return true; // Nothing to destroy
        }

        // Unset all session variables
        $_SESSION = [];

        // Delete the session cookie
        if (ini_get("session.use_cookies")) {
            $params = session_get_cookie_params();
            setcookie(
                session_name(),
                '',
                time() - 42000,
                $params["path"],
                $params["domain"],
                $params["secure"],
                $params["httponly"]
            );
        }

        // Destroy the session
        return session_destroy();
    }

    /**
     * Ensure the session has been started before accessing variables.
     */
    private static function ensureStarted(): void
    {
        if (session_status() !== PHP_SESSION_ACTIVE) {
            // Attempt to start with default settings if not already started
            // This might be too late if headers are sent, but worth a try.
            // Ideally, App::bootstrap() should call Session::start() early.
            self::start();
        }
    }

    /**
     * Optional: Regenerate session ID periodically based on a timestamp.
     * Call this within App::bootstrap or early in request lifecycle.
     *
     * @param int $interval Time in seconds after which to regenerate (e.g., 300 for 5 minutes).
     */
    public static function regeneratePeriodically(int $interval = 300): void
    {
        self::ensureStarted();
        if (!self::has('session_last_regenerate')) {
            self::set('session_last_regenerate', time());
        } elseif (time() - self::get('session_last_regenerate') > $interval) {
            self::regenerate(true);
            self::set('session_last_regenerate', time());
        }
    }
}
