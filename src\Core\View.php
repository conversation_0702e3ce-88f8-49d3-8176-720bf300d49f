<?php

namespace App\Core;

use Exception;

class View
{
    /**
     * Render a view template.
     *
     * @param string $view The view name (e.g., 'admin.dashboard', 'public.track'). Dot notation for subdirectories.
     * @param array $data Data to extract into the view's scope.
     * @param string|null $layout Optional layout file to wrap the view content.
     * @return string The rendered HTML content.
     * @throws Exception If the view file is not found.
     */
    public static function render(string $view, array $data = [], ?string $layout = null): string
    {
        // Convert dot notation to path slashes
        $viewPath = str_replace('.', '/', $view);
        $fullViewPath = VIEWS_PATH . '/' . $viewPath . '.php';

        if (!file_exists($fullViewPath)) {
            throw new Exception("View file not found: {$fullViewPath}");
        }

        // Extract data variables into the current scope
        extract($data);

        // Start output buffering to capture view content
        ob_start();

        // Include the view file
        require $fullViewPath;

        // Get the captured content
        $content = ob_get_clean();

        // If a layout is specified, render the layout with the content
        if ($layout) {
            // Convert layout dot notation
            $layoutPath = str_replace('.', '/', $layout);
            $fullLayoutPath = VIEWS_PATH . '/' . $layoutPath . '.php';

            if (!file_exists($fullLayoutPath)) {
                throw new Exception("Layout file not found: {$fullLayoutPath}");
            }

            // Data should also be available to the layout
            // The $content variable now holds the specific view's output
            ob_start();
            require $fullLayoutPath;
            return ob_get_clean();
        }

        // If no layout, just return the view content
        return $content;
    }

    /**
     * Helper function to safely output data (prevents XSS).
     *
     * @param mixed $data The data to output.
     * @return string Escaped string.
     */
    public static function e(mixed $data): string
    {
        if (is_array($data)) {
            error_log('Array to string conversion in View::e(): ' . print_r($data, true));
            return htmlspecialchars(json_encode($data), ENT_QUOTES, 'UTF-8');
        }
        return htmlspecialchars((string) $data, ENT_QUOTES, 'UTF-8');
    }

    /**
     * Helper function to generate a URL based on the BASE_URL.
     * Uses UrlHelper if available, otherwise basic concatenation.
     *
     * @param string $path Relative path (e.g., '/admin/users', 'css/app.css').
     * @return string Absolute URL.
     */
    public static function url(string $path): string
    {
        if (class_exists(\App\Utils\UrlHelper::class) && method_exists(\App\Utils\UrlHelper::class, 'url')) {
            return \App\Utils\UrlHelper::url($path);
        }
        // Basic fallback
        return rtrim(BASE_URL, '/') . '/' . ltrim($path, '/');
    }

     /**
     * Helper function to generate an asset URL based on the BASE_URL.
     * Uses UrlHelper if available, otherwise basic concatenation.
     *
     * @param string $path Relative path within the public directory (e.g., 'css/admin.css', 'js/app.js', 'assets/logo.png').
     * @return string Absolute URL to the asset.
     */
    public static function asset(string $path): string
    {
         if (class_exists(\App\Utils\UrlHelper::class) && method_exists(\App\Utils\UrlHelper::class, 'assetUrl')) {
            return \App\Utils\UrlHelper::assetUrl($path);
        }
        // Basic fallback - directly under BASE_URL
        return rtrim(BASE_URL, '/') . '/' . ltrim($path, '/');
    }

    // Add more helper functions as needed (e.g., for CSRF tokens, session messages)
    /**
     * Output a CSRF token input field.
     * Assumes CSRF token is stored in session.
     *
     * @return string HTML input field.
     */
    public static function csrfField(): string
    {
        $token = Session::get('_csrf_token', ''); // Assuming token is stored with this key
        if (!$token) {
             // Generate and store token if it doesn't exist for this session
             $token = bin2hex(random_bytes(32));
             Session::set('_csrf_token', $token);
        }
        return '<input type="hidden" name="_csrf_token" value="' . self::e($token) . '">';
    }

    /**
     * Display flash messages.
     *
     * @param string $key The session key for the flash message.
     * @param string $type The type of message (e.g., 'success', 'error', 'info') for CSS class.
     * @return string HTML for the flash message or empty string.
     */
    public static function flashMessage(string $key, string $type = 'info'): string
    {
        if (Session::has($key)) {
            $message = Session::pull($key);
            // Basic alert structure - adapt to your CSS framework
            return '<div class="alert alert-' . self::e($type) . '" role="alert">' . self::e($message) . '</div>';
        }
        return '';
    }
}
