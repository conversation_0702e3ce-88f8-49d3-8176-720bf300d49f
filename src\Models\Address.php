<?php

namespace App\Models;

use App\Core\Database;
use PDO;

class Address
{
    /**
     * Get all addresses
     *
     * @return array
     */
    public static function all(): array
    {
        Database::prepare("SELECT * FROM addresses ORDER BY id DESC");
        Database::execute();
        return Database::fetchAll();
    }
    
    /**
     * Find an address by ID
     *
     * @param int $id
     * @return array|null
     */
    public static function find(int $id): ?array
    {
        Database::prepare("SELECT * FROM addresses WHERE id = :id LIMIT 1");
        Database::bindValue(':id', $id, PDO::PARAM_INT);
        Database::execute();
        $address = Database::fetch();
        
        return $address ?: null;
    }
    
    /**
     * Find addresses by client ID
     *
     * @param int $clientId
     * @return array
     */
    public static function findByClientId(int $clientId): array
    {
        Database::prepare("SELECT * FROM addresses WHERE client_id = :client_id ORDER BY is_default DESC, id DESC");
        Database::bindValue(':client_id', $clientId, PDO::PARAM_INT);
        Database::execute();
        return Database::fetchAll();
    }
    
    /**
     * Create a new address
     *
     * @param array $data
     * @return int|false The new address ID or false on failure
     */
    public static function create(array $data): int|false
    {
        $sql = "INSERT INTO addresses (
            client_id,
            name,
            address_line1,
            address_line2,
            city,
            state,
            postal_code,
            country,
            phone,
            email,
            is_default,
            type,
            company
        ) VALUES (
            :client_id,
            :name,
            :address_line1,
            :address_line2,
            :city,
            :state,
            :postal_code,
            :country,
            :phone,
            :email,
            :is_default,
            :type,
            :company
        )";
        
        Database::prepare($sql);
        
        // Bind values
        Database::bindValue(':client_id', $data['client_id'], PDO::PARAM_INT);
        Database::bindValue(':name', $data['name'], PDO::PARAM_STR);
        Database::bindValue(':address_line1', $data['address_line1'], PDO::PARAM_STR);
        Database::bindValue(':address_line2', $data['address_line2'] ?? null, PDO::PARAM_STR);
        Database::bindValue(':city', $data['city'], PDO::PARAM_STR);
        Database::bindValue(':state', $data['state'] ?? null, PDO::PARAM_STR);
        Database::bindValue(':postal_code', $data['postal_code'] ?? null, PDO::PARAM_STR);
        Database::bindValue(':country', $data['country'] ?? null, PDO::PARAM_STR);
        Database::bindValue(':phone', $data['phone'] ?? null, PDO::PARAM_STR);
        Database::bindValue(':email', $data['email'] ?? null, PDO::PARAM_STR);
        Database::bindValue(':is_default', $data['is_default'] ?? 0, PDO::PARAM_INT);
        Database::bindValue(':type', $data['type'] ?? 'shipping', PDO::PARAM_STR);
        Database::bindValue(':company', $data['company'] ?? null, PDO::PARAM_STR);
        
        $success = Database::execute();
        
        if ($success) {
            // If this is the default address, update other addresses to not be default
            if ($data['is_default'] ?? 0) {
                self::updateDefaultStatus($data['client_id'], (int)Database::lastInsertId());
            }
            
            return (int)Database::lastInsertId();
        }
        
        return false;
    }
    
    /**
     * Update an address
     *
     * @param int $id
     * @param array $data
     * @return bool
     */
    public static function update(int $id, array $data): bool
    {
        $fields = [];
        $params = [':id' => $id];
        
        // Build the SET part dynamically based on provided data
        foreach ($data as $key => $value) {
            if (in_array($key, [
                'client_id', 'name', 'address_line1', 'address_line2', 'city', 'state', 
                'postal_code', 'country', 'phone', 'email', 'is_default', 'type', 'company'
            ])) {
                $fields[] = "{$key} = :{$key}";
                $params[":{$key}"] = $value;
            }
        }
        
        if (empty($fields)) {
            return false; // Nothing to update
        }
        
        $sql = "UPDATE addresses SET " . implode(', ', $fields) . " WHERE id = :id";
        
        Database::prepare($sql);
        
        // Bind all parameters
        foreach ($params as $param => $value) {
            $type = is_int($value) ? PDO::PARAM_INT : PDO::PARAM_STR;
            Database::bindValue($param, $value, $type);
        }
        
        $success = Database::execute();
        
        // If this is the default address, update other addresses to not be default
        if ($success && isset($data['is_default']) && $data['is_default'] && isset($data['client_id'])) {
            self::updateDefaultStatus($data['client_id'], $id);
        }
        
        return $success;
    }
    
    /**
     * Delete an address
     *
     * @param int $id
     * @return bool
     */
    public static function delete(int $id): bool
    {
        Database::prepare("DELETE FROM addresses WHERE id = :id");
        Database::bindValue(':id', $id, PDO::PARAM_INT);
        return Database::execute();
    }
    
    /**
     * Update default status for addresses
     * 
     * @param int $clientId
     * @param int $defaultAddressId
     * @return bool
     */
    private static function updateDefaultStatus(int $clientId, int $defaultAddressId): bool
    {
        Database::prepare("UPDATE addresses SET is_default = 0 WHERE client_id = :client_id AND id != :id");
        Database::bindValue(':client_id', $clientId, PDO::PARAM_INT);
        Database::bindValue(':id', $defaultAddressId, PDO::PARAM_INT);
        return Database::execute();
    }
    
    /**
     * Get formatted address string
     * 
     * @param array $address
     * @return string
     */
    public static function getFormattedAddress(array $address): string
    {
        $parts = [];
        
        if (!empty($address['name'])) {
            $parts[] = $address['name'];
        }
        
        if (!empty($address['company'])) {
            $parts[] = $address['company'];
        }
        
        if (!empty($address['address_line1'])) {
            $parts[] = $address['address_line1'];
        }
        
        if (!empty($address['address_line2'])) {
            $parts[] = $address['address_line2'];
        }
        
        $cityParts = [];
        if (!empty($address['city'])) {
            $cityParts[] = $address['city'];
        }
        
        if (!empty($address['state'])) {
            $cityParts[] = $address['state'];
        }
        
        if (!empty($address['postal_code'])) {
            $cityParts[] = $address['postal_code'];
        }
        
        if (!empty($cityParts)) {
            $parts[] = implode(', ', $cityParts);
        }
        
        if (!empty($address['country'])) {
            $parts[] = $address['country'];
        }
        
        return implode("\n", $parts);
    }
}
