<?php

namespace App\Models;

use App\Core\Database;
use PDO;

class Client
{
    /**
     * Get all clients
     *
     * @param int $limit Optional limit for number of records
     * @param int $offset Optional offset for pagination
     * @return array
     */
    public static function all(int $limit = 0, int $offset = 0): array
    {
        $sql = "SELECT * FROM clients ORDER BY name ASC";

        if ($limit > 0) {
            $sql .= " LIMIT :limit";
            if ($offset > 0) {
                $sql .= " OFFSET :offset";
            }
        }

        Database::prepare($sql);

        if ($limit > 0) {
            Database::bindValue(':limit', $limit, PDO::PARAM_INT);
            if ($offset > 0) {
                Database::bindValue(':offset', $offset, PDO::PARAM_INT);
            }
        }

        Database::execute();
        return Database::fetchAll();
    }

    /**
     * Find a client by ID
     *
     * @param int $id
     * @return array|null
     */
    public static function find(int $id): ?array
    {
        Database::prepare("SELECT * FROM clients WHERE id = :id LIMIT 1");
        Database::bindValue(':id', $id, PDO::PARAM_INT);
        Database::execute();
        $client = Database::fetch();

        return $client ?: null;
    }

    /**
     * Create a new client
     *
     * @param array $data
     * @return int|false The new client ID or false on failure
     */
    public static function create(array $data): int|false
    {
        $sql = "INSERT INTO clients (
            name,
            company,
            email,
            phone,
            address,
            city,
            state,
            postal_code,
            country,
            notes
        ) VALUES (
            :name,
            :company,
            :email,
            :phone,
            :address,
            :city,
            :state,
            :postal_code,
            :country,
            :notes
        )";

        error_log('Client create data: ' . print_r($data, true));
        error_log('SQL: ' . $sql);

        try {
            Database::prepare($sql);

            // Bind values
            Database::bindValue(':name', $data['name'] ?? '', PDO::PARAM_STR);
            Database::bindValue(':company', $data['company'] ?? null, PDO::PARAM_STR);
            Database::bindValue(':email', $data['email'] ?? '', PDO::PARAM_STR);
            Database::bindValue(':phone', $data['phone'] ?? '', PDO::PARAM_STR);
            Database::bindValue(':address', $data['address'] ?? null, PDO::PARAM_STR);
            Database::bindValue(':city', $data['city'] ?? null, PDO::PARAM_STR);
            Database::bindValue(':state', $data['state'] ?? null, PDO::PARAM_STR);
            Database::bindValue(':postal_code', $data['postal_code'] ?? null, PDO::PARAM_STR);
            Database::bindValue(':country', $data['country'] ?? null, PDO::PARAM_STR);
            Database::bindValue(':notes', $data['notes'] ?? null, PDO::PARAM_STR);
        } catch (\Exception $e) {
            error_log('Error binding values: ' . $e->getMessage());
            throw $e;
        }

        try {
            $success = Database::execute();

            if ($success) {
                $lastId = (int)Database::lastInsertId();
                error_log('Client created successfully with ID: ' . $lastId);
                return $lastId;
            }

            error_log('Client creation failed: Database::execute() returned false');
            return false;
        } catch (\Exception $e) {
            error_log('Error executing client creation: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Update a client
     *
     * @param int $id
     * @param array $data
     * @return bool
     */
    public static function update(int $id, array $data): bool
    {
        $fields = [];
        $params = [':id' => $id];

        // Build the SET part dynamically based on provided data
        foreach ($data as $key => $value) {
            if (in_array($key, [
                'name', 'company', 'email', 'phone', 'address',
                'city', 'state', 'postal_code', 'country', 'notes'
            ])) {
                $fields[] = "{$key} = :{$key}";
                $params[":{$key}"] = $value;
            }
        }

        if (empty($fields)) {
            return false; // Nothing to update
        }

        $sql = "UPDATE clients SET " . implode(', ', $fields) . " WHERE id = :id";

        Database::prepare($sql);

        // Bind all parameters
        foreach ($params as $param => $value) {
            $type = is_int($value) ? PDO::PARAM_INT : PDO::PARAM_STR;
            Database::bindValue($param, $value, $type);
        }

        return Database::execute();
    }

    /**
     * Delete a client
     *
     * @param int $id
     * @return bool
     */
    public static function delete(int $id): bool
    {
        Database::prepare("DELETE FROM clients WHERE id = :id");
        Database::bindValue(':id', $id, PDO::PARAM_INT);
        return Database::execute();
    }

    /**
     * Count total clients
     *
     * @return int
     */
    public static function count(): int
    {
        Database::prepare("SELECT COUNT(*) as count FROM clients");
        Database::execute();
        $result = Database::fetch();

        return (int)($result['count'] ?? 0);
    }
}
