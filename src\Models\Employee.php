<?php

namespace App\Models;

use App\Core\Database;
use PDO;

class Employee
{
    /**
     * Get all employees
     *
     * @param int $limit Optional limit for number of records
     * @param int $offset Optional offset for pagination
     * @return array
     */
    public static function all(int $limit = 0, int $offset = 0): array
    {
        $sql = "SELECT * FROM employees ORDER BY name ASC";
        
        if ($limit > 0) {
            $sql .= " LIMIT :limit";
            if ($offset > 0) {
                $sql .= " OFFSET :offset";
            }
        }
        
        Database::prepare($sql);
        
        if ($limit > 0) {
            Database::bindValue(':limit', $limit, PDO::PARAM_INT);
            if ($offset > 0) {
                Database::bindValue(':offset', $offset, PDO::PARAM_INT);
            }
        }
        
        Database::execute();
        return Database::fetchAll();
    }
    
    /**
     * Get all active employees
     *
     * @return array
     */
    public static function allActive(): array
    {
        Database::prepare("SELECT * FROM employees WHERE status = 'active' ORDER BY name ASC");
        Database::execute();
        return Database::fetchAll();
    }
    
    /**
     * Find an employee by ID
     *
     * @param int $id
     * @return array|null
     */
    public static function find(int $id): ?array
    {
        Database::prepare("SELECT * FROM employees WHERE id = :id LIMIT 1");
        Database::bindValue(':id', $id, PDO::PARAM_INT);
        Database::execute();
        $employee = Database::fetch();
        
        return $employee ?: null;
    }
    
    /**
     * Create a new employee
     *
     * @param array $data
     * @return int|false The new employee ID or false on failure
     */
    public static function create(array $data): int|false
    {
        $sql = "INSERT INTO employees (
            name, 
            email, 
            phone, 
            position, 
            department, 
            hire_date, 
            status, 
            notes
        ) VALUES (
            :name, 
            :email, 
            :phone, 
            :position, 
            :department, 
            :hire_date, 
            :status, 
            :notes
        )";
        
        Database::prepare($sql);
        
        // Bind values
        Database::bindValue(':name', $data['name'], PDO::PARAM_STR);
        Database::bindValue(':email', $data['email'] ?? null, PDO::PARAM_STR);
        Database::bindValue(':phone', $data['phone'] ?? null, PDO::PARAM_STR);
        Database::bindValue(':position', $data['position'] ?? null, PDO::PARAM_STR);
        Database::bindValue(':department', $data['department'] ?? null, PDO::PARAM_STR);
        Database::bindValue(':hire_date', $data['hire_date'] ?? null, PDO::PARAM_STR);
        Database::bindValue(':status', $data['status'] ?? 'active', PDO::PARAM_STR);
        Database::bindValue(':notes', $data['notes'] ?? null, PDO::PARAM_STR);
        
        $success = Database::execute();
        
        if ($success) {
            return (int)Database::lastInsertId();
        }
        
        return false;
    }
    
    /**
     * Update an employee
     *
     * @param int $id
     * @param array $data
     * @return bool
     */
    public static function update(int $id, array $data): bool
    {
        $fields = [];
        $params = [':id' => $id];
        
        // Build the SET part dynamically based on provided data
        foreach ($data as $key => $value) {
            if (in_array($key, [
                'name', 'email', 'phone', 'position', 
                'department', 'hire_date', 'status', 'notes'
            ])) {
                $fields[] = "{$key} = :{$key}";
                $params[":{$key}"] = $value;
            }
        }
        
        if (empty($fields)) {
            return false; // Nothing to update
        }
        
        $sql = "UPDATE employees SET " . implode(', ', $fields) . " WHERE id = :id";
        
        Database::prepare($sql);
        
        // Bind all parameters
        foreach ($params as $param => $value) {
            $type = is_int($value) ? PDO::PARAM_INT : PDO::PARAM_STR;
            Database::bindValue($param, $value, $type);
        }
        
        return Database::execute();
    }
    
    /**
     * Delete an employee
     *
     * @param int $id
     * @return bool
     */
    public static function delete(int $id): bool
    {
        Database::prepare("DELETE FROM employees WHERE id = :id");
        Database::bindValue(':id', $id, PDO::PARAM_INT);
        return Database::execute();
    }
    
    /**
     * Count total employees
     *
     * @return int
     */
    public static function count(): int
    {
        Database::prepare("SELECT COUNT(*) as count FROM employees");
        Database::execute();
        $result = Database::fetch();
        
        return (int)($result['count'] ?? 0);
    }
}
