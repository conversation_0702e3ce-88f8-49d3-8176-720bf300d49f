<?php

namespace App\Models;

use App\Core\Database;
use PDO;

class Setting
{
    /**
     * Get all settings
     *
     * @return array
     */
    public static function all(): array
    {
        Database::prepare("SELECT * FROM settings ORDER BY `key`");
        Database::execute();
        $settings = Database::fetchAll();

        // Convert to associative array with key as index
        $result = [];
        foreach ($settings as $setting) {
            $result[$setting['key']] = $setting['value'];
        }

        return $result;
    }

    /**
     * Get settings by category
     *
     * @param string $category
     * @return array
     */
    public static function getByGroup(string $category): array
    {
        Database::prepare("SELECT * FROM settings WHERE `category` = :category ORDER BY `name`");
        Database::bindValue(':category', $category, PDO::PARAM_STR);
        Database::execute();
        $settings = Database::fetchAll();

        // Convert to associative array with name as index
        $result = [];
        foreach ($settings as $setting) {
            $result[$setting['name']] = $setting['value'];
        }

        return $result;
    }

    /**
     * Get a single setting by name
     *
     * @param string $name
     * @param mixed $default Default value if setting not found
     * @return mixed
     */
    public static function get(string $name, mixed $default = null): mixed
    {
        Database::prepare("SELECT * FROM settings WHERE `name` = :name LIMIT 1");
        Database::bindValue(':name', $name, PDO::PARAM_STR);
        Database::execute();
        $setting = Database::fetch();

        if ($setting) {
            // Handle JSON values
            if (self::isJson($setting['value'])) {
                return json_decode($setting['value'], true);
            }
            return $setting['value'];
        }

        return $default;
    }

    /**
     * Set a setting value
     *
     * @param string $name
     * @param mixed $value
     * @param string $category
     * @return bool
     */
    public static function set(string $name, mixed $value, string $category = 'general'): bool
    {
        // Handle arrays and objects by converting to JSON
        if (is_array($value) || is_object($value)) {
            $value = json_encode($value);
        }

        // Check if setting exists
        Database::prepare("SELECT COUNT(*) as count FROM settings WHERE `name` = :name");
        Database::bindValue(':name', $name, PDO::PARAM_STR);
        Database::execute();
        $exists = (int)Database::fetch()['count'] > 0;

        if ($exists) {
            // Update existing setting
            Database::prepare("UPDATE settings SET `value` = :value, updated_at = NOW() WHERE `name` = :name");
            Database::bindValue(':name', $name, PDO::PARAM_STR);
            Database::bindValue(':value', $value, PDO::PARAM_STR);
            return Database::execute();
        } else {
            // Insert new setting
            Database::prepare("
                INSERT INTO settings (`name`, `value`, `category`, `type`, created_at, updated_at)
                VALUES (:name, :value, :category, 'string', NOW(), NOW())
            ");
            Database::bindValue(':name', $name, PDO::PARAM_STR);
            Database::bindValue(':value', $value, PDO::PARAM_STR);
            Database::bindValue(':category', $category, PDO::PARAM_STR);
            return Database::execute();
        }
    }

    /**
     * Delete a setting
     *
     * @param string $name
     * @return bool
     */
    public static function delete(string $name): bool
    {
        Database::prepare("DELETE FROM settings WHERE `name` = :name");
        Database::bindValue(':name', $name, PDO::PARAM_STR);
        return Database::execute();
    }

    /**
     * Get a setting as an array
     *
     * @param string $name
     * @param array $default Default value if setting not found
     * @return array
     */
    public static function getAsArray(string $name, array $default = []): array
    {
        $value = self::get($name, $default);

        // If it's already an array, return it
        if (is_array($value)) {
            return $value;
        }

        // If it's a string that might be JSON, try to decode it
        if (is_string($value) && self::isJson($value)) {
            $decoded = json_decode($value, true);
            if (is_array($decoded)) {
                return $decoded;
            }
        }

        // If it's a comma-separated string, split it into an array
        if (is_string($value) && strpos($value, ',') !== false) {
            return array_map('trim', explode(',', $value));
        }

        // If it's a scalar value, wrap it in an array
        if (is_scalar($value)) {
            return [$value];
        }

        // Default fallback
        return $default;
    }

    /**
     * Check if a string is valid JSON
     *
     * @param string $string
     * @return bool
     */
    private static function isJson(string $string): bool
    {
        json_decode($string);
        return json_last_error() === JSON_ERROR_NONE;
    }

    /**
     * Update or create a setting
     *
     * @param string $category
     * @param string $name
     * @param mixed $value
     * @param string $description
     * @return bool
     */
    public static function updateOrCreate(string $category, string $name, $value, string $description = ''): bool
    {
        try {
            // Check if setting exists
            Database::prepare("SELECT id FROM settings WHERE category = :category AND name = :name");
            Database::bindValue(':category', $category, PDO::PARAM_STR);
            Database::bindValue(':name', $name, PDO::PARAM_STR);
            Database::execute();
            $existing = Database::fetch();

            if ($existing) {
                // Update existing setting
                Database::prepare("UPDATE settings SET value = :value WHERE category = :category AND name = :name");
                Database::bindValue(':value', $value, PDO::PARAM_STR);
                Database::bindValue(':category', $category, PDO::PARAM_STR);
                Database::bindValue(':name', $name, PDO::PARAM_STR);
                Database::execute();
            } else {
                // Create new setting
                Database::prepare("INSERT INTO settings (category, name, value, description) VALUES (:category, :name, :value, :description)");
                Database::bindValue(':category', $category, PDO::PARAM_STR);
                Database::bindValue(':name', $name, PDO::PARAM_STR);
                Database::bindValue(':value', $value, PDO::PARAM_STR);
                Database::bindValue(':description', $description, PDO::PARAM_STR);
                Database::execute();
            }

            return true;
        } catch (Exception $e) {
            error_log('Setting updateOrCreate error: ' . $e->getMessage());
            return false;
        }
    }
}
