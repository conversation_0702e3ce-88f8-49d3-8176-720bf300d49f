<?php

namespace App\Models;

use App\Core\Database;
use PDO;

class Shipment
{
    /**
     * Generate a unique tracking number
     *
     * @return string
     */
    public static function generateTrackingNumber(): string
    {
        // Get settings from database if available
        $prefix = 'TSD';
        $suffix = '';
        $digits = 6;

        if (class_exists('\App\Models\Setting')) {
            $prefix = \App\Models\Setting::get('tracking_number_prefix', 'TSD');
            $suffix = \App\Models\Setting::get('tracking_number_suffix', '');
            $digits = (int)\App\Models\Setting::get('tracking_number_digits', 6);
        }

        // Ensure digits is at least 4
        $digits = max(4, $digits);

        // Format: Prefix + YearMonthDay + Random N digits + Suffix
        $date = date('ymd');
        $random = str_pad(mt_rand(0, pow(10, $digits) - 1), $digits, '0', STR_PAD_LEFT);

        $trackingNumber = $prefix . $date . $random . $suffix;

        // Check if tracking number already exists
        while (self::findByTrackingNumber($trackingNumber)) {
            // Generate a new random part if it exists
            $random = str_pad(mt_rand(0, pow(10, $digits) - 1), $digits, '0', STR_PAD_LEFT);
            $trackingNumber = $prefix . $date . $random . $suffix;
        }

        return $trackingNumber;
    }
    /**
     * Get all shipments
     *
     * @param int $limit Optional limit for number of records
     * @param int $offset Optional offset for pagination
     * @return array
     */
    public static function all(int $limit = 0, int $offset = 0): array
    {
        $sql = "SELECT s.*, c.name as client_name, e.name as employee_name
               FROM shipments s
               LEFT JOIN clients c ON s.client_id = c.id
               LEFT JOIN employees e ON s.employee_id = e.id
               ORDER BY s.created_at DESC";

        if ($limit > 0) {
            $sql .= " LIMIT :limit";
            if ($offset > 0) {
                $sql .= " OFFSET :offset";
            }
        }

        Database::prepare($sql);

        if ($limit > 0) {
            Database::bindValue(':limit', $limit, PDO::PARAM_INT);
            if ($offset > 0) {
                Database::bindValue(':offset', $offset, PDO::PARAM_INT);
            }
        }

        Database::execute();
        return Database::fetchAll();
    }

    /**
     * Find a shipment by ID
     *
     * @param int $id
     * @return array|null
     */
    public static function find(int $id): ?array
    {
        Database::prepare("SELECT s.*, c.name as client_name, e.name as employee_name
                          FROM shipments s
                          LEFT JOIN clients c ON s.client_id = c.id
                          LEFT JOIN employees e ON s.employee_id = e.id
                          WHERE s.id = :id LIMIT 1");
        Database::bindValue(':id', $id, PDO::PARAM_INT);
        Database::execute();
        $shipment = Database::fetch();

        return $shipment ?: null;
    }

    /**
     * Find a shipment by ID with all relations for edit form
     *
     * @param int $id
     * @return array|null
     */
    public static function findWithRelations(int $id): ?array
    {
        // First get the basic shipment data
        Database::prepare("SELECT * FROM shipments WHERE id = :id LIMIT 1");
        Database::bindValue(':id', $id, PDO::PARAM_INT);
        Database::execute();
        $shipment = Database::fetch();

        if (!$shipment) {
            error_log("Shipment not found for ID: $id");
            return null;
        }

        // Log the raw shipment data
        error_log('Raw shipment data from database: ' . json_encode($shipment));

        return $shipment;
    }

    /**
     * Find a shipment by tracking number
     *
     * @param string $trackingNumber
     * @return array|null
     */
    public static function findByTrackingNumber(string $trackingNumber): ?array
    {
        Database::prepare("SELECT * FROM shipments WHERE tracking_number = :tracking_number LIMIT 1");
        Database::bindValue(':tracking_number', $trackingNumber, PDO::PARAM_STR);
        Database::execute();
        $shipment = Database::fetch();

        return $shipment ?: null;
    }

    /**
     * Create a new shipment
     *
     * @param array $data
     * @return int|false The new shipment ID or false on failure
     */
    public static function create(array $data): int|false
    {
        $sql = "INSERT INTO shipments (
            tracking_number,
            client_id,
            employee_id,
            origin,
            destination,
            shipper_name,
            shipper_address,
            shipper_phone,
            shipper_email,
            receiver_name,
            receiver_address,
            receiver_phone,
            receiver_email,
            status,
            shipment_type,
            shipment_mode,
            courier,
            product,
            quantity,
            payment_mode,
            total_freight,
            carrier,
            carrier_reference_no,
            pickup_date,
            pickup_time,
            departure_date,
            expected_delivery_date,
            total_weight,
            comments
        ) VALUES (
            :tracking_number,
            :client_id,
            :employee_id,
            :origin,
            :destination,
            :shipper_name,
            :shipper_address,
            :shipper_phone,
            :shipper_email,
            :receiver_name,
            :receiver_address,
            :receiver_phone,
            :receiver_email,
            :status,
            :shipment_type,
            :shipment_mode,
            :courier,
            :product,
            :quantity,
            :payment_mode,
            :total_freight,
            :carrier,
            :carrier_reference_no,
            :pickup_date,
            :pickup_time,
            :departure_date,
            :expected_delivery_date,
            :total_weight,
            :comments
        )";

        Database::prepare($sql);

        // Bind values
        // Generate tracking number if not provided
        if (empty($data['tracking_number'])) {
            $data['tracking_number'] = self::generateTrackingNumber();
        }

        Database::bindValue(':tracking_number', $data['tracking_number'], PDO::PARAM_STR);
        Database::bindValue(':client_id', $data['client_id'] ?? null, $data['client_id'] ? PDO::PARAM_INT : PDO::PARAM_NULL);
        Database::bindValue(':employee_id', $data['employee_id'] ?? null, $data['employee_id'] ? PDO::PARAM_INT : PDO::PARAM_NULL);
        Database::bindValue(':origin', $data['origin'], PDO::PARAM_STR);
        Database::bindValue(':destination', $data['destination'], PDO::PARAM_STR);

        // Shipper details
        Database::bindValue(':shipper_name', $data['shipper_name'], PDO::PARAM_STR);
        Database::bindValue(':shipper_address', $data['shipper_address'], PDO::PARAM_STR);
        Database::bindValue(':shipper_phone', $data['shipper_phone'], PDO::PARAM_STR);
        Database::bindValue(':shipper_email', $data['shipper_email'] ?? null, PDO::PARAM_STR);

        // Receiver details
        Database::bindValue(':receiver_name', $data['receiver_name'], PDO::PARAM_STR);
        Database::bindValue(':receiver_address', $data['receiver_address'], PDO::PARAM_STR);
        Database::bindValue(':receiver_phone', $data['receiver_phone'], PDO::PARAM_STR);
        Database::bindValue(':receiver_email', $data['receiver_email'] ?? null, PDO::PARAM_STR);

        // Status
        Database::bindValue(':status', $data['status'], PDO::PARAM_STR);

        // Shipment details
        Database::bindValue(':shipment_type', $data['shipment_type'] ?? null, PDO::PARAM_STR);
        Database::bindValue(':shipment_mode', $data['shipment_mode'] ?? null, PDO::PARAM_STR);
        Database::bindValue(':courier', $data['courier'] ?? null, PDO::PARAM_STR);
        Database::bindValue(':product', $data['product'] ?? null, PDO::PARAM_STR);
        Database::bindValue(':quantity', $data['quantity'] ?? null, $data['quantity'] ? PDO::PARAM_INT : PDO::PARAM_NULL);
        Database::bindValue(':payment_mode', $data['payment_mode'] ?? null, PDO::PARAM_STR);
        // Ensure total_freight is a valid decimal or null
        $totalFreight = isset($data['total_freight']) && $data['total_freight'] !== '' ? (float)$data['total_freight'] : null;
        Database::bindValue(':total_freight', $totalFreight, PDO::PARAM_STR);
        Database::bindValue(':carrier', $data['carrier'] ?? null, PDO::PARAM_STR);
        Database::bindValue(':carrier_reference_no', $data['carrier_reference_no'] ?? null, PDO::PARAM_STR);

        // Dates and times
        Database::bindValue(':pickup_date', $data['pickup_date'] ?? date('Y-m-d'), PDO::PARAM_STR);
        Database::bindValue(':pickup_time', $data['pickup_time'] ?? null, PDO::PARAM_STR);
        Database::bindValue(':departure_date', $data['departure_date'] ?? null, PDO::PARAM_STR);
        Database::bindValue(':expected_delivery_date', $data['expected_delivery_date'] ?? null, PDO::PARAM_STR);

        // Weight and comments
        Database::bindValue(':total_weight', $data['total_weight'] ?? $data['weight'] ?? null, PDO::PARAM_STR);
        Database::bindValue(':comments', $data['comments'] ?? $data['notes'] ?? null, PDO::PARAM_STR);

        $success = Database::execute();

        if ($success) {
            return (int)Database::lastInsertId();
        }

        return false;
    }

    /**
     * Update a shipment
     *
     * @param int $id
     * @param array $data
     * @return bool
     */
    public static function update(int $id, array $data): bool
    {
        $fields = [];
        $params = [':id' => $id];

        // Build the SET part dynamically based on provided data
        foreach ($data as $key => $value) {
            if (in_array($key, [
                'tracking_number', 'client_id', 'employee_id', 'origin', 'destination', 'shipper_name',
                'shipper_address', 'shipper_phone', 'shipper_email', 'receiver_name',
                'receiver_address', 'receiver_phone', 'receiver_email', 'status',
                'shipment_type', 'shipment_mode', 'courier', 'product', 'quantity',
                'payment_mode', 'total_freight', 'carrier', 'carrier_reference_no',
                'pickup_date', 'pickup_time', 'departure_date', 'expected_delivery_date',
                'total_weight', 'comments'
            ])) {
                $fields[] = "{$key} = :{$key}";
                $params[":{$key}"] = $value;
            }
        }

        if (empty($fields)) {
            return false; // Nothing to update
        }

        $sql = "UPDATE shipments SET " . implode(', ', $fields) . " WHERE id = :id";

        Database::prepare($sql);

        // Bind all parameters
        foreach ($params as $param => $value) {
            $type = is_int($value) ? PDO::PARAM_INT : PDO::PARAM_STR;
            Database::bindValue($param, $value, $type);
        }

        return Database::execute();
    }

    /**
     * Delete a shipment
     *
     * @param int $id
     * @return bool
     */
    public static function delete(int $id): bool
    {
        Database::prepare("DELETE FROM shipments WHERE id = :id");
        Database::bindValue(':id', $id, PDO::PARAM_INT);
        return Database::execute();
    }

    /**
     * Count shipments by status
     *
     * @return array
     */
    public static function countByStatus(): array
    {
        Database::prepare("
            SELECT
                status,
                COUNT(*) as count
            FROM
                shipments
            GROUP BY
                status
        ");
        Database::execute();
        $results = Database::fetchAll();

        // Convert to associative array with status as key
        $counts = [];
        foreach ($results as $row) {
            $counts[$row['status']] = (int)$row['count'];
        }

        return $counts;
    }

    /**
     * Count total shipments
     *
     * @return int
     */
    public static function count(): int
    {
        Database::prepare("SELECT COUNT(*) as count FROM shipments");
        Database::execute();
        $result = Database::fetch();

        return (int)($result['count'] ?? 0);
    }

    /**
     * Get shipments created today
     *
     * @return array
     */
    public static function createdToday(): array
    {
        Database::prepare("
            SELECT
                *
            FROM
                shipments
            WHERE
                DATE(created_at) = CURDATE()
            ORDER BY
                created_at DESC
        ");
        Database::execute();
        return Database::fetchAll();
    }

    /**
     * Get shipments delivered today
     *
     * @return array
     */
    public static function deliveredToday(): array
    {
        Database::prepare("
            SELECT
                *
            FROM
                shipments
            WHERE
                status = 'delivered'
                AND DATE(updated_at) = CURDATE()
            ORDER BY
                updated_at DESC
        ");
        Database::execute();
        return Database::fetchAll();
    }

    /**
     * Get shipments by month for the current year
     *
     * @return array
     */
    public static function countByMonth(): array
    {
        Database::prepare("
            SELECT
                MONTH(created_at) as month,
                COUNT(*) as count
            FROM
                shipments
            WHERE
                YEAR(created_at) = YEAR(CURDATE())
            GROUP BY
                MONTH(created_at)
            ORDER BY
                month
        ");
        Database::execute();
        $results = Database::fetchAll();

        // Initialize all months with 0
        $counts = array_fill(1, 12, 0);

        // Fill in actual counts
        foreach ($results as $row) {
            $counts[(int)$row['month']] = (int)$row['count'];
        }

        return $counts;
    }
}
