<?php

namespace App\Models;

use App\Core\Database;
use PDO;

class ShipmentHistory
{
    /**
     * Get all history entries for a shipment
     *
     * @param int $shipmentId
     * @return array
     */
    public static function findByShipmentId(int $shipmentId): array
    {
        Database::prepare("
            SELECT * FROM shipment_history
            WHERE shipment_id = :shipment_id
            ORDER BY date_time ASC
        ");
        Database::bindValue(':shipment_id', $shipmentId, PDO::PARAM_INT);
        Database::execute();
        return Database::fetchAll();
    }

    /**
     * Add a new history entry
     *
     * @param array $data
     * @return int|false The new history entry ID or false on failure
     */
    public static function create(array $data): int|false
    {
        $sql = "INSERT INTO shipment_history (
            shipment_id,
            status,
            location,
            message,
            date_time
        ) VALUES (
            :shipment_id,
            :status,
            :location,
            :message,
            :date_time
        )";

        Database::prepare($sql);

        // Bind values
        Database::bindValue(':shipment_id', $data['shipment_id'], PDO::PARAM_INT);
        Database::bindValue(':status', $data['status'], PDO::PARAM_STR);
        Database::bindValue(':location', $data['location'], PDO::PARAM_STR);
        Database::bindValue(':message', $data['message'] ?? $data['description'] ?? '', PDO::PARAM_STR);

        // Use current timestamp if not provided
        $dateTime = $data['date_time'] ?? date('Y-m-d H:i:s');
        Database::bindValue(':date_time', $dateTime, PDO::PARAM_STR);

        $success = Database::execute();

        if ($success) {
            return (int)Database::lastInsertId();
        }

        return false;
    }

    /**
     * Delete history entries for a shipment
     *
     * @param int $shipmentId
     * @return bool
     */
    public static function deleteByShipmentId(int $shipmentId): bool
    {
        Database::prepare("DELETE FROM shipment_history WHERE shipment_id = :shipment_id");
        Database::bindValue(':shipment_id', $shipmentId, PDO::PARAM_INT);
        return Database::execute();
    }

    /**
     * Get the latest history entry for a shipment
     *
     * @param int $shipmentId
     * @return array|null
     */
    public static function getLatestForShipment(int $shipmentId): ?array
    {
        Database::prepare("
            SELECT * FROM shipment_history
            WHERE shipment_id = :shipment_id
            ORDER BY date_time DESC
            LIMIT 1
        ");
        Database::bindValue(':shipment_id', $shipmentId, PDO::PARAM_INT);
        Database::execute();
        $history = Database::fetch();

        return $history ?: null;
    }
}
