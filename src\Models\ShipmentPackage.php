<?php

namespace App\Models;

use App\Core\Database;
use PDO;

class ShipmentPackage
{
    /**
     * Get all packages for a shipment
     *
     * @param int $shipmentId
     * @return array
     */
    public static function findByShipmentId(int $shipmentId): array
    {
        Database::prepare("SELECT * FROM shipment_packages WHERE shipment_id = :shipment_id ORDER BY id ASC");
        Database::bindValue(':shipment_id', $shipmentId, PDO::PARAM_INT);
        Database::execute();
        return Database::fetchAll();
    }
    
    /**
     * Find a package by ID
     *
     * @param int $id
     * @return array|null
     */
    public static function find(int $id): ?array
    {
        Database::prepare("SELECT * FROM shipment_packages WHERE id = :id LIMIT 1");
        Database::bindValue(':id', $id, PDO::PARAM_INT);
        Database::execute();
        $package = Database::fetch();
        
        return $package ?: null;
    }
    
    /**
     * Create a new package
     *
     * @param array $data
     * @return int|false The new package ID or false on failure
     */
    public static function create(array $data): int|false
    {
        $sql = "INSERT INTO shipment_packages (
            shipment_id,
            piece_type,
            description,
            quantity,
            length,
            width,
            height,
            weight,
            volume,
            volumetric_weight
        ) VALUES (
            :shipment_id,
            :piece_type,
            :description,
            :quantity,
            :length,
            :width,
            :height,
            :weight,
            :volume,
            :volumetric_weight
        )";
        
        Database::prepare($sql);
        
        // Calculate volume if dimensions are provided
        $volume = null;
        if (!empty($data['length']) && !empty($data['width']) && !empty($data['height'])) {
            $volume = ($data['length'] * $data['width'] * $data['height']) / 1000000; // Convert to cubic meters
        }
        
        // Calculate volumetric weight if dimensions are provided (using standard divisor 5000)
        $volumetricWeight = null;
        if (!empty($data['length']) && !empty($data['width']) && !empty($data['height'])) {
            $volumetricWeight = ($data['length'] * $data['width'] * $data['height']) / 5000; // Standard formula
        }
        
        // Bind values
        Database::bindValue(':shipment_id', $data['shipment_id'], PDO::PARAM_INT);
        Database::bindValue(':piece_type', $data['piece_type'] ?? null, PDO::PARAM_STR);
        Database::bindValue(':description', $data['description'] ?? null, PDO::PARAM_STR);
        Database::bindValue(':quantity', $data['quantity'] ?? 1, PDO::PARAM_INT);
        Database::bindValue(':length', $data['length'] ?? null, PDO::PARAM_STR);
        Database::bindValue(':width', $data['width'] ?? null, PDO::PARAM_STR);
        Database::bindValue(':height', $data['height'] ?? null, PDO::PARAM_STR);
        Database::bindValue(':weight', $data['weight'] ?? null, PDO::PARAM_STR);
        Database::bindValue(':volume', $volume, PDO::PARAM_STR);
        Database::bindValue(':volumetric_weight', $volumetricWeight, PDO::PARAM_STR);
        
        $success = Database::execute();
        
        if ($success) {
            return (int)Database::lastInsertId();
        }
        
        return false;
    }
    
    /**
     * Update a package
     *
     * @param int $id
     * @param array $data
     * @return bool
     */
    public static function update(int $id, array $data): bool
    {
        $fields = [];
        $params = [':id' => $id];
        
        // Calculate volume if dimensions are provided
        if (!empty($data['length']) && !empty($data['width']) && !empty($data['height'])) {
            $data['volume'] = ($data['length'] * $data['width'] * $data['height']) / 1000000; // Convert to cubic meters
            $data['volumetric_weight'] = ($data['length'] * $data['width'] * $data['height']) / 5000; // Standard formula
        }
        
        // Build the SET part dynamically based on provided data
        foreach ($data as $key => $value) {
            if (in_array($key, [
                'shipment_id', 'piece_type', 'description', 'quantity',
                'length', 'width', 'height', 'weight', 'volume', 'volumetric_weight'
            ])) {
                $fields[] = "{$key} = :{$key}";
                $params[":{$key}"] = $value;
            }
        }
        
        if (empty($fields)) {
            return false; // Nothing to update
        }
        
        $sql = "UPDATE shipment_packages SET " . implode(', ', $fields) . " WHERE id = :id";
        
        Database::prepare($sql);
        
        // Bind all parameters
        foreach ($params as $param => $value) {
            $type = is_int($value) ? PDO::PARAM_INT : PDO::PARAM_STR;
            Database::bindValue($param, $value, $type);
        }
        
        return Database::execute();
    }
    
    /**
     * Delete a package
     *
     * @param int $id
     * @return bool
     */
    public static function delete(int $id): bool
    {
        Database::prepare("DELETE FROM shipment_packages WHERE id = :id");
        Database::bindValue(':id', $id, PDO::PARAM_INT);
        return Database::execute();
    }
    
    /**
     * Delete all packages for a shipment
     *
     * @param int $shipmentId
     * @return bool
     */
    public static function deleteByShipmentId(int $shipmentId): bool
    {
        Database::prepare("DELETE FROM shipment_packages WHERE shipment_id = :shipment_id");
        Database::bindValue(':shipment_id', $shipmentId, PDO::PARAM_INT);
        return Database::execute();
    }
    
    /**
     * Calculate total weight for a shipment
     *
     * @param int $shipmentId
     * @return float
     */
    public static function calculateTotalWeight(int $shipmentId): float
    {
        Database::prepare("SELECT SUM(weight * quantity) as total_weight FROM shipment_packages WHERE shipment_id = :shipment_id");
        Database::bindValue(':shipment_id', $shipmentId, PDO::PARAM_INT);
        Database::execute();
        $result = Database::fetch();
        
        return (float)($result['total_weight'] ?? 0);
    }
    
    /**
     * Calculate total volumetric weight for a shipment
     *
     * @param int $shipmentId
     * @return float
     */
    public static function calculateTotalVolumetricWeight(int $shipmentId): float
    {
        Database::prepare("SELECT SUM(volumetric_weight * quantity) as total_volumetric_weight FROM shipment_packages WHERE shipment_id = :shipment_id");
        Database::bindValue(':shipment_id', $shipmentId, PDO::PARAM_INT);
        Database::execute();
        $result = Database::fetch();
        
        return (float)($result['total_volumetric_weight'] ?? 0);
    }
}
