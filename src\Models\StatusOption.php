<?php

namespace App\Models;

use App\Core\Database;
use PDO;

class StatusOption
{
    /**
     * Get all status options (both active and inactive)
     *
     * @return array
     */
    public static function all(): array
    {
        Database::prepare("SELECT * FROM status_options ORDER BY name ASC");
        Database::execute();
        return Database::fetchAll();
    }

    /**
     * Get only active status options
     *
     * @return array
     */
    public static function getActive(): array
    {
        Database::prepare("SELECT * FROM status_options WHERE is_active = 1 ORDER BY name ASC");
        Database::execute();
        return Database::fetchAll();
    }

    /**
     * Find a status option by ID
     *
     * @param int $id
     * @return array|null
     */
    public static function find(int $id): ?array
    {
        Database::prepare("SELECT * FROM status_options WHERE id = :id LIMIT 1");
        Database::bindValue(':id', $id, PDO::PARAM_INT);
        Database::execute();
        $status = Database::fetch();

        return $status ?: null;
    }

    /**
     * Find a status option by name
     *
     * @param string $name
     * @return array|null
     */
    public static function findByName(string $name): ?array
    {
        Database::prepare("SELECT * FROM status_options WHERE name = :name LIMIT 1");
        Database::bindValue(':name', $name, PDO::PARAM_STR);
        Database::execute();
        $status = Database::fetch();

        return $status ?: null;
    }

    /**
     * Create a new status option
     *
     * @param array $data
     * @return int|false The new status option ID or false on failure
     */
    public static function create(array $data): int|false
    {
        $sql = "INSERT INTO status_options (
            name,
            description,
            color,
            is_active
        ) VALUES (
            :name,
            :description,
            :color,
            :is_active
        )";

        Database::prepare($sql);

        // Bind values
        Database::bindValue(':name', $data['name'], PDO::PARAM_STR);
        Database::bindValue(':description', $data['description'] ?? null, PDO::PARAM_STR);
        Database::bindValue(':color', $data['color'] ?? null, PDO::PARAM_STR);
        Database::bindValue(':is_active', $data['is_active'] ?? 1, PDO::PARAM_INT);

        $success = Database::execute();

        if ($success) {
            return (int)Database::lastInsertId();
        }

        return false;
    }

    /**
     * Update a status option
     *
     * @param int $id
     * @param array $data
     * @return bool
     */
    public static function update(int $id, array $data): bool
    {
        $fields = [];
        $params = [':id' => $id];

        // Build the SET part dynamically based on provided data
        foreach ($data as $key => $value) {
            if (in_array($key, ['name', 'description', 'color', 'is_active'])) {
                $fields[] = "{$key} = :{$key}";
                $params[":{$key}"] = $value;
            }
        }

        if (empty($fields)) {
            return false; // Nothing to update
        }

        $sql = "UPDATE status_options SET " . implode(', ', $fields) . " WHERE id = :id";

        Database::prepare($sql);

        // Bind all parameters
        foreach ($params as $param => $value) {
            $type = is_int($value) ? PDO::PARAM_INT : PDO::PARAM_STR;
            Database::bindValue($param, $value, $type);
        }

        return Database::execute();
    }

    /**
     * Delete a status option
     *
     * @param int $id
     * @return bool
     */
    public static function delete(int $id): bool
    {
        Database::prepare("DELETE FROM status_options WHERE id = :id");
        Database::bindValue(':id', $id, PDO::PARAM_INT);
        return Database::execute();
    }

    /**
     * Get all status options as an associative array
     *
     * @param bool $activeOnly Whether to include only active statuses
     * @return array
     */
    public static function getAsAssociativeArray(bool $activeOnly = true): array
    {
        $statuses = $activeOnly ? self::getActive() : self::all();
        $result = [];

        foreach ($statuses as $status) {
            $result[$status['name']] = [
                'label' => ucfirst(str_replace('_', ' ', $status['name'])),
                'color' => $status['color'] ?? '#000000'
            ];
        }

        return $result;
    }
}
