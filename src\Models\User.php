<?php

namespace App\Models;

use App\Core\Database;
use PDO;

class User
{
    /**
     * Find a user by their ID.
     *
     * @param int $id
     * @return array|null User data or null if not found
     */
    public static function find(int $id): ?array
    {
        Database::prepare("SELECT * FROM users WHERE id = :id LIMIT 1");
        Database::execute(['id' => $id]);
        $user = Database::fetch();
        
        return $user ?: null;
    }

    /**
     * Find a user by their email.
     *
     * @param string $email
     * @return array|null User data or null if not found
     */
    public static function findByEmail(string $email): ?array
    {
        Database::prepare("SELECT * FROM users WHERE email = :email LIMIT 1");
        Database::execute(['email' => $email]);
        $user = Database::fetch();
        
        return $user ?: null;
    }

    /**
     * Find a user by their username.
     *
     * @param string $username
     * @return array|null User data or null if not found
     */
    public static function findByUsername(string $username): ?array
    {
        Database::prepare("SELECT * FROM users WHERE username = :username LIMIT 1");
        Database::execute(['username' => $username]);
        $user = Database::fetch();
        
        return $user ?: null;
    }

    /**
     * Update the last login timestamp for a user.
     *
     * @param int $id
     * @return bool
     */
    public static function updateLastLogin(int $id): bool
    {
        Database::prepare("UPDATE users SET last_login = NOW() WHERE id = :id");
        return Database::execute(['id' => $id]);
    }

    /**
     * Create a new user.
     *
     * @param array $data
     * @return int|false The new user ID or false on failure
     */
    public static function create(array $data): int|false
    {
        // Ensure password is hashed
        if (isset($data['password']) && !empty($data['password'])) {
            $data['password'] = password_hash($data['password'], PASSWORD_DEFAULT);
        }

        $sql = "INSERT INTO users (username, password, email, name, role) 
                VALUES (:username, :password, :email, :name, :role)";
        
        Database::prepare($sql);
        $success = Database::execute([
            'username' => $data['username'],
            'password' => $data['password'],
            'email' => $data['email'],
            'name' => $data['name'],
            'role' => $data['role'] ?? 'staff'
        ]);
        
        if ($success) {
            return (int)Database::lastInsertId();
        }
        
        return false;
    }

    /**
     * Update a user.
     *
     * @param int $id
     * @param array $data
     * @return bool
     */
    public static function update(int $id, array $data): bool
    {
        // Build the SET part of the query dynamically based on provided data
        $sets = [];
        $params = ['id' => $id];
        
        foreach ($data as $key => $value) {
            // Handle password separately to hash it
            if ($key === 'password' && !empty($value)) {
                $sets[] = "password = :password";
                $params['password'] = password_hash($value, PASSWORD_DEFAULT);
            } elseif (in_array($key, ['username', 'email', 'name', 'role', 'is_active'])) {
                $sets[] = "$key = :$key";
                $params[$key] = $value;
            }
        }
        
        if (empty($sets)) {
            return false; // Nothing to update
        }
        
        $sql = "UPDATE users SET " . implode(', ', $sets) . " WHERE id = :id";
        Database::prepare($sql);
        return Database::execute($params);
    }

    /**
     * Delete a user.
     *
     * @param int $id
     * @return bool
     */
    public static function delete(int $id): bool
    {
        Database::prepare("DELETE FROM users WHERE id = :id");
        return Database::execute(['id' => $id]);
    }

    /**
     * Get all users.
     *
     * @return array
     */
    public static function all(): array
    {
        Database::prepare("SELECT * FROM users ORDER BY id");
        Database::execute();
        return Database::fetchAll();
    }

    /**
     * Verify a password against a user's stored hash.
     *
     * @param string $password Plain text password to verify
     * @param string $hash The stored password hash
     * @return bool
     */
    public static function verifyPassword(string $password, string $hash): bool
    {
        return password_verify($password, $hash);
    }
}
