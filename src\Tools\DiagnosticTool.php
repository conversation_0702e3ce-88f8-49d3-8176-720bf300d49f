<?php

namespace App\Tools;

use App\Core\Database;
use App\Core\Config;
use PDO;
use Exception;

/**
 * Diagnostic Tool for testing and debugging application components
 */
class DiagnosticTool
{
    private static $results = [];
    private static $errors = [];
    private static $warnings = [];
    private static $info = [];

    /**
     * Get test results
     *
     * @return array
     */
    public static function getResults(): array
    {
        return self::$results;
    }

    /**
     * Get test errors
     *
     * @return array
     */
    public static function getErrors(): array
    {
        return self::$errors;
    }

    /**
     * Get test warnings
     *
     * @return array
     */
    public static function getWarnings(): array
    {
        return self::$warnings;
    }

    /**
     * Get test info
     *
     * @return array
     */
    public static function getInfo(): array
    {
        return self::$info;
    }

    /**
     * Run all diagnostic tests
     *
     * @return array Results of all tests
     */
    public static function runAllTests(): array
    {
        self::testDatabaseConnection();
        self::testClientsTable();
        self::testClientRoutes();
        self::testClientController();
        self::testClientViews();

        return [
            'results' => self::$results,
            'errors' => self::$errors,
            'warnings' => self::$warnings,
            'info' => self::$info
        ];
    }

    /**
     * Test database connection
     */
    public static function testDatabaseConnection(): void
    {
        self::$results[] = "Testing database connection...";

        try {
            $config = Config::get('database');
            if (!$config) {
                self::$errors[] = "Database configuration not loaded.";
                return;
            }

            $default = $config['default'];
            $connectionConfig = $config['connections'][$default] ?? null;

            if (!$connectionConfig) {
                self::$errors[] = "Default database connection configuration ('{$default}') not found.";
                return;
            }

            $driver = $connectionConfig['driver'];
            $host = $connectionConfig['host'];
            $port = $connectionConfig['port'];
            $db = $connectionConfig['database'];
            $user = $connectionConfig['username'];
            $pass = $connectionConfig['password'];
            $charset = $connectionConfig['charset'];

            self::$info[] = "Database config: Driver: {$driver}, Host: {$host}, Port: {$port}, Database: {$db}, User: {$user}";

            $dsn = "";
            if ($driver === 'mysql') {
                $dsn = "mysql:host={$host};port={$port};dbname={$db};charset={$charset}";
            } elseif ($driver === 'pgsql') {
                $dsn = "pgsql:host={$host};port={$port};dbname={$db}";
            } else {
                self::$errors[] = "Unsupported database driver: {$driver}";
                return;
            }

            $options = [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
            ];

            $pdo = new PDO($dsn, $user, $pass, $options);
            self::$results[] = "Database connection successful!";

            // Test query execution
            $stmt = $pdo->query("SELECT 1 AS test");
            $result = $stmt->fetch();

            if ($result && isset($result['test']) && $result['test'] == 1) {
                self::$results[] = "Database query execution successful!";
            } else {
                self::$errors[] = "Database query execution failed.";
            }
        } catch (Exception $e) {
            self::$errors[] = "Database connection failed: " . $e->getMessage();
        }
    }

    /**
     * Test clients table structure and data
     */
    public static function testClientsTable(): void
    {
        self::$results[] = "Testing clients table...";

        try {
            // Check if table exists
            $sql = "SHOW TABLES LIKE 'clients'";
            Database::prepare($sql);
            Database::execute();
            $tableExists = Database::fetch();

            if (!$tableExists) {
                self::$errors[] = "Clients table does not exist.";
                return;
            }

            self::$results[] = "Clients table exists.";

            // Check table structure
            $sql = "DESCRIBE clients";
            Database::prepare($sql);
            Database::execute();
            $columns = Database::fetchAll();

            $requiredColumns = ['id', 'name', 'company', 'email', 'phone', 'address', 'city', 'state', 'postal_code', 'country', 'notes'];
            $missingColumns = [];

            $columnNames = array_column($columns, 'Field');
            self::$info[] = "Clients table columns: " . implode(', ', $columnNames);

            foreach ($requiredColumns as $column) {
                if (!in_array($column, $columnNames)) {
                    $missingColumns[] = $column;
                }
            }

            if (!empty($missingColumns)) {
                self::$errors[] = "Missing columns in clients table: " . implode(', ', $missingColumns);
            } else {
                self::$results[] = "Clients table structure is correct.";
            }

            // Check for data
            $sql = "SELECT COUNT(*) as count FROM clients";
            Database::prepare($sql);
            Database::execute();
            $result = Database::fetch();

            $count = $result['count'] ?? 0;
            self::$info[] = "Clients table has {$count} records.";

            if ($count == 0) {
                self::$warnings[] = "Clients table is empty.";
            }

            // Check for sample data
            if ($count > 0) {
                $sql = "SELECT * FROM clients LIMIT 1";
                Database::prepare($sql);
                Database::execute();
                $client = Database::fetch();

                self::$info[] = "Sample client data: " . print_r($client, true);
            }
        } catch (Exception $e) {
            self::$errors[] = "Error testing clients table: " . $e->getMessage();
        }
    }

    /**
     * Test client routes
     */
    public static function testClientRoutes(): void
    {
        self::$results[] = "Testing client routes...";

        $routes = [
            '/admin/clients' => 'Admin\ClientController@index',
            '/admin/clients/create' => 'Admin\ClientController@create',
            '/admin/clients/store' => 'Admin\ClientController@store',
            '/admin/clients/edit/{id}' => 'Admin\ClientController@edit',
            '/admin/clients/update/{id}' => 'Admin\ClientController@update',
            '/admin/clients/delete/{id}' => 'Admin\ClientController@delete'
        ];

        $routeFile = file_get_contents(ROOT_DIR . '/routes/web.php');

        foreach ($routes as $route => $handler) {
            if (strpos($routeFile, $route) !== false && strpos($routeFile, $handler) !== false) {
                self::$results[] = "Route {$route} => {$handler} exists.";
            } else {
                self::$errors[] = "Route {$route} => {$handler} not found in routes file.";
            }
        }
    }

    /**
     * Test client controller
     */
    public static function testClientController(): void
    {
        self::$results[] = "Testing client controller...";

        $controllerFile = ROOT_DIR . '/src/Controllers/Admin/ClientController.php';

        if (!file_exists($controllerFile)) {
            self::$errors[] = "ClientController.php file not found.";
            return;
        }

        self::$results[] = "ClientController.php file exists.";

        $controllerContent = file_get_contents($controllerFile);

        $requiredMethods = ['index', 'create', 'store', 'edit', 'update', 'delete'];

        foreach ($requiredMethods as $method) {
            if (strpos($controllerContent, "public function {$method}") !== false) {
                self::$results[] = "ClientController::{$method} method exists.";
            } else {
                self::$errors[] = "ClientController::{$method} method not found.";
            }
        }

        // Check for namespace
        if (strpos($controllerContent, "namespace App\Controllers\Admin") !== false) {
            self::$results[] = "ClientController has correct namespace.";
        } else {
            self::$errors[] = "ClientController has incorrect namespace.";
        }

        // Check for model usage
        if (strpos($controllerContent, "use App\Models\Client") !== false) {
            self::$results[] = "ClientController uses Client model.";
        } else {
            self::$errors[] = "ClientController does not use Client model.";
        }
    }

    /**
     * Test client views
     */
    public static function testClientViews(): void
    {
        self::$results[] = "Testing client views...";

        $viewsPath = ROOT_DIR . '/src/Views/admin/clients';

        if (!is_dir($viewsPath)) {
            self::$errors[] = "Client views directory not found.";
            return;
        }

        self::$results[] = "Client views directory exists.";

        $requiredViews = ['index.php', 'form.php'];

        foreach ($requiredViews as $view) {
            if (file_exists($viewsPath . '/' . $view)) {
                self::$results[] = "Client view {$view} exists.";

                // Check view content
                $viewContent = file_get_contents($viewsPath . '/' . $view);

                if ($view === 'index.php') {
                    if (strpos($viewContent, 'table') !== false) {
                        self::$results[] = "Client index view contains a table.";
                    } else {
                        self::$warnings[] = "Client index view does not contain a table.";
                    }
                }

                if ($view === 'form.php') {
                    if (strpos($viewContent, '<form') !== false) {
                        self::$results[] = "Client form view contains a form.";
                    } else {
                        self::$errors[] = "Client form view does not contain a form.";
                    }
                }
            } else {
                self::$errors[] = "Client view {$view} not found.";
            }
        }
    }

    /**
     * Format results as HTML
     */
    public static function formatResultsAsHtml(array $testResults): string
    {
        $html = '<div style="font-family: Arial, sans-serif; max-width: 1200px; margin: 0 auto; padding: 20px;">';
        $html .= '<h1 style="color: #333;">Diagnostic Test Results</h1>';

        // Results
        $html .= '<div style="margin-bottom: 20px;">';
        $html .= '<h2 style="color: #333;">Test Results</h2>';
        $html .= '<ul style="list-style-type: none; padding: 0;">';
        foreach ($testResults['results'] as $result) {
            $html .= '<li style="padding: 8px; margin-bottom: 5px; background-color: #f0f0f0; border-left: 4px solid #4CAF50;">' . htmlspecialchars($result) . '</li>';
        }
        $html .= '</ul>';
        $html .= '</div>';

        // Errors
        if (!empty($testResults['errors'])) {
            $html .= '<div style="margin-bottom: 20px;">';
            $html .= '<h2 style="color: #D32F2F;">Errors</h2>';
            $html .= '<ul style="list-style-type: none; padding: 0;">';
            foreach ($testResults['errors'] as $error) {
                $html .= '<li style="padding: 8px; margin-bottom: 5px; background-color: #FFEBEE; border-left: 4px solid #D32F2F;">' . htmlspecialchars($error) . '</li>';
            }
            $html .= '</ul>';
            $html .= '</div>';
        }

        // Warnings
        if (!empty($testResults['warnings'])) {
            $html .= '<div style="margin-bottom: 20px;">';
            $html .= '<h2 style="color: #FF9800;">Warnings</h2>';
            $html .= '<ul style="list-style-type: none; padding: 0;">';
            foreach ($testResults['warnings'] as $warning) {
                $html .= '<li style="padding: 8px; margin-bottom: 5px; background-color: #FFF8E1; border-left: 4px solid #FF9800;">' . htmlspecialchars($warning) . '</li>';
            }
            $html .= '</ul>';
            $html .= '</div>';
        }

        // Info
        if (!empty($testResults['info'])) {
            $html .= '<div style="margin-bottom: 20px;">';
            $html .= '<h2 style="color: #1976D2;">Information</h2>';
            $html .= '<ul style="list-style-type: none; padding: 0;">';
            foreach ($testResults['info'] as $info) {
                $html .= '<li style="padding: 8px; margin-bottom: 5px; background-color: #E3F2FD; border-left: 4px solid #1976D2;">' . htmlspecialchars($info) . '</li>';
            }
            $html .= '</ul>';
            $html .= '</div>';
        }

        $html .= '</div>';

        return $html;
    }
}
