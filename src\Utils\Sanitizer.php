<?php

namespace App\Utils;

class Sanitizer
{
    /**
     * Sanitize a string, removing potentially harmful characters or tags.
     *
     * @param string|null $input The input string.
     * @param bool $stripTags Whether to strip HTML and PHP tags.
     * @return string The sanitized string.
     */
    public static function sanitizeString(?string $input, bool $stripTags = true): string
    {
        if ($input === null) {
            return '';
        }

        $output = trim($input);

        if ($stripTags) {
            $output = strip_tags($output);
        }

        // Use filter_var for basic sanitization (removes chars with ASCII value > 127 without flags)
        // FILTER_SANITIZE_SPECIAL_CHARS could be used instead of htmlspecialchars in View::e
        // but applying it here might be too aggressive depending on needs.
        // Let's stick to basic trimming and optional tag stripping for now.
        // $output = filter_var($output, FILTER_SANITIZE_STRING, FILTER_FLAG_NO_ENCODE_QUOTES); // Deprecated in PHP 8.0
        // Use htmlspecialchars for output encoding (in View::e), not general sanitization here.

        // Consider additional specific sanitization if needed, e.g., removing control characters:
        // $output = preg_replace('/[\x00-\x1F\x7F]/u', '', $output);

        return $output;
    }

    /**
     * Sanitize an email address.
     *
     * @param string|null $input The input email.
     * @return string The sanitized email or an empty string if invalid format.
     */
    public static function sanitizeEmail(?string $input): string
    {
        if ($input === null) {
            return '';
        }
        $sanitized = filter_var(trim($input), FILTER_SANITIZE_EMAIL);
        // Re-validate after sanitizing, as sanitization might create invalid emails
        if (filter_var($sanitized, FILTER_VALIDATE_EMAIL)) {
            return $sanitized;
        }
        return ''; // Return empty if invalid
    }

    /**
     * Sanitize a URL.
     *
     * @param string|null $input The input URL.
     * @return string The sanitized URL or an empty string if invalid format.
     */
    public static function sanitizeUrl(?string $input): string
    {
        if ($input === null) {
            return '';
        }
        $sanitized = filter_var(trim($input), FILTER_SANITIZE_URL);
         // Re-validate after sanitizing
        if (filter_var($sanitized, FILTER_VALIDATE_URL)) {
            return $sanitized;
        }
         return ''; // Return empty if invalid
    }

    /**
     * Sanitize an integer.
     *
     * @param mixed $input The input value.
     * @return int The sanitized integer (0 if invalid).
     */
    public static function sanitizeInt(mixed $input): int
    {
        return filter_var($input, FILTER_SANITIZE_NUMBER_INT);
        // Note: filter_var returns string on success, so cast might be needed depending on usage
        // return (int) filter_var($input, FILTER_SANITIZE_NUMBER_INT);
        // Or use intval after basic checks
        // return intval(preg_replace('/[^0-9-]/', '', $input));
    }

     /**
     * Sanitize a float/decimal number.
     *
     * @param mixed $input The input value.
     * @param int $flags Flags for filter_var (e.g., FILTER_FLAG_ALLOW_FRACTION).
     * @return float The sanitized float (0.0 if invalid).
     */
    public static function sanitizeFloat(mixed $input, int $flags = FILTER_FLAG_ALLOW_FRACTION): float
    {
         // FILTER_SANITIZE_NUMBER_FLOAT allows scientific notation, commas, etc.
         // Use FILTER_FLAG_ALLOW_FRACTION, FILTER_FLAG_ALLOW_THOUSAND, FILTER_FLAG_ALLOW_SCIENTIFIC as needed.
        return filter_var($input, FILTER_SANITIZE_NUMBER_FLOAT, $flags);
        // return (float) filter_var($input, FILTER_SANITIZE_NUMBER_FLOAT, $flags);
    }

    /**
     * Sanitize an array recursively.
     * Applies sanitizeString to all string values in the array.
     *
     * @param array $input The input array.
     * @param bool $stripTags Whether to strip tags from strings.
     * @return array The sanitized array.
     */
    public static function sanitizeArray(array $input, bool $stripTags = true): array
    {
        $sanitizedArray = [];
        foreach ($input as $key => $value) {
            // Sanitize the key as well if needed (usually keys are safe, but depends on source)
            $sanitizedKey = self::sanitizeString((string)$key, true); // Ensure key is string and sanitized

            if (is_array($value)) {
                $sanitizedArray[$sanitizedKey] = self::sanitizeArray($value, $stripTags);
            } elseif (is_string($value)) {
                $sanitizedArray[$sanitizedKey] = self::sanitizeString($value, $stripTags);
            } else {
                // Keep non-string, non-array values as is (e.g., numbers, booleans)
                // Consider specific sanitization for other types if necessary
                $sanitizedArray[$sanitizedKey] = $value;
            }
        }
        return $sanitizedArray;
    }
}
