<?php

namespace App\Utils;

use App\Core\Config; // Use Config to get BASE_URL if needed

class UrlHelper
{
    /**
     * Generate an absolute URL for a given path within the application.
     * Respects the BASE_URL defined in config/paths.php or .env.
     *
     * @param string $path The relative path (e.g., '/users', '/admin/settings').
     * @param array $params Optional query parameters as key-value pairs.
     * @return string The absolute URL.
     */
    public static function url(string $path, array $params = []): string
    {
        // Use APP_URL from config as the definitive base
        $baseUrl = Config::get('app.url', 'http://localhost'); // Fallback just in case
        $url = rtrim($baseUrl, '/') . '/' . ltrim($path, '/');

        if (!empty($params)) {
            $url .= '?' . http_build_query($params);
        }

        return $url;
    }

    /**
     * Generate an absolute URL for an asset file (CSS, JS, images).
     * Assumes assets are served from the public directory.
     *
     * @param string $path The relative path from the public directory (e.g., 'css/app.css', 'images/logo.png').
     * @param bool $addVersion Add version query string for cache busting if in production and versioning is enabled.
     * @return string The absolute URL to the asset.
     */
    public static function assetUrl(string $path, bool $addVersion = true): string
    {
        // Use APP_URL from config as the definitive base
        $baseUrl = Config::get('app.url', 'http://localhost'); // Fallback just in case
        $assetBase = rtrim($baseUrl, '/'); // Assets are relative to the public base URL (APP_URL)

        $filePath = ltrim($path, '/');

        // Basic versioning placeholder - In a real app with Laravel Mix,
        // you'd read the mix-manifest.json file.
        if ($addVersion && Config::get('app.env') === 'production') {
             // Simple timestamp versioning as a fallback
             $version = time(); // Replace with manifest lookup later
             $filePath .= '?v=' . $version;
        }

        return $assetBase . '/' . $filePath;
    }

    /**
     * Generate a URL for a named route (if using a more advanced router).
     * Placeholder for future implementation if needed.
     *
     * @param string $routeName The name of the route.
     * @param array $parameters Route parameters.
     * @return string The absolute URL.
     * @throws \Exception If route name is not found (when implemented).
     */
    public static function routeUrl(string $routeName, array $parameters = []): string
    {
        // This requires the Router to support named routes and provide a lookup mechanism.
        // Placeholder implementation:
        throw new \Exception("Named route functionality ('{$routeName}') not yet implemented.");
        // Example future logic:
        // $uri = App::getInstance()->getRouter()->getUriByName($routeName, $parameters);
        // return self::url($uri);
    }

    /**
     * Redirect to a given path or URL.
     *
     * @param string $path The relative path or absolute URL.
     * @param int $statusCode HTTP status code for redirection (default 302).
     */
    public static function redirect(string $path, int $statusCode = 302): void
    {
        // Check if it's an absolute URL
        if (filter_var($path, FILTER_VALIDATE_URL)) {
            $url = $path;
        } else {
            // Assume it's a relative path within the app
            $url = self::url($path);
        }

        header('Location: ' . $url, true, $statusCode);
        exit;
    }

     /**
     * Get the current full URL.
     *
     * @return string
     */
    public static function currentUrl(): string
    {
        $protocol = (!empty($_SERVER['HTTPS']) && $_SERVER['HTTPS'] !== 'off' || $_SERVER['SERVER_PORT'] == 443) ? "https://" : "http://";
        $host = $_SERVER['HTTP_HOST'] ?? 'localhost';
        $uri = $_SERVER['REQUEST_URI'] ?? '/';
        return $protocol . $host . $uri;
    }

    /**
     * Get the current request URI path.
     *
     * @return string
     */
    public static function currentPath(): string
    {
        return parse_url($_SERVER['REQUEST_URI'] ?? '/', PHP_URL_PATH);
    }
}
