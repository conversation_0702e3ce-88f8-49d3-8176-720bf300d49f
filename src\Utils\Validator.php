<?php

namespace App\Utils;

class Validator
{
    private array $data;
    private array $rules;
    private array $errors = [];
    private array $customMessages = [];

    /**
     * Create a new Validator instance.
     *
     * @param array $data The data to validate.
     * @param array $rules The validation rules.
     * @param array $messages Custom error messages.
     */
    public function __construct(array $data, array $rules, array $messages = [])
    {
        $this->data = $data;
        $this->rules = $rules;
        $this->customMessages = $messages;
    }

    /**
     * Static factory method.
     *
     * @param array $data
     * @param array $rules
     * @param array $messages
     * @return static
     */
    public static function make(array $data, array $rules, array $messages = []): static
    {
        return new static($data, $rules, $messages);
    }

    /**
     * Run the validation checks.
     *
     * @return bool True if validation passes, false otherwise.
     */
    public function validate(): bool
    {
        $this->errors = []; // Reset errors

        foreach ($this->rules as $field => $fieldRules) {
            $rulesArray = is_string($fieldRules) ? explode('|', $fieldRules) : $fieldRules;
            $value = $this->data[$field] ?? null;

            foreach ($rulesArray as $rule) {
                $params = [];
                if (str_contains($rule, ':')) {
                    [$rule, $paramString] = explode(':', $rule, 2);
                    $params = explode(',', $paramString);
                }

                $method = 'validate' . ucfirst($rule);

                if (method_exists($this, $method)) {
                    if (!$this->$method($field, $value, $params)) {
                        $this->addError($field, $rule, $params);
                        // Stop validating this field on first error? Optional.
                        // break;
                    }
                } else {
                    // Handle unknown validation rule
                    // throw new \Exception("Unknown validation rule: {$rule}");
                    // Or log an error, or ignore
                }
            }
        }

        return empty($this->errors);
    }

    /**
     * Check if validation failed.
     *
     * @return bool
     */
    public function fails(): bool
    {
        return !$this->validate();
    }

    /**
     * Check if validation passed.
     *
     * @return bool
     */
    public function passes(): bool
    {
        return $this->validate();
    }


    /**
     * Get the validation errors.
     *
     * @return array
     */
    public function errors(): array
    {
        return $this->errors;
    }

    /**
     * Get the first error message for a specific field.
     *
     * @param string $field
     * @return string|null
     */
    public function firstError(string $field): ?string
    {
        return $this->errors[$field][0] ?? null;
    }

    /**
     * Add an error message for a field.
     *
     * @param string $field
     * @param string $rule
     * @param array $params
     */
    protected function addError(string $field, string $rule, array $params = []): void
    {
        $message = $this->getErrorMessage($field, $rule, $params);
        $this->errors[$field][] = $message;
    }

    /**
     * Get the appropriate error message for a rule.
     *
     * @param string $field
     * @param string $rule
     * @param array $params
     * @return string
     */
    protected function getErrorMessage(string $field, string $rule, array $params): string
    {
        $customKey = "{$field}.{$rule}";
        if (isset($this->customMessages[$customKey])) {
            $message = $this->customMessages[$customKey];
        } else {
            // Default messages
            $defaultMessages = [
                'required' => "The {$field} field is required.",
                'email' => "The {$field} must be a valid email address.",
                'numeric' => "The {$field} must be a number.",
                'integer' => "The {$field} must be an integer.",
                'string' => "The {$field} must be a string.",
                'min' => "The {$field} must be at least :min characters.", // Placeholder for length/value
                'max' => "The {$field} must not be greater than :max characters.", // Placeholder for length/value
                'in' => "The selected {$field} is invalid.",
                'date' => "The {$field} must be a valid date.",
                'url' => "The {$field} must be a valid URL.",
                'unique' => "The {$field} has already been taken.", // Needs DB check
                'confirmed' => "The {$field} confirmation does not match.",
                // Add more default messages
            ];
            $message = $this->customMessages[$rule] ?? $defaultMessages[$rule] ?? "The {$field} field is invalid.";
        }

        // Replace placeholders like :min, :max, :values
        if (!empty($params)) {
            switch ($rule) {
                case 'min':
                case 'max':
                    $message = str_replace(":" . $rule, $params[0], $message);
                    break;
                case 'in':
                    $message = str_replace(":values", implode(', ', $params), $message);
                    break;
                // Add more placeholder replacements
            }
        }
        // Replace :field placeholder
        $message = str_replace(':field', $field, $message);


        return $message;
    }

    // --- Validation Rule Methods ---

    protected function validateRequired(string $field, mixed $value, array $params): bool
    {
        if (is_null($value)) {
            return false;
        } elseif (is_string($value) && trim($value) === '') {
            return false;
        } elseif (is_array($value) && empty($value)) {
            return false;
        }
        // Consider file uploads if needed: check $_FILES[$field]['error'] != UPLOAD_ERR_NO_FILE
        return true;
    }

    protected function validateEmail(string $field, mixed $value, array $params): bool
    {
        if (is_null($value) || $value === '') return true; // Allow empty if not required
        return filter_var($value, FILTER_VALIDATE_EMAIL) !== false;
    }

    protected function validateNumeric(string $field, mixed $value, array $params): bool
    {
        if (is_null($value) || $value === '') return true;
        return is_numeric($value);
    }

     protected function validateInteger(string $field, mixed $value, array $params): bool
    {
        if (is_null($value) || $value === '') return true;
        return filter_var($value, FILTER_VALIDATE_INT) !== false;
    }

    protected function validateString(string $field, mixed $value, array $params): bool
    {
         if (is_null($value) || $value === '') return true;
         return is_string($value);
    }

    protected function validateMin(string $field, mixed $value, array $params): bool
    {
        if (is_null($value) || $value === '') return true;
        $min = $params[0] ?? 0;

        if (is_numeric($value)) {
            return $value >= $min;
        } elseif (is_string($value)) {
            return mb_strlen($value) >= $min;
        } elseif (is_array($value)) {
            return count($value) >= $min;
        }
        // Add file size check if needed
        return false;
    }

    protected function validateMax(string $field, mixed $value, array $params): bool
    {
        if (is_null($value) || $value === '') return true;
        $max = $params[0] ?? PHP_INT_MAX;

        if (is_numeric($value)) {
            return $value <= $max;
        } elseif (is_string($value)) {
            return mb_strlen($value) <= $max;
        } elseif (is_array($value)) {
            return count($value) <= $max;
        }
        // Add file size check if needed
        return false;
    }

    protected function validateIn(string $field, mixed $value, array $params): bool
    {
        if (is_null($value) || $value === '') return true;
        return in_array($value, $params);
    }

    protected function validateDate(string $field, mixed $value, array $params): bool
    {
        if (is_null($value) || $value === '') return true;
        // Basic check, might need more robust parsing/validation depending on expected format
        $d = \DateTime::createFromFormat('Y-m-d', $value); // Example format
        if ($d && $d->format('Y-m-d') === $value) return true;
        $d = \DateTime::createFromFormat('Y-m-d H:i:s', $value); // Example format
        if ($d && $d->format('Y-m-d H:i:s') === $value) return true;
        // Add other formats as needed
        return false;
    }

     protected function validateUrl(string $field, mixed $value, array $params): bool
    {
        if (is_null($value) || $value === '') return true;
        return filter_var($value, FILTER_VALIDATE_URL) !== false;
    }

    protected function validateConfirmed(string $field, mixed $value, array $params): bool
    {
        $confirmationField = $field . '_confirmation';
        return isset($this->data[$confirmationField]) && $value === $this->data[$confirmationField];
    }

    // --- Placeholder for Database Dependent Rules ---

    protected function validateUnique(string $field, mixed $value, array $params): bool
    {
        if (is_null($value) || $value === '') return true;
        // Requires Database access
        // Example: $table = $params[0]; $column = $params[1] ?? $field; $exceptId = $params[2] ?? null;
        // $count = Database::queryOne("SELECT COUNT(*) as c FROM {$table} WHERE {$column} = ? AND id != ?", [$value, $exceptId])['c'];
        // return $count === 0;
        // For now, assume it passes until DB is integrated
        return true; // Placeholder
    }

}
