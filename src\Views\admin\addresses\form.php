<?php
// Address form view (used for both create and edit)
$isEdit = isset($address) && $address !== null;
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="text-2xl font-semibold"><?= $isEdit ? 'Edit Address' : 'Add New Address' ?></h1>
    <a href="<?= App\Core\View::url('/admin/clients/' . $client['id'] . '/addresses') ?>" class="btn btn-primary">
        <i class="fas fa-arrow-left"></i> Back to Addresses
    </a>
</div>

<?php if (isset($flash_error)): ?>
    <div class="alert alert-error">
        <?= App\Core\View::e($flash_error) ?>
    </div>
<?php endif; ?>

<?php if (isset($errors) && !empty($errors)): ?>
    <div class="alert alert-error">
        <strong>Please fix the following errors:</strong>
        <ul>
            <?php foreach ($errors as $field => $error): ?>
                <li><?= App\Core\View::e($error) ?></li>
            <?php endforeach; ?>
        </ul>
    </div>
<?php endif; ?>

<div class="card shadow-sm">
    <div class="card-header d-flex justify-content-between align-items-center">
        <span class="font-medium"><?= $isEdit ? 'Edit Address Details' : 'Address Details' ?></span>
    </div>
    <div class="card-body">
        <form action="<?= App\Core\View::url($formAction) ?>" method="POST">
            <?= App\Core\View::csrfField() ?>

            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="name">Name</label>
                        <input type="text" id="name" name="name" class="form-control"
                            value="<?= isset($old['name']) ? App\Core\View::e($old['name']) : ($isEdit ? App\Core\View::e($address['name']) : '') ?>"
                            required>
                    </div>
                </div>

                <div class="col-md-6">
                    <div class="form-group">
                        <label for="company">Company (Optional)</label>
                        <input type="text" id="company" name="company" class="form-control"
                            value="<?= isset($old['company']) ? App\Core\View::e($old['company']) : ($isEdit ? App\Core\View::e($address['company'] ?? '') : '') ?>">
                    </div>
                </div>
            </div>

            <div class="form-group">
                <label for="address_line1">Address Line 1</label>
                <input type="text" id="address_line1" name="address_line1" class="form-control"
                    value="<?= isset($old['address_line1']) ? App\Core\View::e($old['address_line1']) : ($isEdit ? App\Core\View::e($address['address_line1']) : '') ?>"
                    required>
            </div>

            <div class="form-group">
                <label for="address_line2">Address Line 2 (Optional)</label>
                <input type="text" id="address_line2" name="address_line2" class="form-control"
                    value="<?= isset($old['address_line2']) ? App\Core\View::e($old['address_line2']) : ($isEdit ? App\Core\View::e($address['address_line2'] ?? '') : '') ?>">
            </div>

            <div class="row">
                <div class="col-md-4">
                    <div class="form-group">
                        <label for="city">City</label>
                        <input type="text" id="city" name="city" class="form-control"
                            value="<?= isset($old['city']) ? App\Core\View::e($old['city']) : ($isEdit ? App\Core\View::e($address['city']) : '') ?>"
                            required>
                    </div>
                </div>

                <div class="col-md-4">
                    <div class="form-group">
                        <label for="state">State/Province (Optional)</label>
                        <input type="text" id="state" name="state" class="form-control"
                            value="<?= isset($old['state']) ? App\Core\View::e($old['state']) : ($isEdit ? App\Core\View::e($address['state'] ?? '') : '') ?>">
                    </div>
                </div>

                <div class="col-md-4">
                    <div class="form-group">
                        <label for="postal_code">Postal Code (Optional)</label>
                        <input type="text" id="postal_code" name="postal_code" class="form-control"
                            value="<?= isset($old['postal_code']) ? App\Core\View::e($old['postal_code']) : ($isEdit ? App\Core\View::e($address['postal_code'] ?? '') : '') ?>">
                    </div>
                </div>
            </div>

            <div class="form-group">
                <label for="country">Country (Optional)</label>
                <input type="text" id="country" name="country" class="form-control"
                    value="<?= isset($old['country']) ? App\Core\View::e($old['country']) : ($isEdit ? App\Core\View::e($address['country'] ?? '') : '') ?>">
            </div>

            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="phone">Phone (Optional)</label>
                        <input type="text" id="phone" name="phone" class="form-control"
                            value="<?= isset($old['phone']) ? App\Core\View::e($old['phone']) : ($isEdit ? App\Core\View::e($address['phone'] ?? '') : '') ?>">
                    </div>
                </div>

                <div class="col-md-6">
                    <div class="form-group">
                        <label for="email">Email (Optional)</label>
                        <input type="email" id="email" name="email" class="form-control"
                            value="<?= isset($old['email']) ? App\Core\View::e($old['email']) : ($isEdit ? App\Core\View::e($address['email'] ?? '') : '') ?>">
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="type">Address Type</label>
                        <select id="type" name="type" class="form-control">
                            <?php
                            $types = ['shipping' => 'Shipping', 'billing' => 'Billing', 'both' => 'Shipping & Billing'];
                            $selectedType = isset($old['type']) ? $old['type'] : ($isEdit ? $address['type'] : 'shipping');
                            
                            foreach ($types as $value => $label):
                            ?>
                                <option value="<?= $value ?>" <?= $selectedType === $value ? 'selected' : '' ?>>
                                    <?= App\Core\View::e($label) ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                </div>

                <div class="col-md-6">
                    <div class="form-group mt-4">
                        <div class="form-check">
                            <input type="checkbox" class="form-check-input" id="is_default" name="is_default" value="1"
                                <?= isset($old['is_default']) ? ($old['is_default'] ? 'checked' : '') : ($isEdit && $address['is_default'] ? 'checked' : '') ?>>
                            <label class="form-check-label" for="is_default">Set as Default Address</label>
                        </div>
                    </div>
                </div>
            </div>

            <div class="form-group mt-4">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save"></i> <?= $isEdit ? 'Update Address' : 'Save Address' ?>
                </button>
                <a href="<?= App\Core\View::url('/admin/clients/' . $client['id'] . '/addresses') ?>" class="btn btn-secondary">
                    <i class="fas fa-times"></i> Cancel
                </a>
            </div>
        </form>
    </div>
</div>
