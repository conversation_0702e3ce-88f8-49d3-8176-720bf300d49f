<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="text-2xl font-semibold">Addresses for <?= App\Core\View::e($client['name']) ?></h1>
    <div>
        <a href="<?= App\Core\View::url('/admin/clients/' . $client['id'] . '/addresses/create') ?>" class="btn btn-primary">
            <i class="fas fa-plus"></i> Add New Address
        </a>
        <a href="<?= App\Core\View::url('/admin/clients') ?>" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Back to Clients
        </a>
    </div>
</div>

<div class="card shadow-sm">
    <div class="card-header d-flex justify-content-between align-items-center">
        <span class="font-medium">Client Addresses</span>
    </div>
    <div class="card-body p-0">
        <div class="table-container">
            <table class="table table-striped">
                <thead>
                    <tr>
                        <th>Name</th>
                        <th>Address</th>
                        <th>Contact</th>
                        <th>Type</th>
                        <th>Default</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php if (empty($addresses)): ?>
                        <tr>
                            <td colspan="6" class="text-center">No addresses found.</td>
                        </tr>
                    <?php else: ?>
                        <?php foreach ($addresses as $address): ?>
                            <tr>
                                <td>
                                    <strong><?= App\Core\View::e($address['name']) ?></strong>
                                    <?php if (!empty($address['company'])): ?>
                                        <br><small><?= App\Core\View::e($address['company']) ?></small>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?= App\Core\View::e($address['address_line1']) ?>
                                    <?php if (!empty($address['address_line2'])): ?>
                                        <br><?= App\Core\View::e($address['address_line2']) ?>
                                    <?php endif; ?>
                                    <br><?= App\Core\View::e($address['city']) ?>
                                    <?php if (!empty($address['state'])): ?>
                                        , <?= App\Core\View::e($address['state']) ?>
                                    <?php endif; ?>
                                    <?php if (!empty($address['postal_code'])): ?>
                                        <?= App\Core\View::e($address['postal_code']) ?>
                                    <?php endif; ?>
                                    <?php if (!empty($address['country'])): ?>
                                        <br><?= App\Core\View::e($address['country']) ?>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php if (!empty($address['phone'])): ?>
                                        <i class="fas fa-phone"></i> <?= App\Core\View::e($address['phone']) ?><br>
                                    <?php endif; ?>
                                    <?php if (!empty($address['email'])): ?>
                                        <i class="fas fa-envelope"></i> <?= App\Core\View::e($address['email']) ?>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php
                                    $typeLabel = 'Shipping';
                                    $typeClass = 'bg-info';
                                    
                                    if ($address['type'] === 'billing') {
                                        $typeLabel = 'Billing';
                                        $typeClass = 'bg-warning';
                                    } elseif ($address['type'] === 'both') {
                                        $typeLabel = 'Shipping & Billing';
                                        $typeClass = 'bg-primary';
                                    }
                                    ?>
                                    <span class="badge <?= $typeClass ?>"><?= $typeLabel ?></span>
                                </td>
                                <td>
                                    <?php if ($address['is_default']): ?>
                                        <span class="badge bg-success">Default</span>
                                    <?php else: ?>
                                        <span class="badge bg-secondary">No</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <a href="<?= App\Core\View::url('/admin/clients/' . $client['id'] . '/addresses/edit/' . $address['id']) ?>" class="btn btn-sm btn-primary">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <form action="<?= App\Core\View::url('/admin/clients/' . $client['id'] . '/addresses/delete/' . $address['id']) ?>" method="POST" class="d-inline">
                                        <?= App\Core\View::csrfField() ?>
                                        <button type="submit" class="btn btn-sm btn-danger" onclick="return confirm('Are you sure you want to delete this address?')">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </form>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>
