<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Login - <?= App\Core\Config::get('app.name', 'ELTA') ?></title>
    <!-- Link to specific login CSS if needed -->
    <style>
        body { font-family: sans-serif; background: #f4f4f4; display: flex; justify-content: center; align-items: center; height: 100vh; margin: 0; }
        .login-container { background: #fff; padding: 30px; border-radius: 5px; box-shadow: 0 0 10px rgba(0,0,0,0.1); width: 300px; }
        .login-container h1 { text-align: center; margin-bottom: 20px; }
        .form-group { margin-bottom: 15px; }
        .form-group label { display: block; margin-bottom: 5px; }
        .form-group input[type="email"],
        .form-group input[type="password"] { width: 100%; padding: 8px; box-sizing: border-box; border: 1px solid #ccc; border-radius: 3px; }
        .form-group button { width: 100%; padding: 10px; background: #333; color: #fff; border: none; border-radius: 3px; cursor: pointer; }
        .form-group button:hover { background: #555; }
        .error-message { color: #a94442; background-color: #f2dede; border: 1px solid #ebccd1; padding: 10px; border-radius: 4px; margin-bottom: 15px; font-size: 0.9em; }
        .error-list { list-style: none; padding: 0; margin: 0; }
        .flash-error { color: #a94442; background-color: #f2dede; border: 1px solid #ebccd1; padding: 10px; border-radius: 4px; margin-bottom: 15px; }
    </style>
</head>
<body>
    <div class="login-container">
        <h1>Admin Login</h1>

        <!-- Session Flash Error -->
        <?= App\Core\View::flashMessage('error', 'error') ?>

        <!-- Validation Errors -->
        <?php if (!empty($errors)): ?>
            <div class="error-message">
                <ul class="error-list">
                    <?php if (is_array($errors)): ?>
                        <?php foreach ($errors as $fieldErrors): ?>
                            <?php if (is_array($fieldErrors)): ?>
                                <?php foreach ($fieldErrors as $error): ?>
                                    <li><?= App\Core\View::e($error) ?></li>
                                <?php endforeach; ?>
                            <?php else: ?>
                                <li><?= App\Core\View::e($fieldErrors) ?></li>
                            <?php endif; ?>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <li><?= App\Core\View::e($errors) ?></li>
                    <?php endif; ?>
                </ul>
            </div>
        <?php endif; ?>

        <form action="<?= App\Core\View::url('/admin/login') ?>" method="POST">
            <?= App\Core\View::csrfField() ?>
            <div class="form-group">
                <label for="email">Email:</label>
                <input type="email" id="email" name="email" value="<?= App\Core\View::e($old['email'] ?? '') ?>" required>
            </div>
            <div class="form-group">
                <label for="password">Password:</label>
                <input type="password" id="password" name="password" required>
            </div>
            <!-- Optional: Remember Me -->
            <!-- <div class="form-group">
                <input type="checkbox" id="remember" name="remember">
                <label for="remember">Remember Me</label>
            </div> -->
            <div class="form-group">
                <button type="submit">Login</button>
            </div>
        </form>
    </div>
</body>
</html>
