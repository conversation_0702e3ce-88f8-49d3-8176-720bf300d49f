<?php
// Client form view (used for both create and edit)
$isEdit = isset($client) && $client !== null;
$formAction = $isEdit ? App\Core\View::url('/admin/clients/update/' . $client['id']) : App\Core\View::url('/admin/clients/store');

// Prepare old input or client data
$old = $old ?? [];
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 fw-bold"><?= $isEdit ? 'Edit Client' : 'Add New Client' ?></h1>
        <p class="text-muted">Fill in the details below to <?= $isEdit ? 'update' : 'create' ?> a client</p>
    </div>
    <a href="<?= App\Core\View::url('/admin/clients') ?>" class="btn btn-outline-primary">
        <i class="fas fa-arrow-left me-1"></i> Back to Clients
    </a>
</div>

<?php if (isset($flash_error)): ?>
    <div class="alert alert-danger d-flex align-items-center mb-4" role="alert">
        <i class="fas fa-exclamation-circle me-2"></i>
        <div>
            <?= App\Core\View::e($flash_error) ?>
        </div>
    </div>
<?php endif; ?>

<?php if (isset($errors) && !empty($errors)): ?>
    <div class="alert alert-danger mb-4" role="alert">
        <div class="d-flex align-items-center">
            <i class="fas fa-exclamation-triangle me-2"></i>
            <strong>Please fix the following errors:</strong>
        </div>
        <ul class="mt-2 ms-4 mb-0">
            <?php foreach ($errors as $field => $error): ?>
                <li><?= App\Core\View::e($error) ?></li>
            <?php endforeach; ?>
        </ul>
    </div>
<?php endif; ?>

<div class="card shadow-sm">
    <div class="card-header bg-white py-3">
        <h5 class="mb-0 fw-bold">Client Information</h5>
    </div>
    <div class="card-body">
        <form action="<?= $formAction ?>" method="POST" id="clientForm">
            <?= App\Core\View::csrfField() ?>
            <?php if ($isEdit): ?>
                <input type="hidden" name="_method" value="PUT">
            <?php endif; ?>

            <div class="row g-3">
                <!-- Basic Information -->
                <div class="col-md-6">
                    <div class="form-group mb-3">
                        <label for="name" class="form-label fw-bold">Name <span class="text-danger">*</span></label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-user"></i></span>
                            <input type="text" id="name" name="name" class="form-control"
                                value="<?= isset($old['name']) ? App\Core\View::e($old['name']) : ($isEdit ? App\Core\View::e($client['name']) : '') ?>"
                                required>
                        </div>
                    </div>
                </div>

                <div class="col-md-6">
                    <div class="form-group mb-3">
                        <label for="company" class="form-label fw-bold">Company</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-building"></i></span>
                            <input type="text" id="company" name="company" class="form-control"
                                value="<?= isset($old['company']) ? App\Core\View::e($old['company']) : ($isEdit ? App\Core\View::e($client['company'] ?? '') : '') ?>">
                        </div>
                    </div>
                </div>

                <div class="col-md-6">
                    <div class="form-group mb-3">
                        <label for="email" class="form-label fw-bold">Email <span class="text-danger">*</span></label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-envelope"></i></span>
                            <input type="email" id="email" name="email" class="form-control"
                                value="<?= isset($old['email']) ? App\Core\View::e($old['email']) : ($isEdit ? App\Core\View::e($client['email']) : '') ?>"
                                required>
                        </div>
                    </div>
                </div>

                <div class="col-md-6">
                    <div class="form-group mb-3">
                        <label for="phone" class="form-label fw-bold">Phone <span class="text-danger">*</span></label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-phone"></i></span>
                            <input type="tel" id="phone" name="phone" class="form-control"
                                value="<?= isset($old['phone']) ? App\Core\View::e($old['phone']) : ($isEdit ? App\Core\View::e($client['phone']) : '') ?>"
                                required>
                        </div>
                    </div>
                </div>

                <!-- Address Information -->
                <div class="col-12 mt-2">
                    <h5 class="border-bottom pb-2 mb-3">Client Address</h5>
                    <p class="text-muted small mb-3">This address will be used as the default address for this client in shipments.</p>
                </div>

                <div class="col-12">
                    <div class="form-group mb-3">
                        <label for="address" class="form-label fw-bold">Address <span class="text-danger">*</span></label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-map-marker-alt"></i></span>
                            <textarea id="address" name="address" class="form-control" rows="2" required><?= isset($old['address']) ? App\Core\View::e($old['address']) : ($isEdit ? App\Core\View::e($client['address'] ?? '') : '') ?></textarea>
                        </div>
                    </div>
                </div>

                <div class="col-md-4">
                    <div class="form-group mb-3">
                        <label for="city" class="form-label fw-bold">City <span class="text-danger">*</span></label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-city"></i></span>
                            <input type="text" id="city" name="city" class="form-control" required
                                value="<?= isset($old['city']) ? App\Core\View::e($old['city']) : ($isEdit ? App\Core\View::e($client['city'] ?? '') : '') ?>">
                        </div>
                    </div>
                </div>

                <div class="col-md-4">
                    <div class="form-group mb-3">
                        <label for="state" class="form-label fw-bold">State/Province</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-map"></i></span>
                            <input type="text" id="state" name="state" class="form-control"
                                value="<?= isset($old['state']) ? App\Core\View::e($old['state']) : ($isEdit ? App\Core\View::e($client['state'] ?? '') : '') ?>">
                        </div>
                    </div>
                </div>

                <div class="col-md-4">
                    <div class="form-group mb-3">
                        <label for="postal_code" class="form-label fw-bold">Postal Code</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-mail-bulk"></i></span>
                            <input type="text" id="postal_code" name="postal_code" class="form-control"
                                value="<?= isset($old['postal_code']) ? App\Core\View::e($old['postal_code']) : ($isEdit ? App\Core\View::e($client['postal_code'] ?? '') : '') ?>">
                        </div>
                    </div>
                </div>

                <div class="col-md-6">
                    <div class="form-group mb-3">
                        <label for="country" class="form-label fw-bold">Country <span class="text-danger">*</span></label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-globe"></i></span>
                            <select id="country" name="country" class="form-select" required>
                                <option value="">-- Select Country --</option>
                                <?php
                                $selectedCountry = isset($old['country']) ? $old['country'] : ($isEdit ? ($client['country'] ?? '') : '');
                                if (isset($countries) && !empty($countries)):
                                    foreach ($countries as $country):
                                        // Extract name from array if it's an array
                                        $countryName = is_array($country) ? $country['name'] : $country;
                                        $selected = $selectedCountry === $countryName ? 'selected' : '';
                                ?>
                                    <option value="<?= App\Core\View::e($countryName) ?>" <?= $selected ?>>
                                        <?= App\Core\View::e($countryName) ?>
                                    </option>
                                <?php endforeach; endif; ?>
                            </select>
                        </div>
                    </div>
                </div>

                <div class="col-md-6">
                    <div class="form-group mb-3">
                        <label for="notes" class="form-label fw-bold">Notes</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-sticky-note"></i></span>
                            <textarea id="notes" name="notes" class="form-control" rows="2"><?= isset($old['notes']) ? App\Core\View::e($old['notes']) : ($isEdit ? App\Core\View::e($client['notes'] ?? '') : '') ?></textarea>
                        </div>
                    </div>
                </div>
            </div>

            <div class="mt-4 d-flex justify-content-end gap-2">
                <a href="<?= App\Core\View::url('/admin/clients') ?>" class="btn btn-outline-secondary">
                    <i class="fas fa-times me-1"></i> Cancel
                </a>
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save me-1"></i> <?= $isEdit ? 'Update Client' : 'Save Client' ?>
                </button>
            </div>
        </form>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('clientForm');

    // Add input validation
    form.addEventListener('submit', function(e) {
        try {
            // Validate form fields
            const name = document.getElementById('name').value;
            const email = document.getElementById('email').value;
            const phone = document.getElementById('phone').value;
            const address = document.getElementById('address').value;
            const city = document.getElementById('city').value;
            const country = document.getElementById('country').value;

            if (!name || !email || !phone || !address || !city || !country) {
                e.preventDefault();

                // Show validation error
                alert('Please fill in all required fields (Name, Email, Phone, Address, City, Country)');
                return;
            }
        } catch (error) {
            console.error('Form submission error:', error);
        }
    });
});
</script>
