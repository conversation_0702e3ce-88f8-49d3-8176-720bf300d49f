<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <p class="text-muted">Manage your clients</p>
    </div>
    <a href="<?= App\Core\View::url('/admin/clients/create') ?>" class="btn btn-primary">
        <i class="fas fa-plus me-1"></i> Add New Client
    </a>
</div>

<?php if (isset($flash_success)): ?>
    <div class="alert alert-success d-flex align-items-center mb-4" role="alert">
        <i class="fas fa-check-circle me-2"></i>
        <div>
            <?= App\Core\View::e($flash_success) ?>
        </div>
    </div>
<?php endif; ?>

<?php if (isset($flash_error)): ?>
    <div class="alert alert-danger d-flex align-items-center mb-4" role="alert">
        <i class="fas fa-exclamation-circle me-2"></i>
        <div>
            <?= App\Core\View::e($flash_error) ?>
        </div>
    </div>
<?php endif; ?>

<div class="card shadow-sm">
    <div class="card-header d-flex justify-content-between align-items-center bg-white py-3">
        <span class="fw-bold">Client List</span>
        <div class="search-container" style="width: 300px;">
            <div class="input-group">
                <span class="input-group-text bg-light border-end-0">
                    <i class="fas fa-search text-muted"></i>
                </span>
                <input type="text" id="client-search" class="form-control border-start-0" placeholder="Search clients...">
            </div>
        </div>
    </div>
    <div class="card-body p-0">
        <div class="table-responsive">
            <table class="table table-hover mb-0">
                <thead class="bg-light">
                    <tr>
                        <th class="ps-3" width="5%">ID</th>
                        <th width="15%">NAME</th>
                        <th width="15%">EMAIL</th>
                        <th width="10%">PHONE</th>
                        <th width="35%">ADDRESS</th>
                        <th class="text-center" width="20%">ACTIONS</th>
                    </tr>
                </thead>
                <tbody>
                    <?php if (empty($clients)): ?>
                        <tr>
                            <td colspan="6" class="text-center py-4">No clients found.</td>
                        </tr>
                    <?php else: ?>
                        <?php foreach ($clients as $client): ?>
                            <tr>
                                <td class="ps-3"><?= $client['id'] ?></td>
                                <td class="fw-medium"><?= App\Core\View::e($client['name']) ?></td>
                                <td><?= App\Core\View::e($client['email']) ?></td>
                                <td><?= App\Core\View::e($client['phone']) ?></td>
                                <td>
                                    <?php
                                    $addressParts = [];
                                    if (!empty($client['address'])) $addressParts[] = $client['address'];
                                    if (!empty($client['city'])) $addressParts[] = $client['city'];
                                    if (!empty($client['state'])) $addressParts[] = $client['state'];
                                    if (!empty($client['country'])) $addressParts[] = $client['country'];
                                    echo !empty($addressParts) ? App\Core\View::e(implode(', ', $addressParts)) : '<span class="text-muted">No address</span>';
                                    ?>
                                </td>
                                <td class="text-center">
                                    <table style="margin: 0 auto;">
                                        <tr>
                                            <td style="padding: 0 5px 0 0; border: none;">
                                                <a href="<?= App\Core\View::url('/admin/clients/edit/' . $client['id']) ?>" style="color: #0d6efd; text-decoration: none;">
                                                    <i class="fas fa-edit"></i> Edit
                                                </a>
                                            </td>
                                            <td style="padding: 0 5px; border: none; color: #6c757d;">|</td>
                                            <td style="padding: 0 0 0 5px; border: none;">
                                                <form action="<?= App\Core\View::url('/admin/clients/delete/' . $client['id']) ?>" method="POST" style="display: inline; margin: 0;">
                                                    <?= App\Core\View::csrfField() ?>
                                                    <button type="submit" style="color: #dc3545; background: none; border: none; padding: 0; font: inherit; cursor: pointer; outline: inherit;" onclick="return confirm('Are you sure you want to delete this client?')">
                                                        <i class="fas fa-trash"></i> Delete
                                                    </button>
                                                </form>
                                            </td>
                                        </tr>
                                    </table>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Client search functionality
    const searchInput = document.getElementById('client-search');
    const tableRows = document.querySelectorAll('tbody tr');

    searchInput.addEventListener('keyup', function() {
        const searchTerm = this.value.toLowerCase();

        tableRows.forEach(row => {
            const text = row.textContent.toLowerCase();
            if (text.includes(searchTerm)) {
                row.style.display = '';
            } else {
                row.style.display = 'none';
            }
        });
    });

    // Add hover effect to table rows
    tableRows.forEach(row => {
        row.addEventListener('mouseover', function() {
            this.classList.add('bg-light');
        });

        row.addEventListener('mouseout', function() {
            this.classList.remove('bg-light');
        });
    });
});
</script>
