<?php
// This view will be rendered within the admin layout (admin.layouts.main)
// The $content variable in the layout will hold the output of this file.
?>

<!-- Include the modern dashboard CSS -->
<link rel="stylesheet" href="<?= App\Core\View::asset('css/dashboard-modern.css') ?>">

<div class="dashboard-container">
    <!-- Welcome Card -->
    <div class="welcome-card">
        <h2>Welcome to the Administration Dashboard</h2>
        <p>From here you can manage shipments, update settings, and more. Use the navigation menu on the left to access different sections.</p>
    </div>

    <!-- Stats Cards -->
    <div class="stats-grid">
        <!-- Total Shipments -->
        <div class="stat-card primary">
            <div class="stat-title">
                Total Shipments Today
                <?php if (isset($monthlyShipments) && count($monthlyShipments) > 1):
                    $lastMonth = array_key_last($monthlyShipments);
                    $previousMonth = array_keys($monthlyShipments)[count($monthlyShipments) - 2];
                    $change = $monthlyShipments[$lastMonth] - $monthlyShipments[$previousMonth];
                    $changePercent = $monthlyShipments[$previousMonth] > 0 ? round(($change / $monthlyShipments[$previousMonth]) * 100, 1) : 0;
                ?>
                <span class="stat-percent <?= $change >= 0 ? 'up' : 'down' ?>">
                    <?= $change >= 0 ? '↑' : '↓' ?> <?= $changePercent ?>%
                </span>
                <?php endif; ?>
            </div>
            <div class="stat-value"><?= App\Core\View::e(number_format($totalShipments, 2)) ?></div>
            <svg class="stat-graph up" viewBox="0 0 100 40" preserveAspectRatio="none">
                <path d="M0,40 L10,35 L20,38 L30,32 L40,36 L50,30 L60,28 L70,22 L80,20 L90,15 L100,10" fill="none" stroke="currentColor" stroke-width="2"></path>
            </svg>
        </div>

        <!-- Shipments In Progress -->
        <div class="stat-card info">
            <div class="stat-title">
                Shipments In Progress
                <span class="stat-percent up">↑ 18.0%</span>
            </div>
            <div class="stat-value"><?= App\Core\View::e(number_format($inTransitShipments, 2)) ?></div>
            <svg class="stat-graph up" viewBox="0 0 100 40" preserveAspectRatio="none">
                <path d="M0,30 L10,32 L20,28 L30,30 L40,25 L50,28 L60,20 L70,22 L80,18 L90,15 L100,10" fill="none" stroke="currentColor" stroke-width="2"></path>
            </svg>
        </div>

        <!-- Delivered Shipments -->
        <div class="stat-card success">
            <div class="stat-title">
                Delivered Shipments
                <span class="stat-percent down">↓ 28.0%</span>
            </div>
            <div class="stat-value"><?= App\Core\View::e(number_format($deliveredToday, 2)) ?></div>
            <svg class="stat-graph down" viewBox="0 0 100 40" preserveAspectRatio="none">
                <path d="M0,10 L10,15 L20,12 L30,18 L40,15 L50,20 L60,18 L70,25 L80,22 L90,28 L100,25" fill="none" stroke="currentColor" stroke-width="2"></path>
            </svg>
        </div>

        <!-- Failed Shipments -->
        <div class="stat-card warning">
            <div class="stat-title">
                Failed Shipments
                <span class="stat-percent down">↓ 15.0%</span>
            </div>
            <div class="stat-value"><?= App\Core\View::e(number_format($pendingShipments, 2)) ?></div>
            <svg class="stat-graph down" viewBox="0 0 100 40" preserveAspectRatio="none">
                <path d="M0,20 L10,22 L20,18 L30,25 L40,22 L50,28 L60,25 L70,30 L80,28 L90,32 L100,30" fill="none" stroke="currentColor" stroke-width="2"></path>
            </svg>
        </div>
    </div>

    <!-- Charts Section -->
    <div class="charts-grid">
        <!-- Shipment Status Chart -->
        <div class="chart-card">
            <div class="chart-header">
                <div class="chart-title">Shipment Status Distribution</div>
                <div class="chart-badge">Live Data</div>
            </div>
            <div class="chart-body">
                <canvas id="shipment-status-chart"></canvas>
            </div>
        </div>

        <!-- Monthly Shipments Chart -->
        <div class="chart-card">
            <div class="chart-header">
                <div class="chart-title">Monthly Shipments</div>
                <div class="chart-badge">Last 6 Months</div>
            </div>
            <div class="chart-body">
                <canvas id="monthly-shipments-chart"></canvas>
            </div>
        </div>
    </div>

    <!-- Map and Delivery Progress -->
    <div class="map-delivery-grid">
        <!-- Delivery In Progress Map -->
        <div class="map-card">
            <div class="map-header">
                <div class="map-title">Delivery In Progress</div>
            </div>
            <div class="map-container">
                <!-- We'll create the map directly with canvas instead of using an image -->
                <canvas id="delivery-map" width="600" height="300" style="width:100%; height:100%;"></canvas>
                <div class="map-marker" style="top:50%; left:70%;">
                    <i class="fas fa-truck"></i>
                </div>
                <div class="map-marker" style="top:30%; left:40%;">
                    <i class="fas fa-truck"></i>
                </div>
                <div class="map-marker" style="top:65%; left:25%;">
                    <i class="fas fa-truck"></i>
                </div>
            </div>
        </div>

        <!-- Delivery Progress Timeline -->
        <div class="delivery-progress-card">
            <div class="progress-header">
                <div class="progress-title">Recent Delivery Updates</div>
            </div>
            <div class="progress-body">
                <div class="delivery-steps">
                    <?php
                    // Get all shipments with different statuses for the timeline
                    $timelineShipments = [];
                    $statusTypes = ['delayed', 'pending', 'in_transit', 'out_for_delivery', 'delivered'];
                    $statusIcons = [
                        'delayed' => 'fa-exclamation-triangle',
                        'pending' => 'fa-clock',
                        'in_transit' => 'fa-truck',
                        'out_for_delivery' => 'fa-shipping-fast',
                        'delivered' => 'fa-check-circle'
                    ];

                    // Find one shipment of each status type for the timeline
                    foreach ($statusTypes as $type) {
                        foreach ($recentShipments as $shipment) {
                            $status = strtolower($shipment['status']);
                            if (strpos($status, $type) !== false) {
                                $timelineShipments[$type] = $shipment;
                                break;
                            }
                        }
                    }

                    if (!empty($timelineShipments)):
                        // Sample times for demonstration
                        $times = [
                            'delayed' => '9:15 AM',
                            'pending' => '10:30 AM',
                            'in_transit' => '1:45 PM',
                            'out_for_delivery' => '3:20 PM',
                            'delivered' => '5:10 PM'
                        ];

                        // Sample locations for demonstration
                        $locations = [
                            'delayed' => 'Processing Center - Delay Reported',
                            'pending' => 'Origin Warehouse - Awaiting Pickup',
                            'in_transit' => 'Regional Distribution Center',
                            'out_for_delivery' => 'Local Delivery Vehicle',
                            'delivered' => 'Recipient Address'
                        ];

                        foreach ($statusTypes as $index => $type):
                            if (isset($timelineShipments[$type])):
                                $shipment = $timelineShipments[$type];
                                $isLast = ($index === count($statusTypes) - 1);
                    ?>
                    <div class="delivery-step <?= $isLast ? '' : 'step-line' ?>">
                        <div class="step-icon <?= ($type === 'delivered') ? 'completed' : (($type === 'in_transit' || $type === 'out_for_delivery') ? 'active' : '') ?>">
                            <i class="fas <?= $statusIcons[$type] ?? 'fa-circle' ?>"></i>
                        </div>
                        <div class="step-content">
                            <div class="step-title">
                                <?= ucfirst(str_replace('_', ' ', $type)) ?>
                                <span class="step-tracking"><?= App\Core\View::e($shipment['tracking_number']) ?></span>
                            </div>
                            <div class="step-location"><?= $locations[$type] ?? 'Unknown Location' ?></div>
                            <div class="step-time"><?= $times[$type] ?? date('h:i A', strtotime($shipment['updated_at'])) ?></div>
                        </div>
                    </div>
                    <?php
                            endif;
                        endforeach;
                    else:
                    ?>
                    <div class="timeline-empty">
                        <i class="fas fa-truck"></i>
                        <p>No delivery updates available</p>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Shipments Table -->
    <div class="table-card">
        <div class="table-header">
            <div class="table-title">Recent Shipments</div>
            <a href="<?= App\Core\View::url('/admin/shipments') ?>" class="table-action">
                <i class="fas fa-list"></i> View All
            </a>
        </div>
        <div class="table-container">
            <?php if (empty($recentShipments)): ?>
                <div class="empty-state">
                    <i class="fas fa-box-open"></i>
                    <p>No shipments found.</p>
                </div>
            <?php else: ?>
                <table class="modern-table">
                    <thead>
                        <tr>
                            <th>Tracking #</th>
                            <th>Origin</th>
                            <th>Destination</th>
                            <th>Status</th>
                            <th>Last Update</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($recentShipments as $shipment): ?>
                            <tr>
                                <td><?= App\Core\View::e($shipment['tracking_number']) ?></td>
                                <td><?= App\Core\View::e(isset($shipment['origin_city']) ? $shipment['origin_city'] . ', ' . $shipment['origin_country'] : $shipment['origin']) ?></td>
                                <td><?= App\Core\View::e(isset($shipment['destination_city']) ? $shipment['destination_city'] . ', ' . $shipment['destination_country'] : $shipment['destination']) ?></td>
                                <td>
                                    <?php
                                    $status = strtolower($shipment['status']);
                                    $statusClass = '';

                                    if (strpos($status, 'pending') !== false) {
                                        $statusClass = 'pending';
                                    } elseif (strpos($status, 'transit') !== false) {
                                        $statusClass = 'in-transit';
                                    } elseif (strpos($status, 'delivered') !== false) {
                                        $statusClass = 'delivered';
                                    } elseif (strpos($status, 'delayed') !== false || strpos($status, 'failed') !== false) {
                                        $statusClass = 'delayed';
                                    } else {
                                        $statusClass = 'cancelled';
                                    }
                                    ?>
                                    <span class="status-badge <?= $statusClass ?>">
                                        <?= App\Core\View::e(ucfirst(str_replace('_', ' ', $shipment['status']))) ?>
                                    </span>
                                </td>
                                <td><?= App\Core\View::e(date('M d, Y H:i', strtotime($shipment['updated_at']))) ?></td>
                                <td>
                                    <div class="action-buttons">
                                        <a href="<?= App\Core\View::url('/admin/shipments/viewShipment/' . $shipment['id']) ?>" class="action-button" title="View Details">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="<?= App\Core\View::url('/admin/shipments/edit/' . $shipment['id']) ?>" class="action-button" title="Edit Shipment">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            <?php endif; ?>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="actions-card">
        <div class="actions-header">
            <div class="actions-title">Quick Actions</div>
            <div class="actions-badge">Shortcuts</div>
        </div>
        <div class="actions-body">
            <a href="<?= App\Core\View::url('/admin/shipments/create') ?>" class="action-button">
                <i class="fas fa-plus"></i> New Shipment
            </a>
            <a href="<?= App\Core\View::url('/admin/settings/general') ?>" class="action-button">
                <i class="fas fa-cog"></i> Settings
            </a>
            <a href="<?= App\Core\View::url('/admin/users') ?>" class="action-button">
                <i class="fas fa-users"></i> Manage Users
            </a>
            <a href="<?= App\Core\View::url('/track') ?>" target="_blank" class="action-button">
                <i class="fas fa-search"></i> Track Shipment
            </a>
        </div>
    </div>
</div>

<!-- Chart.js Configuration -->
<script>
    document.addEventListener('DOMContentLoaded', function() {
        if (typeof Chart !== 'undefined') {
            // Monochromatic color scheme
            const greyColors = [
                '#333333', // Dark grey
                '#555555',
                '#777777',
                '#999999',
                '#BBBBBB'  // Light grey
            ];

            // Shipment Status Chart
            const statusChartEl = document.getElementById('shipment-status-chart');
            if (statusChartEl) {
                const statusChart = new Chart(statusChartEl, {
                    type: 'doughnut',
                    data: {
                        labels: ['Pending', 'In Transit', 'Delivered', 'Delayed', 'Cancelled'],
                        datasets: [{
                            data: [
                                <?= $statusCounts['pending'] ?? 0 ?>,
                                <?= $statusCounts['in_transit'] ?? 0 ?>,
                                <?= $statusCounts['delivered'] ?? 0 ?>,
                                <?= $statusCounts['delayed'] ?? 0 ?>,
                                <?= $statusCounts['cancelled'] ?? 0 ?>
                            ],
                            backgroundColor: greyColors,
                            borderWidth: 1,
                            borderColor: '#FFFFFF'
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                position: 'bottom',
                                labels: {
                                    font: {
                                        size: 12,
                                        family: "'Inter', sans-serif"
                                    },
                                    color: '#4A5568'
                                }
                            },
                            tooltip: {
                                backgroundColor: '#333333',
                                titleFont: {
                                    size: 14,
                                    family: "'Inter', sans-serif"
                                },
                                bodyFont: {
                                    size: 13,
                                    family: "'Inter', sans-serif"
                                },
                                padding: 12,
                                cornerRadius: 4,
                                displayColors: true
                            }
                        },
                        cutout: '65%',
                        animation: {
                            animateScale: true,
                            animateRotate: true
                        }
                    }
                });
            }

            // Monthly Shipments Chart
            const monthlyChartEl = document.getElementById('monthly-shipments-chart');
            if (monthlyChartEl) {
                const monthlyChart = new Chart(monthlyChartEl, {
                    type: 'bar',
                    data: {
                        labels: [
                            <?php
                            if (isset($monthlyShipments) && is_array($monthlyShipments)) {
                                foreach ($monthlyShipments as $month => $count) {
                                    echo "'$month', ";
                                }
                            }
                            ?>
                        ],
                        datasets: [{
                            label: 'Shipments',
                            data: [
                                <?php
                                if (isset($monthlyShipments) && is_array($monthlyShipments)) {
                                    foreach ($monthlyShipments as $month => $count) {
                                        echo "$count, ";
                                    }
                                }
                                ?>
                            ],
                            backgroundColor: '#333333',
                            borderWidth: 0,
                            borderRadius: 4,
                            barThickness: 20,
                            maxBarThickness: 30
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        scales: {
                            y: {
                                beginAtZero: true,
                                ticks: {
                                    precision: 0,
                                    font: {
                                        size: 12,
                                        family: "'Inter', sans-serif"
                                    },
                                    color: '#718096'
                                },
                                grid: {
                                    color: '#E2E8F0',
                                    drawBorder: false
                                }
                            },
                            x: {
                                ticks: {
                                    font: {
                                        size: 12,
                                        family: "'Inter', sans-serif"
                                    },
                                    color: '#718096'
                                },
                                grid: {
                                    display: false
                                }
                            }
                        },
                        plugins: {
                            legend: {
                                display: false
                            },
                            tooltip: {
                                backgroundColor: '#333333',
                                titleFont: {
                                    size: 14,
                                    family: "'Inter', sans-serif"
                                },
                                bodyFont: {
                                    size: 13,
                                    family: "'Inter', sans-serif"
                                },
                                padding: 12,
                                cornerRadius: 4,
                                displayColors: false
                            }
                        },
                        animation: {
                            duration: 1000,
                            easing: 'easeOutQuart'
                        }
                    }
                });
            }
        }

        // Draw the delivery map directly on canvas using real shipment data
        const drawDeliveryMap = function() {
            const canvas = document.getElementById('delivery-map');
            if (canvas) {
                const ctx = canvas.getContext('2d');

                // Draw map background
                ctx.fillStyle = '#F7FAFC';
                ctx.fillRect(0, 0, canvas.width, canvas.height);

                // Draw some map features
                ctx.strokeStyle = '#E2E8F0';
                ctx.lineWidth = 1;

                // Grid lines
                for (let i = 0; i < 10; i++) {
                    ctx.beginPath();
                    ctx.moveTo(0, i * 30);
                    ctx.lineTo(canvas.width, i * 30);
                    ctx.stroke();

                    ctx.beginPath();
                    ctx.moveTo(i * 60, 0);
                    ctx.lineTo(i * 60, canvas.height);
                    ctx.stroke();
                }

                // Get shipment data from the page
                const shipmentData = [];
                const deliverySteps = document.querySelectorAll('.delivery-step');

                deliverySteps.forEach(step => {
                    const statusEl = step.querySelector('.step-title');
                    const trackingEl = step.querySelector('.step-tracking');
                    const locationEl = step.querySelector('.step-location');

                    if (statusEl && locationEl) {
                        const status = statusEl.textContent.trim().split('\n')[0];
                        const location = locationEl.textContent.trim();

                        // Only add in-transit shipments to the map
                        if (status.includes('Transit') || status.includes('Delivery')) {
                            // Generate random coordinates for demonstration
                            // In a real app, these would come from geocoding the addresses
                            const x = 100 + Math.floor(Math.random() * 400);
                            const y = 50 + Math.floor(Math.random() * 200);

                            shipmentData.push({
                                status,
                                location,
                                tracking: trackingEl ? trackingEl.textContent.trim() : '',
                                x,
                                y,
                                isOrigin: false
                            });

                            // Add origin point for this shipment
                            const originX = x - 150 - Math.floor(Math.random() * 100);
                            const originY = y - 30 + Math.floor(Math.random() * 60);

                            shipmentData.push({
                                status: 'Origin',
                                location: location.includes('Regional') ? 'Origin Warehouse' : 'Sender Location',
                                tracking: trackingEl ? trackingEl.textContent.trim() : '',
                                x: originX,
                                y: originY,
                                isOrigin: true,
                                destX: x,
                                destY: y
                            });
                        }
                    }
                });

                // If no shipment data found, try to get origin/destination from the table
                if (shipmentData.length === 0) {
                    // Get shipment data from the recent shipments table
                    const tableRows = document.querySelectorAll('.modern-table tbody tr');
                    const tableShipments = [];

                    tableRows.forEach(row => {
                        const cells = row.querySelectorAll('td');
                        if (cells.length >= 3) {
                            const tracking = cells[0].textContent.trim();
                            const origin = cells[1].textContent.trim();
                            const destination = cells[2].textContent.trim();
                            const status = cells[3].querySelector('.status-badge') ?
                                cells[3].querySelector('.status-badge').textContent.trim() : '';

                            if (status.toLowerCase().includes('transit') || status.toLowerCase().includes('delivery')) {
                                tableShipments.push({ tracking, origin, destination, status });
                            }
                        }
                    });

                    // Use up to 3 shipments from the table
                    const limit = Math.min(tableShipments.length, 3);
                    for (let i = 0; i < limit; i++) {
                        const shipment = tableShipments[i];
                        const originX = 100 + (i * 50);
                        const originY = 100 + (i * 40);
                        const destX = 400 - (i * 30);
                        const destY = 150 + (i * 20);

                        // Add origin point
                        shipmentData.push({
                            status: 'Origin',
                            location: shipment.origin,
                            tracking: shipment.tracking,
                            x: originX,
                            y: originY,
                            isOrigin: true,
                            destX: destX,
                            destY: destY
                        });

                        // Add destination point
                        shipmentData.push({
                            status: shipment.status,
                            location: shipment.destination,
                            tracking: shipment.tracking,
                            x: destX,
                            y: destY,
                            isOrigin: false
                        });
                    }

                    // If still no data, use real data from the database
                    // The PHP code below will be executed server-side and inject the actual shipment data
                    if (shipmentData.length === 0) {
                        // Use actual shipment data from the database
                        const dbShipments = [
                            <?php
                            // Get shipments with in_transit or out_for_delivery status
                            $activeShipments = [];
                            foreach ($recentShipments as $shipment) {
                                $status = strtolower($shipment['status']);
                                if (strpos($status, 'transit') !== false || strpos($status, 'delivery') !== false) {
                                    $activeShipments[] = $shipment;
                                }
                            }

                            // If we have active shipments, use them
                            if (!empty($activeShipments)) {
                                foreach ($activeShipments as $index => $shipment) {
                                    $origin = json_encode($shipment['origin']);
                                    $destination = json_encode($shipment['destination']);
                                    $tracking = json_encode($shipment['tracking_number']);
                                    $status = json_encode($shipment['status']);

                                    // Calculate positions based on index to ensure they're spread out
                                    $originX = 100 + ($index * 50);
                                    $originY = 100 + ($index * 40);
                                    $destX = 400 - ($index * 30);
                                    $destY = 150 + ($index * 20);

                                    echo "{
                                        tracking: $tracking,
                                        origin: $origin,
                                        destination: $destination,
                                        status: $status,
                                        originX: $originX,
                                        originY: $originY,
                                        destX: $destX,
                                        destY: $destY
                                    },";
                                }
                            }
                            ?>
                        ];

                        // Process the database shipments
                        dbShipments.forEach(shipment => {
                            if (shipment.origin && shipment.destination) {
                                // Add origin point
                                shipmentData.push({
                                    status: 'Origin',
                                    location: shipment.origin,
                                    tracking: shipment.tracking,
                                    x: shipment.originX,
                                    y: shipment.originY,
                                    isOrigin: true,
                                    destX: shipment.destX,
                                    destY: shipment.destY
                                });

                                // Add destination point
                                shipmentData.push({
                                    status: shipment.status,
                                    location: shipment.destination,
                                    tracking: shipment.tracking,
                                    x: shipment.destX,
                                    y: shipment.destY,
                                    isOrigin: false
                                });
                            }
                        });

                        // If still no data after trying database, use some defaults
                        if (shipmentData.length === 0) {
                            shipmentData.push(
                                {x: 100, y: 150, location: 'New York', isOrigin: true, destX: 300, destY: 180},
                                {x: 300, y: 180, location: 'Los Angeles', isOrigin: false},
                                {x: 150, y: 80, location: 'Chicago', isOrigin: true, destX: 420, destY: 70},
                                {x: 420, y: 70, location: 'Miami', isOrigin: false},
                                {x: 50, y: 200, location: 'Boston', isOrigin: true, destX: 300, destY: 230},
                                {x: 300, y: 230, location: 'Seattle', isOrigin: false}
                            );
                        }
                    }
                }

                // Draw map background elements
                // Draw some country/continent shapes for visual interest
                const drawMapBackground = () => {
                    // Draw a few subtle continent/country shapes
                    const continents = [
                        { // North America
                            path: [
                                {x: 50, y: 80}, {x: 150, y: 100}, {x: 180, y: 150},
                                {x: 120, y: 200}, {x: 60, y: 180}, {x: 40, y: 120}
                            ],
                            color: 'rgba(226, 232, 240, 0.3)'
                        },
                        { // Europe
                            path: [
                                {x: 300, y: 70}, {x: 350, y: 60}, {x: 380, y: 90},
                                {x: 370, y: 130}, {x: 330, y: 140}, {x: 290, y: 110}
                            ],
                            color: 'rgba(226, 232, 240, 0.3)'
                        },
                        { // Africa
                            path: [
                                {x: 320, y: 160}, {x: 370, y: 170}, {x: 380, y: 220},
                                {x: 350, y: 260}, {x: 310, y: 250}, {x: 300, y: 200}
                            ],
                            color: 'rgba(226, 232, 240, 0.3)'
                        },
                        { // Asia
                            path: [
                                {x: 400, y: 100}, {x: 500, y: 80}, {x: 550, y: 130},
                                {x: 520, y: 200}, {x: 450, y: 220}, {x: 390, y: 150}
                            ],
                            color: 'rgba(226, 232, 240, 0.3)'
                        }
                    ];

                    // Draw the continents
                    continents.forEach(continent => {
                        ctx.beginPath();
                        ctx.moveTo(continent.path[0].x, continent.path[0].y);

                        for (let i = 1; i < continent.path.length; i++) {
                            ctx.lineTo(continent.path[i].x, continent.path[i].y);
                        }

                        ctx.closePath();
                        ctx.fillStyle = continent.color;
                        ctx.fill();
                        ctx.strokeStyle = 'rgba(203, 213, 224, 0.4)';
                        ctx.lineWidth = 1;
                        ctx.stroke();
                    });

                    // Draw some ocean names
                    ctx.fillStyle = 'rgba(160, 174, 192, 0.2)';
                    ctx.font = 'italic 14px Arial';
                    ctx.fillText('Atlantic Ocean', 200, 180);
                    ctx.fillText('Pacific Ocean', 450, 180);
                };

                // Draw the background first
                drawMapBackground();

                // Draw city dots and routes
                shipmentData.forEach(point => {
                    // Draw the dot
                    ctx.beginPath();
                    ctx.arc(point.x, point.y, 4, 0, Math.PI * 2);
                    ctx.fillStyle = point.isOrigin ? '#718096' : '#4299E1';
                    ctx.fill();

                    // Add a subtle glow effect
                    ctx.beginPath();
                    ctx.arc(point.x, point.y, 6, 0, Math.PI * 2);
                    ctx.fillStyle = point.isOrigin ? 'rgba(113, 128, 150, 0.2)' : 'rgba(66, 153, 225, 0.2)';
                    ctx.fill();

                    // Location names with background for better readability
                    const displayName = point.location.length > 20 ?
                        point.location.substring(0, 17) + '...' :
                        point.location;

                    // Text background
                    const textWidth = ctx.measureText(displayName).width;
                    ctx.fillStyle = 'rgba(255, 255, 255, 0.7)';
                    ctx.fillRect(point.x - textWidth/2 - 4, point.y - 20, textWidth + 8, 16);

                    // Text
                    ctx.fillStyle = '#4A5568';
                    ctx.font = 'bold 10px Arial';
                    ctx.textAlign = 'center';
                    ctx.fillText(displayName, point.x, point.y - 8);
                    ctx.textAlign = 'left'; // Reset text alignment

                    // Draw route if this is an origin point
                    if (point.isOrigin && point.destX && point.destY) {
                        // Calculate distance between points
                        const dx = point.destX - point.x;
                        const dy = point.destY - point.y;
                        const distance = Math.sqrt(dx * dx + dy * dy);

                        // Adjust curve height based on distance
                        const curveHeight = Math.min(distance * 0.3, 80);

                        // Calculate control points for a more natural curve
                        const midX = (point.x + point.destX) / 2;
                        const midY = (point.y + point.destY) / 2 - curveHeight;

                        // Draw a shadow/glow effect for the route
                        ctx.strokeStyle = 'rgba(66, 153, 225, 0.2)';
                        ctx.lineWidth = 4;
                        ctx.beginPath();
                        ctx.moveTo(point.x, point.y);
                        ctx.quadraticCurveTo(midX, midY, point.destX, point.destY);
                        ctx.stroke();

                        // Draw the main route line
                        ctx.strokeStyle = '#4299E1';
                        ctx.lineWidth = 2;
                        ctx.beginPath();
                        ctx.moveTo(point.x, point.y);
                        ctx.quadraticCurveTo(midX, midY, point.destX, point.destY);
                        ctx.stroke();

                        // Add direction arrows along the path
                        const arrowCount = Math.floor(distance / 100) + 1; // One arrow per 100px

                        for (let i = 1; i <= arrowCount; i++) {
                            const t = i / (arrowCount + 1); // Position along the curve (0-1)

                            // Calculate point along the quadratic curve
                            const arrowX = Math.pow(1-t, 2) * point.x +
                                          2 * (1-t) * t * midX +
                                          Math.pow(t, 2) * point.destX;
                            const arrowY = Math.pow(1-t, 2) * point.y +
                                          2 * (1-t) * t * midY +
                                          Math.pow(t, 2) * point.destY;

                            // Calculate tangent to get direction
                            const t1 = t - 0.01;
                            const t2 = t + 0.01;
                            const x1 = Math.pow(1-t1, 2) * point.x + 2 * (1-t1) * t1 * midX + Math.pow(t1, 2) * point.destX;
                            const y1 = Math.pow(1-t1, 2) * point.y + 2 * (1-t1) * t1 * midY + Math.pow(t1, 2) * point.destY;
                            const x2 = Math.pow(1-t2, 2) * point.x + 2 * (1-t2) * t2 * midX + Math.pow(t2, 2) * point.destX;
                            const y2 = Math.pow(1-t2, 2) * point.y + 2 * (1-t2) * t2 * midY + Math.pow(t2, 2) * point.destY;

                            // Arrow angle
                            const angle = Math.atan2(y2 - y1, x2 - x1);

                            // Draw arrow
                            ctx.save();
                            ctx.translate(arrowX, arrowY);
                            ctx.rotate(angle);

                            ctx.beginPath();
                            ctx.moveTo(0, 0);
                            ctx.lineTo(-6, -3);
                            ctx.lineTo(-6, 3);
                            ctx.closePath();

                            ctx.fillStyle = '#4299E1';
                            ctx.fill();

                            ctx.restore();
                        }
                    }
                });

                // Update truck markers to position them along the routes
                const markers = document.querySelectorAll('.map-marker');
                const routes = [];

                // Find all routes (origin-destination pairs)
                shipmentData.forEach(point => {
                    if (point.isOrigin && point.destX && point.destY) {
                        routes.push({
                            originX: point.x,
                            originY: point.y,
                            destX: point.destX,
                            destY: point.destY,
                            location: point.location,
                            destination: shipmentData.find(p => p.x === point.destX && p.y === point.destY)?.location || ''
                        });
                    }
                });

                // Position markers along the routes
                markers.forEach((marker, index) => {
                    if (index < routes.length) {
                        const route = routes[index];

                        // Position the marker somewhere along the route (30-70% of the way)
                        const progress = 0.3 + (index * 0.2); // 30%, 50%, 70% for different trucks

                        // Calculate distance between points
                        const dx = route.destX - route.originX;
                        const dy = route.destY - route.originY;
                        const distance = Math.sqrt(dx * dx + dy * dy);

                        // Adjust curve height based on distance
                        const curveHeight = Math.min(distance * 0.3, 80);

                        // Calculate control points for a more natural curve
                        const midX = (route.originX + route.destX) / 2;
                        const midY = (route.originY + route.destY) / 2 - curveHeight;

                        // Calculate position along the quadratic bezier curve
                        const t = progress;
                        const x = Math.pow(1-t, 2) * route.originX +
                                  2 * (1-t) * t * midX +
                                  Math.pow(t, 2) * route.destX;
                        const y = Math.pow(1-t, 2) * route.originY +
                                  2 * (1-t) * t * midY +
                                  Math.pow(t, 2) * route.destY;

                        // Convert to percentage for CSS positioning
                        const percentX = (x / canvas.width) * 100;
                        const percentY = (y / canvas.height) * 100;

                        marker.style.top = percentY + '%';
                        marker.style.left = percentX + '%';
                        marker.style.display = 'flex';

                        // Add truck icon if it doesn't exist
                        if (!marker.querySelector('i')) {
                            const icon = document.createElement('i');
                            icon.className = 'fas fa-truck';
                            marker.appendChild(icon);
                        }

                        // Calculate tangent to get direction
                        const t1 = t - 0.05;
                        const t2 = t + 0.05;

                        // Ensure t1 and t2 are within bounds
                        const clampedT1 = Math.max(0, Math.min(1, t1));
                        const clampedT2 = Math.max(0, Math.min(1, t2));

                        // Calculate points along the curve for tangent
                        const x1 = Math.pow(1-clampedT1, 2) * route.originX +
                                   2 * (1-clampedT1) * clampedT1 * midX +
                                   Math.pow(clampedT1, 2) * route.destX;
                        const y1 = Math.pow(1-clampedT1, 2) * route.originY +
                                   2 * (1-clampedT1) * clampedT1 * midY +
                                   Math.pow(clampedT1, 2) * route.destY;

                        const x2 = Math.pow(1-clampedT2, 2) * route.originX +
                                   2 * (1-clampedT2) * clampedT2 * midX +
                                   Math.pow(clampedT2, 2) * route.destX;
                        const y2 = Math.pow(1-clampedT2, 2) * route.originY +
                                   2 * (1-clampedT2) * clampedT2 * midY +
                                   Math.pow(clampedT2, 2) * route.destY;

                        // Calculate angle based on the tangent
                        const angle = Math.atan2(y2 - y1, x2 - x1) * 180 / Math.PI;

                        // Rotate the truck to face the direction of travel
                        marker.style.transform = `translate(-50%, -50%) rotate(${angle}deg)`;

                        // Add a pulsing effect to the active truck
                        marker.style.animation = 'pulse 1.5s infinite';
                    } else {
                        marker.style.display = 'none';
                    }
                });
            }
        };

        // Call the function to draw the delivery map
        drawDeliveryMap();
    });
</script>
