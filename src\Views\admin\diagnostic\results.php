<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="text-2xl font-semibold"><?= $pageTitle ?></h1>
    <a href="<?= App\Core\View::url('/admin/dashboard') ?>" class="btn btn-primary">
        <i class="fas fa-arrow-left"></i> Back to Dashboard
    </a>
</div>

<?php if (isset($flash_error)): ?>
    <div class="alert alert-error">
        <?= App\Core\View::e($flash_error) ?>
    </div>
<?php endif; ?>

<?php if (isset($flash_success)): ?>
    <div class="alert alert-success">
        <?= App\Core\View::e($flash_success) ?>
    </div>
<?php endif; ?>

<div class="card shadow-sm mb-4">
    <div class="card-header d-flex justify-content-between align-items-center">
        <span class="font-medium">Test Results</span>
        <div>
            <a href="<?= App\Core\View::url('/admin/diagnostic/fix/complete') ?>" class="btn btn-sm btn-success me-2">
                <i class="fas fa-magic"></i> Apply Comprehensive Fix
            </a>
            <a href="<?= App\Core\View::url('/admin/diagnostic/run') ?>" class="btn btn-sm btn-primary">
                <i class="fas fa-sync"></i> Run Tests Again
            </a>
        </div>
    </div>
    <div class="card-body">
        <?= $resultsHtml ?>
    </div>
</div>

<div class="row">
    <div class="col-md-6">
        <div class="card shadow-sm mb-4">
            <div class="card-header">
                <span class="font-medium">Quick Fixes</span>
            </div>
            <div class="card-body">
                <ul class="list-group">
                    <?php if (!empty($testResults['errors'])): ?>
                        <?php foreach ($testResults['errors'] as $error): ?>
                            <li class="list-group-item list-group-item-danger">
                                <strong>Error:</strong> <?= App\Core\View::e($error) ?>
                                <?php if (strpos($error, 'namespace') !== false): ?>
                                    <div class="mt-2">
                                        <a href="<?= App\Core\View::url('/admin/diagnostic/fix/namespace') ?>" class="btn btn-sm btn-outline-primary">Fix Namespace</a>
                                    </div>
                                <?php endif; ?>
                                <?php if (strpos($error, 'controller') !== false || strpos($error, 'Controller') !== false): ?>
                                    <div class="mt-2">
                                        <a href="<?= App\Core\View::url('/admin/diagnostic/fix/router') ?>" class="btn btn-sm btn-outline-primary">Fix Router</a>
                                    </div>
                                <?php endif; ?>
                            </li>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <li class="list-group-item list-group-item-success">
                            <i class="fas fa-check-circle"></i> No critical errors found!
                        </li>
                    <?php endif; ?>
                </ul>
            </div>
        </div>
    </div>

    <div class="col-md-6">
        <div class="card shadow-sm mb-4">
            <div class="card-header">
                <span class="font-medium">Specific Tests</span>
            </div>
            <div class="card-body">
                <div class="list-group">
                    <a href="<?= App\Core\View::url('/admin/diagnostic/test/databaseConnection') ?>" class="list-group-item list-group-item-action">
                        <i class="fas fa-database"></i> Test Database Connection
                    </a>
                    <a href="<?= App\Core\View::url('/admin/diagnostic/test/clientsTable') ?>" class="list-group-item list-group-item-action">
                        <i class="fas fa-table"></i> Test Clients Table
                    </a>
                    <a href="<?= App\Core\View::url('/admin/diagnostic/test/clientRoutes') ?>" class="list-group-item list-group-item-action">
                        <i class="fas fa-route"></i> Test Client Routes
                    </a>
                    <a href="<?= App\Core\View::url('/admin/diagnostic/test/clientController') ?>" class="list-group-item list-group-item-action">
                        <i class="fas fa-cogs"></i> Test Client Controller
                    </a>
                    <a href="<?= App\Core\View::url('/admin/diagnostic/test/clientViews') ?>" class="list-group-item list-group-item-action">
                        <i class="fas fa-desktop"></i> Test Client Views
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Add any JavaScript for the diagnostic page here
    console.log('Diagnostic page loaded');
});
</script>
