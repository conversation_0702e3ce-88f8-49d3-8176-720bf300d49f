<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= App\Core\View::e($pageTitle ?? 'Admin Panel') ?> - <?= App\Core\Config::get('app.name', 'ELTA') ?></title>
    <!-- Link to compiled CSS -->
    <link rel="stylesheet" href="<?= App\Core\View::asset('css/admin-tailwind.css') ?>">
    <!-- Form fixes CSS -->
    <link rel="stylesheet" href="<?= App\Core\View::asset('css/form-fixes.css') ?>">
    <!-- Custom CSS for specific fixes -->
    <link rel="stylesheet" href="<?= App\Core\View::asset('css/custom.css') ?>">
    <!-- Standardized Button System -->
    <link rel="stylesheet" href="<?= App\Core\View::asset('css/standardized-buttons.css') ?>">
    <!-- Custom Theme CSS (Generated from appearance settings) -->
    <link rel="stylesheet" href="<?= App\Core\View::asset('css/custom-theme.css') ?>">
    <!-- Google Fonts - Inter -->
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap">
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Optional: Chart.js for dashboard charts -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.js"></script>
</head>
<body>
    <header class="header">
        <div class="d-flex align-items-center">
            <button id="sidebar-toggle" class="btn btn-sm d-md-none mr-3">
                <i class="fas fa-bars"></i>
            </button>
            <h1><?= App\Core\Config::get('app.name', 'ELTA') ?> Admin</h1>
        </div>
        <div class="user-info">
            <span><i class="fas fa-user"></i> <?= App\Core\View::e($userName ?? 'Admin') ?></span>
            <form action="<?= App\Core\View::url('/admin/logout') ?>" method="POST" style="display: inline;">
                <?= App\Core\View::csrfField() ?>
                <button type="submit"><i class="fas fa-sign-out-alt"></i> Logout</button>
            </form>
        </div>
    </header>

    <aside class="sidebar">
        <nav>
            <ul>
                <li><a href="<?= App\Core\View::url('/admin/dashboard') ?>"><i class="fas fa-tachometer-alt"></i> Dashboard</a></li>
                <li>
                    <span class="menu-title"><i class="fas fa-shipping-fast"></i> Shipments</span>
                    <ul>
                        <li><a href="<?= App\Core\View::url('/admin/shipments') ?>"><i class="fas fa-list"></i> All Shipments</a></li>
                        <li><a href="<?= App\Core\View::url('/admin/shipments/create') ?>"><i class="fas fa-plus"></i> Create Shipment</a></li>
                    </ul>
                </li>
                <li><a href="<?= App\Core\View::url('/admin/clients') ?>"><i class="fas fa-users"></i> Clients</a></li>
                <li>
                    <span class="menu-title"><i class="fas fa-cog"></i> Settings</span>
                    <ul>
                        <li><a href="<?= App\Core\View::url('/admin/settings/general') ?>"><i class="fas fa-sliders-h"></i> General</a></li>
                        <li><a href="<?= App\Core\View::url('/admin/settings/packages') ?>"><i class="fas fa-box"></i> Packages</a></li>
                        <li><a href="<?= App\Core\View::url('/admin/settings/emails') ?>"><i class="fas fa-envelope"></i> Emails</a></li>
                        <li><a href="<?= App\Core\View::url('/admin/settings/status') ?>"><i class="fas fa-tags"></i> Status Options</a></li>
                        <li><a href="<?= App\Core\View::url('/admin/settings/shipmentOptions') ?>"><i class="fas fa-shipping-fast"></i> Shipment Options</a></li>
                        <li><a href="<?= App\Core\View::url('/admin/settings/document-types') ?>"><i class="fas fa-file-alt"></i> Document Types</a></li>
                        <li><a href="<?= App\Core\View::url('/admin/settings/appearance') ?>"><i class="fas fa-palette"></i> Appearance</a></li>
                    </ul>
                </li>
                <li><a href="<?= App\Core\View::url('/admin/users') ?>"><i class="fas fa-users"></i> Users</a></li>
                <li><a href="<?= App\Core\View::url('/') ?>" target="_blank"><i class="fas fa-external-link-alt"></i> View Site</a></li>
            </ul>
        </nav>
    </aside>

    <main class="content container-fluid">
        <h1><?= App\Core\View::e($pageTitle ?? 'Admin Panel') ?></h1>

        <!-- Flash Messages -->
        <?= App\Core\View::flashMessage('success', 'success') ?>
        <?= App\Core\View::flashMessage('error', 'error') ?>
        <?= App\Core\View::flashMessage('info', 'info') ?>

        <!-- Main Content Area -->
        <?= $content ?? '' ?>
    </main>

    <!-- Link to compiled JS -->
    <script src="<?= App\Core\View::asset('js/admin.js') ?>"></script>
</body>
</html>
