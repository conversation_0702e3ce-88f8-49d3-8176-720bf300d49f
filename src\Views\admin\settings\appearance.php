<?php
// This view receives $settings from the controller
$primaryColor = $settings['primary_color'] ?? '#2c3e50';
$secondaryColor = $settings['secondary_color'] ?? '#e74c3c';
$accentColor = $settings['accent_color'] ?? '#3498db';
$logoPath = $settings['company_logo'] ?? '';
?>

<div class="page-header">
    <div class="page-header-content">
        <h1><i class="fas fa-palette"></i> Appearance Settings</h1>
        <p>Customize the look and feel of your courier system</p>
    </div>
</div>

<?php if (isset($_SESSION['flash_success'])): ?>
    <div class="alert alert-success">
        <?= App\Core\View::e($_SESSION['flash_success']) ?>
        <?php unset($_SESSION['flash_success']); ?>
    </div>
<?php endif; ?>

<?php if (isset($_SESSION['flash_error'])): ?>
    <div class="alert alert-danger">
        <?= App\Core\View::e($_SESSION['flash_error']) ?>
        <?php unset($_SESSION['flash_error']); ?>
    </div>
<?php endif; ?>

<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">Color Scheme</h3>
            </div>
            <div class="card-body">
                <form action="<?= App\Core\View::url('/admin/settings/appearance') ?>" method="POST" enctype="multipart/form-data">
                    <?= App\Core\View::csrfField() ?>
                    
                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="primary_color">Primary Color</label>
                                <div class="color-input-group">
                                    <input type="color" id="primary_color" name="primary_color" 
                                           class="form-control color-picker" value="<?= $primaryColor ?>">
                                    <input type="text" class="form-control color-text" 
                                           value="<?= $primaryColor ?>" readonly>
                                </div>
                                <small class="form-text text-muted">Used for headers and navigation</small>
                            </div>
                        </div>
                        
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="secondary_color">Secondary Color</label>
                                <div class="color-input-group">
                                    <input type="color" id="secondary_color" name="secondary_color" 
                                           class="form-control color-picker" value="<?= $secondaryColor ?>">
                                    <input type="text" class="form-control color-text" 
                                           value="<?= $secondaryColor ?>" readonly>
                                </div>
                                <small class="form-text text-muted">Used for buttons and highlights</small>
                            </div>
                        </div>
                        
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="accent_color">Accent Color</label>
                                <div class="color-input-group">
                                    <input type="color" id="accent_color" name="accent_color" 
                                           class="form-control color-picker" value="<?= $accentColor ?>">
                                    <input type="text" class="form-control color-text" 
                                           value="<?= $accentColor ?>" readonly>
                                </div>
                                <small class="form-text text-muted">Used for links and accents</small>
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <h4>Predefined Color Schemes</h4>
                        <div class="color-schemes">
                            <div class="scheme-option" data-primary="#2c3e50" data-secondary="#e74c3c" data-accent="#3498db">
                                <div class="scheme-preview">
                                    <div class="color-block" style="background: #2c3e50;"></div>
                                    <div class="color-block" style="background: #e74c3c;"></div>
                                    <div class="color-block" style="background: #3498db;"></div>
                                </div>
                                <span>Professional Blue</span>
                            </div>
                            
                            <div class="scheme-option" data-primary="#1a1a1a" data-secondary="#ffffff" data-accent="#007bff">
                                <div class="scheme-preview">
                                    <div class="color-block" style="background: #1a1a1a;"></div>
                                    <div class="color-block" style="background: #ffffff; border: 1px solid #ddd;"></div>
                                    <div class="color-block" style="background: #007bff;"></div>
                                </div>
                                <span>Classic Black & White</span>
                            </div>
                            
                            <div class="scheme-option" data-primary="#343a40" data-secondary="#28a745" data-accent="#17a2b8">
                                <div class="scheme-preview">
                                    <div class="color-block" style="background: #343a40;"></div>
                                    <div class="color-block" style="background: #28a745;"></div>
                                    <div class="color-block" style="background: #17a2b8;"></div>
                                </div>
                                <span>Corporate Green</span>
                            </div>
                            
                            <div class="scheme-option" data-primary="#6f42c1" data-secondary="#fd7e14" data-accent="#20c997">
                                <div class="scheme-preview">
                                    <div class="color-block" style="background: #6f42c1;"></div>
                                    <div class="color-block" style="background: #fd7e14;"></div>
                                    <div class="color-block" style="background: #20c997;"></div>
                                </div>
                                <span>Modern Purple</span>
                            </div>
                        </div>
                    </div>

                    <hr class="my-4">

                    <div class="form-group">
                        <h4>Company Logo</h4>
                        <div class="row">
                            <div class="col-md-6">
                                <label for="company_logo">Upload Logo</label>
                                <div class="logo-upload-area" id="logoUploadArea">
                                    <?php if (!empty($logoPath) && file_exists('public/uploads/logos/' . $logoPath)): ?>
                                        <div class="current-logo">
                                            <img src="<?= App\Core\View::url('/uploads/logos/' . $logoPath) ?>" alt="Current Logo" id="currentLogoPreview">
                                            <div class="logo-overlay">
                                                <button type="button" class="btn btn-sm btn-danger" onclick="removeLogo()">
                                                    <i class="fas fa-trash"></i> Remove
                                                </button>
                                            </div>
                                        </div>
                                    <?php else: ?>
                                        <div class="upload-placeholder" id="uploadPlaceholder">
                                            <i class="fas fa-cloud-upload-alt"></i>
                                            <p>Click to upload logo or drag & drop</p>
                                            <small>Recommended: PNG, JPG, SVG (max 2MB)</small>
                                        </div>
                                    <?php endif; ?>
                                    <input type="file" id="company_logo" name="company_logo"
                                           accept="image/*" style="display: none;">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="logo-guidelines">
                                    <h5>Logo Guidelines</h5>
                                    <ul class="list-unstyled">
                                        <li><i class="fas fa-check text-success"></i> Recommended size: 200x80px</li>
                                        <li><i class="fas fa-check text-success"></i> Formats: PNG, JPG, SVG</li>
                                        <li><i class="fas fa-check text-success"></i> Max file size: 2MB</li>
                                        <li><i class="fas fa-check text-success"></i> Transparent background preferred</li>
                                    </ul>
                                    <div class="alert alert-info">
                                        <small><i class="fas fa-info-circle"></i> Your logo will appear on invoices, labels, and email templates.</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="form-actions">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> Save Color Settings
                        </button>
                        <button type="button" class="btn btn-secondary" onclick="resetToDefaults()">
                            <i class="fas fa-undo"></i> Reset to Defaults
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">Preview</h3>
            </div>
            <div class="card-body">
                <div id="color-preview" class="preview-container">
                    <div class="preview-header" style="background: <?= $primaryColor ?>; color: white; padding: 15px; border-radius: 4px 4px 0 0;">
                        <h4 style="margin: 0;">ELTA COURIER</h4>
                        <small>Admin Dashboard</small>
                    </div>
                    <div class="preview-content" style="padding: 15px; border: 1px solid #ddd; border-top: none; border-radius: 0 0 4px 4px;">
                        <button class="btn btn-sm" style="background: <?= $secondaryColor ?>; color: white; border: none; margin-right: 10px;">
                            Primary Button
                        </button>
                        <a href="#" style="color: <?= $accentColor ?>; text-decoration: none;">Sample Link</a>
                        <div style="margin-top: 15px; padding: 10px; background: #f8f9fa; border-radius: 4px;">
                            <small>This preview shows how your colors will appear in the system.</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h3 class="card-title">Usage Guidelines</h3>
            </div>
            <div class="card-body">
                <ul class="list-unstyled">
                    <li><strong>Primary Color:</strong> Headers, navigation, main branding</li>
                    <li><strong>Secondary Color:</strong> Buttons, call-to-action elements</li>
                    <li><strong>Accent Color:</strong> Links, highlights, status indicators</li>
                </ul>
                <div class="alert alert-info">
                    <small><i class="fas fa-info-circle"></i> Changes will be applied to invoices, labels, and the admin interface.</small>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.color-input-group {
    display: flex;
    gap: 10px;
}

.color-picker {
    width: 60px;
    height: 40px;
    padding: 2px;
    border-radius: 4px;
}

.color-text {
    flex: 1;
    font-family: monospace;
}

.color-schemes {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-top: 10px;
}

.scheme-option {
    cursor: pointer;
    padding: 15px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    text-align: center;
    transition: all 0.3s ease;
}

.scheme-option:hover {
    border-color: #007bff;
    box-shadow: 0 2px 10px rgba(0, 123, 255, 0.1);
}

.scheme-preview {
    display: flex;
    margin-bottom: 10px;
    border-radius: 4px;
    overflow: hidden;
}

.color-block {
    flex: 1;
    height: 30px;
}

.preview-container {
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.logo-upload-area {
    border: 2px dashed #ddd;
    border-radius: 8px;
    padding: 20px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    min-height: 120px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.logo-upload-area:hover {
    border-color: #007bff;
    background-color: #f8f9ff;
}

.logo-upload-area.dragover {
    border-color: #007bff;
    background-color: #e3f2fd;
}

.upload-placeholder {
    color: #6c757d;
}

.upload-placeholder i {
    font-size: 2rem;
    margin-bottom: 10px;
    display: block;
}

.current-logo {
    position: relative;
    display: inline-block;
}

.current-logo img {
    max-width: 200px;
    max-height: 80px;
    border-radius: 4px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.logo-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
    border-radius: 4px;
}

.current-logo:hover .logo-overlay {
    opacity: 1;
}

.logo-guidelines {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    height: 100%;
}

.logo-guidelines h5 {
    color: #495057;
    margin-bottom: 15px;
}

.logo-guidelines li {
    margin-bottom: 8px;
    font-size: 14px;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Update color text inputs when color pickers change
    document.querySelectorAll('.color-picker').forEach(picker => {
        picker.addEventListener('input', function() {
            const textInput = this.parentNode.querySelector('.color-text');
            textInput.value = this.value;
            updatePreview();
        });
    });

    // Logo upload functionality
    initializeLogoUpload();
    
    // Handle predefined scheme selection
    document.querySelectorAll('.scheme-option').forEach(option => {
        option.addEventListener('click', function() {
            const primary = this.dataset.primary;
            const secondary = this.dataset.secondary;
            const accent = this.dataset.accent;
            
            document.getElementById('primary_color').value = primary;
            document.getElementById('secondary_color').value = secondary;
            document.getElementById('accent_color').value = accent;
            
            document.querySelector('#primary_color + .color-text').value = primary;
            document.querySelector('#secondary_color + .color-text').value = secondary;
            document.querySelector('#accent_color + .color-text').value = accent;
            
            updatePreview();
        });
    });
});

function updatePreview() {
    const primary = document.getElementById('primary_color').value;
    const secondary = document.getElementById('secondary_color').value;
    const accent = document.getElementById('accent_color').value;
    
    const preview = document.getElementById('color-preview');
    const header = preview.querySelector('.preview-header');
    const button = preview.querySelector('.btn');
    const link = preview.querySelector('a');
    
    header.style.background = primary;
    button.style.background = secondary;
    link.style.color = accent;
}

function resetToDefaults() {
    document.getElementById('primary_color').value = '#2c3e50';
    document.getElementById('secondary_color').value = '#e74c3c';
    document.getElementById('accent_color').value = '#3498db';

    document.querySelector('#primary_color + .color-text').value = '#2c3e50';
    document.querySelector('#secondary_color + .color-text').value = '#e74c3c';
    document.querySelector('#accent_color + .color-text').value = '#3498db';

    updatePreview();
}

function initializeLogoUpload() {
    const uploadArea = document.getElementById('logoUploadArea');
    const fileInput = document.getElementById('company_logo');

    // Click to upload
    uploadArea.addEventListener('click', function() {
        fileInput.click();
    });

    // File selection
    fileInput.addEventListener('change', function(e) {
        handleFileSelect(e.target.files[0]);
    });

    // Drag and drop
    uploadArea.addEventListener('dragover', function(e) {
        e.preventDefault();
        uploadArea.classList.add('dragover');
    });

    uploadArea.addEventListener('dragleave', function(e) {
        e.preventDefault();
        uploadArea.classList.remove('dragover');
    });

    uploadArea.addEventListener('drop', function(e) {
        e.preventDefault();
        uploadArea.classList.remove('dragover');
        handleFileSelect(e.dataTransfer.files[0]);
    });
}

function handleFileSelect(file) {
    if (!file) return;

    // Validate file type
    if (!file.type.startsWith('image/')) {
        alert('Please select an image file (PNG, JPG, SVG)');
        return;
    }

    // Validate file size (2MB)
    if (file.size > 2 * 1024 * 1024) {
        alert('File size must be less than 2MB');
        return;
    }

    // Preview the image
    const reader = new FileReader();
    reader.onload = function(e) {
        showLogoPreview(e.target.result);
    };
    reader.readAsDataURL(file);
}

function showLogoPreview(src) {
    const uploadArea = document.getElementById('logoUploadArea');
    uploadArea.innerHTML = `
        <div class="current-logo">
            <img src="${src}" alt="Logo Preview" id="currentLogoPreview">
            <div class="logo-overlay">
                <button type="button" class="btn btn-sm btn-warning" onclick="changeLogoFile()">
                    <i class="fas fa-edit"></i> Change
                </button>
                <button type="button" class="btn btn-sm btn-danger ml-2" onclick="removeLogo()">
                    <i class="fas fa-trash"></i> Remove
                </button>
            </div>
        </div>
    `;
}

function changeLogoFile() {
    document.getElementById('company_logo').click();
}

function removeLogo() {
    const uploadArea = document.getElementById('logoUploadArea');
    const fileInput = document.getElementById('company_logo');

    fileInput.value = '';
    uploadArea.innerHTML = `
        <div class="upload-placeholder" id="uploadPlaceholder">
            <i class="fas fa-cloud-upload-alt"></i>
            <p>Click to upload logo or drag & drop</p>
            <small>Recommended: PNG, JPG, SVG (max 2MB)</small>
        </div>
    `;

    // Re-initialize upload functionality
    initializeLogoUpload();
}
</script>
