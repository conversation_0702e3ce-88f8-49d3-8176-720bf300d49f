<?php
$pageTitle = 'Document Types Management';
$breadcrumbs = [
    ['title' => 'Dashboard', 'url' => '/admin/dashboard'],
    ['title' => 'Settings', 'url' => '/admin/settings/general'],
    ['title' => 'Document Types', 'url' => '']
];
?>

<div class="page-header">
    <div class="page-header-content">
        <p class="page-description">Manage document types that can be requested for shipments</p>
    </div>
    <div class="page-actions">
        <button type="button" class="btn btn-primary" onclick="openAddDocumentTypeModal()">
            <i class="fas fa-plus"></i>
            Add Document Type
        </button>
    </div>
</div>

<div class="content-wrapper">
    <div class="card">
        <div class="card-header">
            <h3 class="card-title">
                <i class="fas fa-list"></i>
                Document Types
            </h3>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped" id="documentTypesTable">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Name</th>
                            <th>Description</th>
                            <th>Type</th>
                            <th>Status</th>
                            <th>Created</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <!-- Data will be loaded via JavaScript -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Add Document Type Modal -->
<div id="addDocumentTypeModal" class="modal fade" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Add Document Type</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form id="addDocumentTypeForm">
                <?= App\Core\View::csrfField() ?>
                <div class="modal-body">
                    <div class="form-group">
                        <label for="name">Document Type Name <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="name" name="name" required 
                               placeholder="e.g., Commercial Invoice, Bill of Lading">
                    </div>
                    
                    <div class="form-group">
                        <label for="description">Description</label>
                        <textarea class="form-control" id="description" name="description" rows="3"
                                  placeholder="Brief description of this document type"></textarea>
                    </div>
                    

                    
                    <div class="form-group">
                        <label for="is_active">Status</label>
                        <select class="form-control" id="is_active" name="is_active">
                            <option value="1">Active</option>
                            <option value="0">Inactive</option>
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i>
                        Save Document Type
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit Document Type Modal -->
<div id="editDocumentTypeModal" class="modal fade" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Edit Document Type</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form id="editDocumentTypeForm">
                <?= App\Core\View::csrfField() ?>
                <input type="hidden" id="edit_id" name="id">
                <div class="modal-body">
                    <!-- Same form fields as add modal -->
                    <div class="form-group">
                        <label for="edit_name">Document Type Name <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="edit_name" name="name" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="edit_description">Description</label>
                        <textarea class="form-control" id="edit_description" name="description" rows="3"></textarea>
                    </div>
                    
                    <div class="form-group">
                        <label for="edit_is_active">Status</label>
                        <select class="form-control" id="edit_is_active" name="is_active">
                            <option value="1">Active</option>
                            <option value="0">Inactive</option>
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i>
                        Update Document Type
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<style>
.checkbox-group {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}

.form-check {
    margin-bottom: 5px;
}

.page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 1px solid #e9ecef;
}

.page-header-content h1 {
    margin: 0;
    color: var(--primary-color);
}

.page-header-content p {
    margin: 5px 0 0 0;
    color: #6c757d;
}

.card {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    border: none;
}

.card-header {
    background: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
    padding: 15px 20px;
    border-radius: 8px 8px 0 0;
}

.card-title {
    margin: 0;
    color: var(--text-color);
    font-size: 16px;
    font-weight: 600;
}

.card-body {
    padding: 20px;
}

.table th {
    background: #f8f9fa;
    border-top: none;
    font-weight: 600;
    color: var(--text-color);
}

.btn-sm {
    padding: 4px 8px;
    font-size: 12px;
}

.status-badge {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 500;
    text-transform: uppercase;
}

.status-badge.active {
    background: #d4edda;
    color: #155724;
}

.status-badge.inactive {
    background: #f8d7da;
    color: #721c24;
}

.status-badge.predefined {
    background: #d1ecf1;
    color: #0c5460;
}

.status-badge.custom {
    background: #fff3cd;
    color: #856404;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    loadDocumentTypes();
    
    // Initialize modals
    $('#addDocumentTypeForm').on('submit', function(e) {
        e.preventDefault();
        saveDocumentType();
    });
    
    $('#editDocumentTypeForm').on('submit', function(e) {
        e.preventDefault();
        updateDocumentType();
    });
});

function loadDocumentTypes() {
    fetch('<?= App\Core\View::url('/api/admin/document-types.php') ?>')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                populateDocumentTypesTable(data.data);
            } else {
                console.error('Failed to load document types:', data.message);
            }
        })
        .catch(error => {
            console.error('Error loading document types:', error);
        });
}

function populateDocumentTypesTable(documentTypes) {
    const tbody = document.querySelector('#documentTypesTable tbody');
    tbody.innerHTML = '';
    
    documentTypes.forEach(type => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td>${type.id}</td>
            <td>${type.name}</td>
            <td>${type.description || '-'}</td>
            <td>
                <span class="status-badge ${type.is_predefined ? 'predefined' : 'custom'}">
                    ${type.is_predefined ? 'Predefined' : 'Custom'}
                </span>
            </td>
            <td>
                <span class="status-badge ${type.is_active ? 'active' : 'inactive'}">
                    ${type.is_active ? 'Active' : 'Inactive'}
                </span>
            </td>
            <td>${new Date(type.created_at).toLocaleDateString()}</td>
            <td>
                <button class="btn btn-sm btn-primary" onclick="editDocumentType(${type.id})" ${type.is_predefined ? 'disabled title="Cannot edit predefined types"' : ''}>
                    <i class="fas fa-edit"></i>
                </button>
                <button class="btn btn-sm btn-danger" onclick="deleteDocumentType(${type.id})" ${type.is_predefined ? 'disabled title="Cannot delete predefined types"' : ''}>
                    <i class="fas fa-trash"></i>
                </button>
            </td>
        `;
        tbody.appendChild(row);
    });
}

function openAddDocumentTypeModal() {
    $('#addDocumentTypeModal').modal('show');
}

function saveDocumentType() {
    const formData = new FormData(document.getElementById('addDocumentTypeForm'));
    
    fetch('<?= App\Core\View::url('/api/admin/document-types.php') ?>', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            $('#addDocumentTypeModal').modal('hide');
            document.getElementById('addDocumentTypeForm').reset();
            loadDocumentTypes();
            alert('Document type added successfully!');
        } else {
            alert('Error: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error saving document type:', error);
        alert('An error occurred while saving the document type.');
    });
}

function editDocumentType(id) {
    // Load document type data and populate edit modal
    fetch(`<?= App\Core\View::url('/api/admin/document-types.php') ?>?id=${id}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const type = data.data;
                document.getElementById('edit_id').value = type.id;
                document.getElementById('edit_name').value = type.name;
                document.getElementById('edit_description').value = type.description || '';
                document.getElementById('edit_is_active').value = type.is_active;
                $('#editDocumentTypeModal').modal('show');
            } else {
                alert('Error loading document type: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error loading document type:', error);
        });
}

function updateDocumentType() {
    const formData = new FormData(document.getElementById('editDocumentTypeForm'));
    
    fetch('<?= App\Core\View::url('/api/admin/document-types.php') ?>', {
        method: 'PUT',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            $('#editDocumentTypeModal').modal('hide');
            loadDocumentTypes();
            alert('Document type updated successfully!');
        } else {
            alert('Error: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error updating document type:', error);
        alert('An error occurred while updating the document type.');
    });
}

function deleteDocumentType(id) {
    if (confirm('Are you sure you want to delete this document type?')) {
        fetch(`<?= App\Core\View::url('/api/admin/document-types.php') ?>?id=${id}`, {
            method: 'DELETE'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                loadDocumentTypes();
                alert('Document type deleted successfully!');
            } else {
                alert('Error: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error deleting document type:', error);
            alert('An error occurred while deleting the document type.');
        });
    }
}
</script>
