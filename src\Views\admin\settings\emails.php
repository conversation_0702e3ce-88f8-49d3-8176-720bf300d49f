<?php
// Email settings view
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>Email Settings</h1>
    <div>
        <a href="<?= App\Core\View::url('/admin/dashboard') ?>" class="btn btn-primary">
            <i class="fas fa-arrow-left"></i> Back to Dashboard
        </a>
    </div>
</div>

<?php if (isset($flash_success)): ?>
    <div class="alert alert-success">
        <?= App\Core\View::e($flash_success) ?>
    </div>
<?php endif; ?>

<?php if (isset($flash_error)): ?>
    <div class="alert alert-error">
        <?= App\Core\View::e($flash_error) ?>
    </div>
<?php endif; ?>

<div class="card">
    <div class="card-header">Email Configuration</div>
    <div class="card-body">
        <form action="<?= App\Core\View::url('/admin/settings/update-emails') ?>" method="POST">
            <?= App\Core\View::csrfField() ?>
            
            <div class="mb-4">
                <p>Configure the email settings for sending notifications and alerts.</p>
            </div>
            
            <h3 class="mb-3">SMTP Settings</h3>
            
            <div class="form-group">
                <label for="mail_mailer">Mail Driver</label>
                <select id="mail_mailer" name="mail_mailer" class="form-control">
                    <?php 
                    $mailers = ['smtp' => 'SMTP', 'sendmail' => 'Sendmail', 'log' => 'Log (for testing)'];
                    $selectedMailer = isset($settings['mail_mailer']) ? $settings['mail_mailer'] : 'smtp';
                    
                    foreach ($mailers as $value => $label): 
                    ?>
                        <option value="<?= $value ?>" <?= $selectedMailer === $value ? 'selected' : '' ?>>
                            <?= App\Core\View::e($label) ?>
                        </option>
                    <?php endforeach; ?>
                </select>
                <small class="text-muted">The mail driver to use for sending emails.</small>
            </div>
            
            <div class="smtp-settings" <?= $selectedMailer !== 'smtp' ? 'style="display: none;"' : '' ?>>
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="mail_host">SMTP Host</label>
                            <input type="text" id="mail_host" name="mail_host" class="form-control" 
                                value="<?= isset($settings['mail_host']) ? App\Core\View::e($settings['mail_host']) : '' ?>">
                            <small class="text-muted">The SMTP server host (e.g., smtp.gmail.com).</small>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="mail_port">SMTP Port</label>
                            <input type="number" id="mail_port" name="mail_port" class="form-control" 
                                value="<?= isset($settings['mail_port']) ? App\Core\View::e($settings['mail_port']) : '587' ?>">
                            <small class="text-muted">The SMTP server port (usually 587 for TLS, 465 for SSL).</small>
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="mail_username">SMTP Username</label>
                            <input type="text" id="mail_username" name="mail_username" class="form-control" 
                                value="<?= isset($settings['mail_username']) ? App\Core\View::e($settings['mail_username']) : '' ?>">
                            <small class="text-muted">The SMTP server username.</small>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="mail_password">SMTP Password</label>
                            <input type="password" id="mail_password" name="mail_password" class="form-control" 
                                value="<?= isset($settings['mail_password']) ? '********' : '' ?>">
                            <small class="text-muted">The SMTP server password. Leave unchanged to keep the current password.</small>
                        </div>
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="mail_encryption">Encryption</label>
                    <select id="mail_encryption" name="mail_encryption" class="form-control">
                        <?php 
                        $encryptions = ['' => 'None', 'tls' => 'TLS', 'ssl' => 'SSL'];
                        $selectedEncryption = isset($settings['mail_encryption']) ? $settings['mail_encryption'] : 'tls';
                        
                        foreach ($encryptions as $value => $label): 
                        ?>
                            <option value="<?= $value ?>" <?= $selectedEncryption === $value ? 'selected' : '' ?>>
                                <?= App\Core\View::e($label) ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                    <small class="text-muted">The encryption protocol to use (TLS is recommended).</small>
                </div>
            </div>
            
            <h3 class="mt-4 mb-3">From Address</h3>
            
            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="mail_from_address">From Email Address</label>
                        <input type="email" id="mail_from_address" name="mail_from_address" class="form-control" 
                            value="<?= isset($settings['mail_from_address']) ? App\Core\View::e($settings['mail_from_address']) : '' ?>" 
                            required>
                        <small class="text-muted">The email address that will appear in the "From" field.</small>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="mail_from_name">From Name</label>
                        <input type="text" id="mail_from_name" name="mail_from_name" class="form-control" 
                            value="<?= isset($settings['mail_from_name']) ? App\Core\View::e($settings['mail_from_name']) : '' ?>" 
                            required>
                        <small class="text-muted">The name that will appear in the "From" field.</small>
                    </div>
                </div>
            </div>
            
            <h3 class="mt-4 mb-3">Notification Settings</h3>
            
            <div class="form-group">
                <div class="form-check">
                    <input type="checkbox" class="form-check-input" id="notify_admin_new_shipment" name="notify_admin_new_shipment" value="1" 
                        <?= isset($settings['notify_admin_new_shipment']) && $settings['notify_admin_new_shipment'] ? 'checked' : '' ?>>
                    <label class="form-check-label" for="notify_admin_new_shipment">
                        Notify administrators when a new shipment is created
                    </label>
                </div>
            </div>
            
            <div class="form-group">
                <div class="form-check">
                    <input type="checkbox" class="form-check-input" id="notify_customer_status_change" name="notify_customer_status_change" value="1" 
                        <?= isset($settings['notify_customer_status_change']) && $settings['notify_customer_status_change'] ? 'checked' : '' ?>>
                    <label class="form-check-label" for="notify_customer_status_change">
                        Notify customers when their shipment status changes
                    </label>
                </div>
            </div>
            
            <div class="form-group">
                <div class="form-check">
                    <input type="checkbox" class="form-check-input" id="notify_customer_delivery" name="notify_customer_delivery" value="1" 
                        <?= isset($settings['notify_customer_delivery']) && $settings['notify_customer_delivery'] ? 'checked' : '' ?>>
                    <label class="form-check-label" for="notify_customer_delivery">
                        Notify customers when their shipment is delivered
                    </label>
                </div>
            </div>
            
            <div class="form-group mt-4">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save"></i> Save Settings
                </button>
                <button type="button" id="test-email" class="btn btn-secondary">
                    <i class="fas fa-envelope"></i> Send Test Email
                </button>
            </div>
        </form>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Show/hide SMTP settings based on mail driver
        const mailMailer = document.getElementById('mail_mailer');
        const smtpSettings = document.querySelector('.smtp-settings');
        
        mailMailer.addEventListener('change', function() {
            if (this.value === 'smtp') {
                smtpSettings.style.display = 'block';
            } else {
                smtpSettings.style.display = 'none';
            }
        });
        
        // Test email button (placeholder - would need AJAX implementation)
        document.getElementById('test-email').addEventListener('click', function() {
            alert('This feature is not yet implemented. In a real application, this would send a test email using the current settings.');
        });
    });
</script>
