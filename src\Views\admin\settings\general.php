<?php
// General settings view
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>General Settings</h1>
    <div>
        <a href="<?= App\Core\View::url('/admin/dashboard') ?>" class="btn btn-primary">
            <i class="fas fa-arrow-left"></i> Back to Dashboard
        </a>
    </div>
</div>

<?php if (isset($flash_success)): ?>
    <div class="alert alert-success">
        <?= App\Core\View::e($flash_success) ?>
    </div>
<?php endif; ?>

<?php if (isset($flash_error)): ?>
    <div class="alert alert-error">
        <?= App\Core\View::e($flash_error) ?>
    </div>
<?php endif; ?>

<div class="card">
    <div class="card-header">General Settings</div>
    <div class="card-body">
        <form action="<?= App\Core\View::url('/admin/settings/update-general') ?>" method="POST">
            <?= App\Core\View::csrfField() ?>

            <div class="form-group">
                <label for="app_name">Application Name</label>
                <input type="text" id="app_name" name="app_name" class="form-control"
                    value="<?= isset($settings['app_name']) ? App\Core\View::e($settings['app_name']) : 'ELTA Courier' ?>"
                    required>
                <small class="text-muted">The name of your application, displayed in the header.</small>
            </div>

            <div class="form-group">
                <label for="site_title">Site Title</label>
                <input type="text" id="site_title" name="site_title" class="form-control"
                    value="<?= isset($settings['site_title']) ? App\Core\View::e($settings['site_title']) : 'ELTA Courier - Shipment Tracking' ?>"
                    required>
                <small class="text-muted">The title displayed in the browser tab.</small>
            </div>

            <div class="form-group">
                <label for="company_name">Company Name</label>
                <input type="text" id="company_name" name="company_name" class="form-control"
                    value="<?= isset($settings['company_name']) ? App\Core\View::e($settings['company_name']) : 'ELTA Courier Services' ?>">
                <small class="text-muted">Your company's legal name, used in emails and documents.</small>
            </div>

            <div class="form-group">
                <label for="company_address">Company Address</label>
                <textarea id="company_address" name="company_address" class="form-control" rows="3"><?= isset($settings['company_address']) ? App\Core\View::e($settings['company_address']) : '' ?></textarea>
                <small class="text-muted">Your company's physical address, used in emails and documents.</small>
            </div>

            <div class="form-group">
                <label for="company_phone">Company Phone</label>
                <input type="text" id="company_phone" name="company_phone" class="form-control"
                    value="<?= isset($settings['company_phone']) ? App\Core\View::e($settings['company_phone']) : '' ?>">
                <small class="text-muted">Your company's contact phone number.</small>
            </div>

            <div class="form-group">
                <label for="company_email">Company Email</label>
                <input type="email" id="company_email" name="company_email" class="form-control"
                    value="<?= isset($settings['company_email']) ? App\Core\View::e($settings['company_email']) : '' ?>">
                <small class="text-muted">Your company's contact email address.</small>
            </div>

            <div class="form-group">
                <label for="currency">Currency</label>
                <select id="currency" name="currency" class="form-control">
                    <?php
                    $currencies = [
                        'USD' => 'USD - US Dollar',
                        'EUR' => 'EUR - Euro',
                        'GBP' => 'GBP - British Pound',
                        'CAD' => 'CAD - Canadian Dollar',
                        'AUD' => 'AUD - Australian Dollar',
                        'JPY' => 'JPY - Japanese Yen',
                        'CNY' => 'CNY - Chinese Yuan',
                        'INR' => 'INR - Indian Rupee',
                        'BRL' => 'BRL - Brazilian Real',
                        'ZAR' => 'ZAR - South African Rand',
                        'NGN' => 'NGN - Nigerian Naira',
                        'MXN' => 'MXN - Mexican Peso',
                        'SGD' => 'SGD - Singapore Dollar',
                        'CHF' => 'CHF - Swiss Franc',
                        'AED' => 'AED - UAE Dirham'
                    ];

                    $selectedCurrency = isset($settings['currency']) ? $settings['currency'] : 'USD';

                    foreach ($currencies as $value => $label):
                    ?>
                        <option value="<?= $value ?>" <?= $selectedCurrency === $value ? 'selected' : '' ?>>
                            <?= App\Core\View::e($label) ?>
                        </option>
                    <?php endforeach; ?>
                </select>
                <small class="text-muted">The currency used for pricing throughout the application.</small>
            </div>

            <div class="form-group">
                <label for="currency_symbol">Currency Symbol</label>
                <input type="text" id="currency_symbol" name="currency_symbol" class="form-control"
                    value="<?= isset($settings['currency_symbol']) ? App\Core\View::e($settings['currency_symbol']) : '$' ?>">
                <small class="text-muted">The symbol used for the selected currency (e.g., $, €, £).</small>
            </div>

            <div class="form-group">
                <label for="timezone">Timezone</label>
                <select id="timezone" name="timezone" class="form-control">
                    <?php
                    $timezones = [
                        'UTC' => 'UTC',
                        'America/New_York' => 'Eastern Time (US & Canada)',
                        'America/Chicago' => 'Central Time (US & Canada)',
                        'America/Denver' => 'Mountain Time (US & Canada)',
                        'America/Los_Angeles' => 'Pacific Time (US & Canada)',
                        'Europe/London' => 'London',
                        'Europe/Paris' => 'Paris',
                        'Europe/Berlin' => 'Berlin',
                        'Asia/Tokyo' => 'Tokyo',
                        'Asia/Shanghai' => 'Shanghai',
                        'Australia/Sydney' => 'Sydney'
                    ];

                    $selectedTimezone = isset($settings['timezone']) ? $settings['timezone'] : 'UTC';

                    foreach ($timezones as $value => $label):
                    ?>
                        <option value="<?= $value ?>" <?= $selectedTimezone === $value ? 'selected' : '' ?>>
                            <?= App\Core\View::e($label) ?>
                        </option>
                    <?php endforeach; ?>
                </select>
                <small class="text-muted">The timezone used for dates and times.</small>
            </div>

            <div class="form-group">
                <label for="date_format">Date Format</label>
                <select id="date_format" name="date_format" class="form-control">
                    <?php
                    $dateFormats = [
                        'Y-m-d' => date('Y-m-d') . ' (YYYY-MM-DD)',
                        'm/d/Y' => date('m/d/Y') . ' (MM/DD/YYYY)',
                        'd/m/Y' => date('d/m/Y') . ' (DD/MM/YYYY)',
                        'M j, Y' => date('M j, Y') . ' (Month Day, Year)'
                    ];

                    $selectedFormat = isset($settings['date_format']) ? $settings['date_format'] : 'Y-m-d';

                    foreach ($dateFormats as $value => $label):
                    ?>
                        <option value="<?= $value ?>" <?= $selectedFormat === $value ? 'selected' : '' ?>>
                            <?= App\Core\View::e($label) ?>
                        </option>
                    <?php endforeach; ?>
                </select>
                <small class="text-muted">The format used for displaying dates.</small>
            </div>

            <div class="form-group mt-4">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save"></i> Save Settings
                </button>
            </div>
        </form>
    </div>
</div>
