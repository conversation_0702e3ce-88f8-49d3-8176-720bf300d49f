<?php
// Package settings view
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>Package Settings</h1>
    <div>
        <a href="<?= App\Core\View::url('/admin/dashboard') ?>" class="btn btn-primary">
            <i class="fas fa-arrow-left"></i> Back to Dashboard
        </a>
    </div>
</div>

<?php if (isset($flash_success)): ?>
    <div class="alert alert-success">
        <?= App\Core\View::e($flash_success) ?>
    </div>
<?php endif; ?>

<?php if (isset($flash_error)): ?>
    <div class="alert alert-error">
        <?= App\Core\View::e($flash_error) ?>
    </div>
<?php endif; ?>

<div class="card">
    <div class="card-header">Package Types</div>
    <div class="card-body">
        <form action="<?= App\Core\View::url('/admin/settings/update-packages') ?>" method="POST">
            <?= App\Core\View::csrfField() ?>
            
            <div class="mb-4">
                <p>Configure the package types available for shipments. These will be available as options when creating shipments.</p>
            </div>
            
            <div id="package-types-container">
                <?php
                // Get package types from settings
                $packageTypes = isset($settings['package_types']) ? json_decode($settings['package_types'], true) : [];
                
                // If no package types defined, add some defaults
                if (empty($packageTypes)) {
                    $packageTypes = [
                        ['name' => 'Envelope', 'max_weight' => 0.5, 'dimensions' => '30x20x1', 'price' => 5.99],
                        ['name' => 'Small Box', 'max_weight' => 2, 'dimensions' => '20x20x20', 'price' => 9.99],
                        ['name' => 'Medium Box', 'max_weight' => 5, 'dimensions' => '30x30x30', 'price' => 14.99],
                        ['name' => 'Large Box', 'max_weight' => 10, 'dimensions' => '40x40x40', 'price' => 19.99]
                    ];
                }
                
                foreach ($packageTypes as $index => $type):
                ?>
                    <div class="package-type-row mb-3 p-3 border rounded">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>Package Name</label>
                                    <input type="text" name="package_type[<?= $index ?>][name]" class="form-control" 
                                        value="<?= App\Core\View::e($type['name']) ?>" required>
                                </div>
                            </div>
                            
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>Max Weight (kg)</label>
                                    <input type="number" step="0.1" name="package_type[<?= $index ?>][max_weight]" class="form-control" 
                                        value="<?= App\Core\View::e($type['max_weight']) ?>" required>
                                </div>
                            </div>
                            
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>Dimensions (cm)</label>
                                    <input type="text" name="package_type[<?= $index ?>][dimensions]" class="form-control" 
                                        value="<?= App\Core\View::e($type['dimensions']) ?>" 
                                        placeholder="LxWxH">
                                </div>
                            </div>
                            
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label>Base Price</label>
                                    <input type="number" step="0.01" name="package_type[<?= $index ?>][price]" class="form-control" 
                                        value="<?= App\Core\View::e($type['price']) ?>" required>
                                </div>
                            </div>
                            
                            <div class="col-md-1">
                                <div class="form-group">
                                    <label>&nbsp;</label>
                                    <button type="button" class="btn btn-danger form-control remove-package-type">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
            
            <div class="mb-3">
                <button type="button" id="add-package-type" class="btn btn-success">
                    <i class="fas fa-plus"></i> Add Package Type
                </button>
            </div>
            
            <hr>
            
            <h3 class="mt-4 mb-3">Pricing Settings</h3>
            
            <div class="form-group">
                <label for="distance_price_factor">Distance Price Factor</label>
                <input type="number" step="0.01" id="distance_price_factor" name="distance_price_factor" class="form-control" 
                    value="<?= isset($settings['distance_price_factor']) ? App\Core\View::e($settings['distance_price_factor']) : '0.1' ?>">
                <small class="text-muted">Price per kilometer (in addition to base package price).</small>
            </div>
            
            <div class="form-group">
                <label for="weight_price_factor">Weight Price Factor</label>
                <input type="number" step="0.01" id="weight_price_factor" name="weight_price_factor" class="form-control" 
                    value="<?= isset($settings['weight_price_factor']) ? App\Core\View::e($settings['weight_price_factor']) : '0.5' ?>">
                <small class="text-muted">Additional price per kg above the package's base weight.</small>
            </div>
            
            <div class="form-group">
                <label for="express_delivery_factor">Express Delivery Factor</label>
                <input type="number" step="0.01" id="express_delivery_factor" name="express_delivery_factor" class="form-control" 
                    value="<?= isset($settings['express_delivery_factor']) ? App\Core\View::e($settings['express_delivery_factor']) : '1.5' ?>">
                <small class="text-muted">Multiplier for express delivery (e.g., 1.5 = 50% more expensive).</small>
            </div>
            
            <div class="form-group mt-4">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save"></i> Save Settings
                </button>
            </div>
        </form>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Add new package type
        document.getElementById('add-package-type').addEventListener('click', function() {
            const container = document.getElementById('package-types-container');
            const index = container.querySelectorAll('.package-type-row').length;
            
            const newRow = document.createElement('div');
            newRow.className = 'package-type-row mb-3 p-3 border rounded';
            newRow.innerHTML = `
                <div class="row">
                    <div class="col-md-3">
                        <div class="form-group">
                            <label>Package Name</label>
                            <input type="text" name="package_type[${index}][name]" class="form-control" required>
                        </div>
                    </div>
                    
                    <div class="col-md-3">
                        <div class="form-group">
                            <label>Max Weight (kg)</label>
                            <input type="number" step="0.1" name="package_type[${index}][max_weight]" class="form-control" required>
                        </div>
                    </div>
                    
                    <div class="col-md-3">
                        <div class="form-group">
                            <label>Dimensions (cm)</label>
                            <input type="text" name="package_type[${index}][dimensions]" class="form-control" placeholder="LxWxH">
                        </div>
                    </div>
                    
                    <div class="col-md-2">
                        <div class="form-group">
                            <label>Base Price</label>
                            <input type="number" step="0.01" name="package_type[${index}][price]" class="form-control" required>
                        </div>
                    </div>
                    
                    <div class="col-md-1">
                        <div class="form-group">
                            <label>&nbsp;</label>
                            <button type="button" class="btn btn-danger form-control remove-package-type">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>
                </div>
            `;
            
            container.appendChild(newRow);
            
            // Add event listener to the new remove button
            newRow.querySelector('.remove-package-type').addEventListener('click', function() {
                container.removeChild(newRow);
            });
        });
        
        // Remove package type
        document.querySelectorAll('.remove-package-type').forEach(button => {
            button.addEventListener('click', function() {
                const row = this.closest('.package-type-row');
                row.parentNode.removeChild(row);
            });
        });
    });
</script>
