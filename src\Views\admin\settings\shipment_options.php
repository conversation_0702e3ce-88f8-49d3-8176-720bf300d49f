<style>
    /* Custom styles for tabs */
    #shipmentOptionsTabs {
        display: flex;
        flex-wrap: wrap;
        border-bottom: 1px solid #dee2e6;
        margin-bottom: 1.5rem;
        margin-top: 1.5rem;
        justify-content: flex-start;
        width: 100%;
    }

    #shipmentOptionsTabs .nav-item {
        margin-bottom: -1px;
        margin-right: 15px;
    }

    #shipmentOptionsTabs .nav-link {
        color: #333;
        border-radius: 0;
        padding: 12px 25px;
        font-weight: 500;
        border: 1px solid transparent;
        white-space: nowrap;
    }

    #shipmentOptionsTabs .nav-link.active {
        color: #000;
        background-color: #fff;
        border-bottom: 2px solid #000;
    }

    #shipmentOptionsTabs .nav-link:hover:not(.active) {
        background-color: #f8f9fa;
        border-color: transparent;
    }

    #shipmentOptionsTabs .nav-link i {
        margin-right: 8px;
    }

    /* Action buttons styling */
    .action-buttons {
        display: flex;
        align-items: center;
    }

    .action-buttons .btn {
        margin-right: 5px;
    }

    .action-buttons .separator {
        margin: 0 5px;
        color: #ccc;
    }

    /* Make sure country badges are visible */
    .location-tag {
        display: inline-block;
        margin: 5px;
        padding: 5px 10px;
        background-color: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 4px;
        color: #000 !important; /* Force black text color */
    }

    /* Override any Bootstrap badge text color */
    .badge.text-dark,
    .badge.bg-light {
        color: #000 !important;
        background-color: #f8f9fa !important;
    }

    /* Fix for country badges */
    .location-tags-container .badge {
        color: #000 !important;
        background-color: #f8f9fa !important;
        border: 1px solid #dee2e6 !important;
        font-weight: normal;
    }
</style>

<!-- Flash Messages -->
<?php if (isset($flash_success)): ?>
<div class="alert alert-success d-flex align-items-center mb-4" role="alert">
    <i class="fas fa-check-circle me-2"></i>
    <div>
        <?= App\Core\View::e($flash_success) ?>
    </div>
</div>
<?php endif; ?>

<?php if (isset($flash_error)): ?>
<div class="alert alert-danger d-flex align-items-center mb-4" role="alert">
    <i class="fas fa-exclamation-circle me-2"></i>
    <div>
        <?= App\Core\View::e($flash_error) ?>
    </div>
</div>
<?php endif; ?>

<div class="row">
    <div class="col-md-12 mb-4">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h2>Shipment Options Settings</h2>
            </div>
            <div class="card-body">
                <p>Manage all shipment-related options in one place. These options will be available in the shipment creation form.</p>

                <!-- Tabs Navigation -->
                <ul class="nav nav-tabs" id="shipmentOptionsTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="shipment-types-tab" data-bs-toggle="tab" data-bs-target="#shipment-types" type="button" role="tab" aria-controls="shipment-types" aria-selected="true">
                            <i class="fas fa-box"></i> Shipment Types
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="shipment-modes-tab" data-bs-toggle="tab" data-bs-target="#shipment-modes" type="button" role="tab" aria-controls="shipment-modes" aria-selected="false">
                            <i class="fas fa-truck"></i> Shipment Modes
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="payment-modes-tab" data-bs-toggle="tab" data-bs-target="#payment-modes" type="button" role="tab" aria-controls="payment-modes" aria-selected="false">
                            <i class="fas fa-credit-card"></i> Payment Modes
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="carriers-tab" data-bs-toggle="tab" data-bs-target="#carriers" type="button" role="tab" aria-controls="carriers" aria-selected="false">
                            <i class="fas fa-shipping-fast"></i> Carriers
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="locations-tab" data-bs-toggle="tab" data-bs-target="#locations" type="button" role="tab" aria-controls="locations" aria-selected="false">
                            <i class="fas fa-globe"></i> Locations
                        </button>
                    </li>
                </ul>
            </div>
        </div>
    </div>
</div>

<!-- Tab Content -->
<div class="row">
    <div class="col-md-12 mb-4">
        <div class="card">
            <div class="card-body">
                <div class="tab-content" id="shipmentOptionsTabContent">
                    <!-- Shipment Types Tab -->
                    <div class="tab-pane fade show active" id="shipment-types" role="tabpanel" aria-labelledby="shipment-types-tab">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h3>Shipment Types</h3>
                            <div>
                                <div class="input-group">
                                    <input type="text" id="shipment-types-search" class="form-control" placeholder="Search types...">
                                    <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#add-shipment-type-modal">
                                        <i class="fas fa-plus"></i> Add New
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th>Name</th>
                                        <th>Description</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody id="shipment-types-table">
                                    <?php if (empty($shipmentTypes)): ?>
                                        <tr>
                                            <td colspan="3" class="text-center">No shipment types found.</td>
                                        </tr>
                                    <?php else: ?>
                                        <?php foreach ($shipmentTypes as $type): ?>
                                            <tr class="option-row">
                                                <td><?= App\Core\View::e($type['name'] ?? $type) ?></td>
                                                <td><?= App\Core\View::e($type['description'] ?? '') ?></td>
                                                <td>
                                                    <div class="action-buttons">
                                                        <button type="button" class="btn btn-sm btn-outline-primary edit-option-btn"
                                                            data-option-type="shipment_type"
                                                            data-id="<?= isset($type['id']) ? $type['id'] : '' ?>"
                                                            data-name="<?= App\Core\View::e($type['name'] ?? $type) ?>"
                                                            data-description="<?= App\Core\View::e($type['description'] ?? '') ?>">
                                                            <i class="fas fa-edit"></i> Edit
                                                        </button>
                                                        <span class="separator">|</span>
                                                        <form action="<?= App\Core\View::url('/admin/settings/delete-option') ?>" method="POST" class="d-inline">
                                                            <?= App\Core\View::csrfField() ?>
                                                            <input type="hidden" name="option_type" value="shipment_type">
                                                            <input type="hidden" name="name" value="<?= App\Core\View::e($type['name'] ?? $type) ?>">
                                                            <button type="submit" class="btn btn-sm btn-outline-danger" onclick="return confirm('Are you sure you want to delete this item?')">
                                                                <i class="fas fa-trash"></i> Delete
                                                            </button>
                                                        </form>
                                                    </div>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>
                        <div class="mt-3">
                            <nav aria-label="Shipment types pagination">
                                <ul class="pagination justify-content-center" id="shipment-types-pagination">
                                    <!-- Pagination will be added by JavaScript -->
                                </ul>
                            </nav>
                        </div>
                    </div>

                    <!-- Shipment Modes Tab -->
                    <div class="tab-pane fade" id="shipment-modes" role="tabpanel" aria-labelledby="shipment-modes-tab">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h3>Shipment Modes</h3>
                            <div>
                                <div class="input-group">
                                    <input type="text" id="shipment-modes-search" class="form-control" placeholder="Search modes...">
                                    <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#add-shipment-mode-modal">
                                        <i class="fas fa-plus"></i> Add New
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th>Name</th>
                                        <th>Description</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody id="shipment-modes-table">
                                    <?php if (empty($shipmentModes)): ?>
                                        <tr>
                                            <td colspan="3" class="text-center">No shipment modes found.</td>
                                        </tr>
                                    <?php else: ?>
                                        <?php foreach ($shipmentModes as $mode): ?>
                                            <tr class="option-row">
                                                <td><?= App\Core\View::e($mode['name'] ?? $mode) ?></td>
                                                <td><?= App\Core\View::e($mode['description'] ?? '') ?></td>
                                                <td>
                                                    <div class="action-buttons">
                                                        <button type="button" class="btn btn-sm btn-outline-primary edit-option-btn"
                                                            data-option-type="shipment_mode"
                                                            data-id="<?= isset($mode['id']) ? $mode['id'] : '' ?>"
                                                            data-name="<?= App\Core\View::e($mode['name'] ?? $mode) ?>"
                                                            data-description="<?= App\Core\View::e($mode['description'] ?? '') ?>">
                                                            <i class="fas fa-edit"></i> Edit
                                                        </button>
                                                        <span class="separator">|</span>
                                                        <form action="<?= App\Core\View::url('/admin/settings/delete-option') ?>" method="POST" class="d-inline">
                                                            <?= App\Core\View::csrfField() ?>
                                                            <input type="hidden" name="option_type" value="shipment_mode">
                                                            <input type="hidden" name="name" value="<?= App\Core\View::e($mode['name'] ?? $mode) ?>">
                                                            <button type="submit" class="btn btn-sm btn-outline-danger" onclick="return confirm('Are you sure you want to delete this item?')">
                                                                <i class="fas fa-trash"></i> Delete
                                                            </button>
                                                        </form>
                                                    </div>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>
                        <div class="mt-3">
                            <nav aria-label="Shipment modes pagination">
                                <ul class="pagination justify-content-center" id="shipment-modes-pagination">
                                    <!-- Pagination will be added by JavaScript -->
                                </ul>
                            </nav>
                        </div>
                    </div>

                    <!-- Payment Modes Tab -->
                    <div class="tab-pane fade" id="payment-modes" role="tabpanel" aria-labelledby="payment-modes-tab">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h3>Payment Modes</h3>
                            <div>
                                <div class="input-group">
                                    <input type="text" id="payment-modes-search" class="form-control" placeholder="Search payment modes...">
                                    <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#add-payment-mode-modal">
                                        <i class="fas fa-plus"></i> Add New
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th>Name</th>
                                        <th>Description</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody id="payment-modes-table">
                                    <?php if (empty($paymentModes)): ?>
                                        <tr>
                                            <td colspan="3" class="text-center">No payment modes found.</td>
                                        </tr>
                                    <?php else: ?>
                                        <?php foreach ($paymentModes as $mode): ?>
                                            <tr class="option-row">
                                                <td><?= App\Core\View::e($mode['name'] ?? $mode) ?></td>
                                                <td><?= App\Core\View::e($mode['description'] ?? '') ?></td>
                                                <td>
                                                    <div class="action-buttons">
                                                        <button type="button" class="btn btn-sm btn-outline-primary edit-option-btn"
                                                            data-option-type="payment_mode"
                                                            data-id="<?= isset($mode['id']) ? $mode['id'] : '' ?>"
                                                            data-name="<?= App\Core\View::e($mode['name'] ?? $mode) ?>"
                                                            data-description="<?= App\Core\View::e($mode['description'] ?? '') ?>">
                                                            <i class="fas fa-edit"></i> Edit
                                                        </button>
                                                        <span class="separator">|</span>
                                                        <form action="<?= App\Core\View::url('/admin/settings/delete-option') ?>" method="POST" class="d-inline">
                                                            <?= App\Core\View::csrfField() ?>
                                                            <input type="hidden" name="option_type" value="payment_mode">
                                                            <input type="hidden" name="name" value="<?= App\Core\View::e($mode['name'] ?? $mode) ?>">
                                                            <button type="submit" class="btn btn-sm btn-outline-danger" onclick="return confirm('Are you sure you want to delete this item?')">
                                                                <i class="fas fa-trash"></i> Delete
                                                            </button>
                                                        </form>
                                                    </div>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>
                        <div class="mt-3">
                            <nav aria-label="Payment modes pagination">
                                <ul class="pagination justify-content-center" id="payment-modes-pagination">
                                    <!-- Pagination will be added by JavaScript -->
                                </ul>
                            </nav>
                        </div>
                    </div>

                    <!-- Carriers Tab -->
                    <div class="tab-pane fade" id="carriers" role="tabpanel" aria-labelledby="carriers-tab">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h3>Carriers</h3>
                            <div>
                                <div class="input-group">
                                    <input type="text" id="carriers-search" class="form-control" placeholder="Search carriers...">
                                    <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#add-carrier-modal">
                                        <i class="fas fa-plus"></i> Add New
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th>Name</th>
                                        <th>Description</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody id="carriers-table">
                                    <?php if (empty($carriers)): ?>
                                        <tr>
                                            <td colspan="3" class="text-center">No carriers found.</td>
                                        </tr>
                                    <?php else: ?>
                                        <?php foreach ($carriers as $carrier): ?>
                                            <tr class="option-row">
                                                <td><?= App\Core\View::e($carrier['name'] ?? $carrier) ?></td>
                                                <td><?= App\Core\View::e($carrier['description'] ?? '') ?></td>
                                                <td>
                                                    <div class="action-buttons">
                                                        <button type="button" class="btn btn-sm btn-outline-primary edit-option-btn"
                                                            data-option-type="carrier"
                                                            data-id="<?= isset($carrier['id']) ? $carrier['id'] : '' ?>"
                                                            data-name="<?= App\Core\View::e($carrier['name'] ?? $carrier) ?>"
                                                            data-description="<?= App\Core\View::e($carrier['description'] ?? '') ?>">
                                                            <i class="fas fa-edit"></i> Edit
                                                        </button>
                                                        <span class="separator">|</span>
                                                        <form action="<?= App\Core\View::url('/admin/settings/delete-option') ?>" method="POST" class="d-inline">
                                                            <?= App\Core\View::csrfField() ?>
                                                            <input type="hidden" name="option_type" value="carrier">
                                                            <input type="hidden" name="name" value="<?= App\Core\View::e($carrier['name'] ?? $carrier) ?>">
                                                            <button type="submit" class="btn btn-sm btn-outline-danger" onclick="return confirm('Are you sure you want to delete this item?')">
                                                                <i class="fas fa-trash"></i> Delete
                                                            </button>
                                                        </form>
                                                    </div>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>
                        <div class="mt-3">
                            <nav aria-label="Carriers pagination">
                                <ul class="pagination justify-content-center" id="carriers-pagination">
                                    <!-- Pagination will be added by JavaScript -->
                                </ul>
                            </nav>
                        </div>
                    </div>

                    <!-- Locations Tab -->
                    <div class="tab-pane fade" id="locations" role="tabpanel" aria-labelledby="locations-tab">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h3>Countries & Locations</h3>
                            <div>
                                <div class="input-group">
                                    <input type="text" id="locations-search" class="form-control" placeholder="Search countries...">
                                    <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#edit-locations-modal">
                                        <i class="fas fa-edit"></i> Edit Countries
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-12">
                                <div class="card">
                                    <div class="card-body">
                                        <div class="form-group">
                                            <label for="locations-list">Available Countries</label>
                                            <p class="text-muted">These countries will be available in origin/destination dropdowns in the shipment form.</p>
                                            <div class="location-tags-container p-3 border rounded" style="max-height: 300px; overflow-y: auto;">
                                                <?php if (is_array($locations) && !empty($locations)): ?>
                                                    <?php foreach ($locations as $location): ?>
                                                        <span class="badge bg-light text-dark border m-1 p-2 location-tag"><?= App\Core\View::e($location) ?></span>
                                                    <?php endforeach; ?>
                                                <?php else: ?>
                                                    <p class="text-muted">No countries defined. Add some countries to use in shipments.</p>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Shipment Type Modal -->
<div class="modal fade" id="add-shipment-type-modal" tabindex="-1" aria-labelledby="add-shipment-type-modal-label" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <form action="<?= App\Core\View::url('/admin/settings/add-option') ?>" method="POST">
                <?= App\Core\View::csrfField() ?>
                <input type="hidden" name="option_type" value="shipment_type">
                <div class="modal-header">
                    <h5 class="modal-title" id="add-shipment-type-modal-label">Add New Shipment Type</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="shipment-type-name" class="form-label">Name</label>
                        <input type="text" class="form-control" id="shipment-type-name" name="name" required>
                    </div>
                    <div class="mb-3">
                        <label for="shipment-type-description" class="form-label">Description</label>
                        <textarea class="form-control" id="shipment-type-description" name="description" rows="2"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Save</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Add Shipment Mode Modal -->
<div class="modal fade" id="add-shipment-mode-modal" tabindex="-1" aria-labelledby="add-shipment-mode-modal-label" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <form action="<?= App\Core\View::url('/admin/settings/add-option') ?>" method="POST">
                <?= App\Core\View::csrfField() ?>
                <input type="hidden" name="option_type" value="shipment_mode">
                <div class="modal-header">
                    <h5 class="modal-title" id="add-shipment-mode-modal-label">Add New Shipment Mode</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="shipment-mode-name" class="form-label">Name</label>
                        <input type="text" class="form-control" id="shipment-mode-name" name="name" required>
                    </div>
                    <div class="mb-3">
                        <label for="shipment-mode-description" class="form-label">Description</label>
                        <textarea class="form-control" id="shipment-mode-description" name="description" rows="2"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Save</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Add Payment Mode Modal -->
<div class="modal fade" id="add-payment-mode-modal" tabindex="-1" aria-labelledby="add-payment-mode-modal-label" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <form action="<?= App\Core\View::url('/admin/settings/add-option') ?>" method="POST">
                <?= App\Core\View::csrfField() ?>
                <input type="hidden" name="option_type" value="payment_mode">
                <div class="modal-header">
                    <h5 class="modal-title" id="add-payment-mode-modal-label">Add New Payment Mode</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="payment-mode-name" class="form-label">Name</label>
                        <input type="text" class="form-control" id="payment-mode-name" name="name" required>
                    </div>
                    <div class="mb-3">
                        <label for="payment-mode-description" class="form-label">Description</label>
                        <textarea class="form-control" id="payment-mode-description" name="description" rows="2"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Save</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Add Carrier Modal -->
<div class="modal fade" id="add-carrier-modal" tabindex="-1" aria-labelledby="add-carrier-modal-label" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <form action="<?= App\Core\View::url('/admin/settings/add-option') ?>" method="POST">
                <?= App\Core\View::csrfField() ?>
                <input type="hidden" name="option_type" value="carrier">
                <div class="modal-header">
                    <h5 class="modal-title" id="add-carrier-modal-label">Add New Carrier</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="carrier-name" class="form-label">Name</label>
                        <input type="text" class="form-control" id="carrier-name" name="name" required>
                    </div>
                    <div class="mb-3">
                        <label for="carrier-description" class="form-label">Description</label>
                        <textarea class="form-control" id="carrier-description" name="description" rows="2"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Save</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit Option Modal -->
<div class="modal fade" id="edit-option-modal" tabindex="-1" aria-labelledby="edit-option-modal-label" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <form action="<?= App\Core\View::url('/admin/settings/update-option') ?>" method="POST">
                <?= App\Core\View::csrfField() ?>
                <input type="hidden" id="edit-option-type" name="option_type">
                <input type="hidden" id="edit-option-id" name="id">
                <input type="hidden" id="edit-option-old-name" name="old_name">
                <div class="modal-header">
                    <h5 class="modal-title" id="edit-option-modal-label">Edit Option</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="edit-option-name" class="form-label">Name</label>
                        <input type="text" class="form-control" id="edit-option-name" name="name" required>
                    </div>
                    <div class="mb-3">
                        <label for="edit-option-description" class="form-label">Description</label>
                        <textarea class="form-control" id="edit-option-description" name="description" rows="2"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Update</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit Locations Modal -->
<div class="modal fade" id="edit-locations-modal" tabindex="-1" aria-labelledby="edit-locations-modal-label" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <form action="<?= App\Core\View::url('/admin/settings/update-option') ?>" method="POST">
                <?= App\Core\View::csrfField() ?>
                <input type="hidden" name="option_type" value="locations">
                <div class="modal-header">
                    <h5 class="modal-title" id="edit-locations-modal-label">Edit Countries & Locations</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="locations-textarea" class="form-label">Countries (one per line or comma-separated)</label>
                        <textarea class="form-control" id="locations-textarea" name="locations" rows="10"><?= is_array($locations) ? implode(",\n", $locations) : $locations ?></textarea>
                        <small class="text-muted">Enter each country on a new line or separate with commas. These will be available as origin/destination options.</small>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Quick Add Common Countries</label>
                        <div class="d-flex flex-wrap gap-2">
                            <button type="button" class="btn btn-sm btn-outline-secondary add-common-location" data-location="United States">United States</button>
                            <button type="button" class="btn btn-sm btn-outline-secondary add-common-location" data-location="United Kingdom">United Kingdom</button>
                            <button type="button" class="btn btn-sm btn-outline-secondary add-common-location" data-location="Canada">Canada</button>
                            <button type="button" class="btn btn-sm btn-outline-secondary add-common-location" data-location="Australia">Australia</button>
                            <button type="button" class="btn btn-sm btn-outline-secondary add-common-location" data-location="Germany">Germany</button>
                            <button type="button" class="btn btn-sm btn-outline-secondary add-common-location" data-location="France">France</button>
                            <button type="button" class="btn btn-sm btn-outline-secondary add-common-location" data-location="Japan">Japan</button>
                            <button type="button" class="btn btn-sm btn-outline-secondary add-common-location" data-location="China">China</button>
                        </div>
                    </div>
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="sort-locations-checkbox">
                            <label class="form-check-label" for="sort-locations-checkbox">
                                Sort countries alphabetically when saving
                            </label>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary" id="save-locations-btn">Save Changes</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Handle edit option button clicks
    document.querySelectorAll('.edit-option-btn').forEach(function(button) {
        button.addEventListener('click', function() {
            const optionType = this.getAttribute('data-option-type');
            const id = this.getAttribute('data-id');
            const name = this.getAttribute('data-name');
            const description = this.getAttribute('data-description');

            document.getElementById('edit-option-type').value = optionType;
            document.getElementById('edit-option-id').value = id;
            document.getElementById('edit-option-old-name').value = name;
            document.getElementById('edit-option-name').value = name;
            document.getElementById('edit-option-description').value = description;

            // Update modal title based on option type
            let title = 'Edit Option';
            switch(optionType) {
                case 'shipment_type':
                    title = 'Edit Shipment Type';
                    break;
                case 'shipment_mode':
                    title = 'Edit Shipment Mode';
                    break;
                case 'payment_mode':
                    title = 'Edit Payment Mode';
                    break;
                case 'carrier':
                    title = 'Edit Carrier';
                    break;
            }
            document.getElementById('edit-option-modal-label').textContent = title;

            // Show the modal
            const modal = new bootstrap.Modal(document.getElementById('edit-option-modal'));
            modal.show();
        });
    });

    // Search functionality for each tab
    const searchInputs = {
        'shipment-types': document.getElementById('shipment-types-search'),
        'shipment-modes': document.getElementById('shipment-modes-search'),
        'payment-modes': document.getElementById('payment-modes-search'),
        'carriers': document.getElementById('carriers-search'),
        'locations': document.getElementById('locations-search')
    };

    // Add search functionality to each input
    Object.keys(searchInputs).forEach(function(key) {
        const input = searchInputs[key];
        if (input) {
            input.addEventListener('input', function() {
                const searchTerm = this.value.toLowerCase();
                const container = document.getElementById(key);

                if (key === 'locations') {
                    // For locations, search in the location tags
                    const locationTags = container.querySelectorAll('.location-tag');
                    locationTags.forEach(function(tag) {
                        const text = tag.textContent.toLowerCase();
                        if (text.includes(searchTerm)) {
                            tag.style.display = '';
                        } else {
                            tag.style.display = 'none';
                        }
                    });
                } else {
                    // For tables, search in the rows
                    const rows = container.querySelectorAll('.option-row');
                    rows.forEach(function(row) {
                        const text = row.textContent.toLowerCase();
                        if (text.includes(searchTerm)) {
                            row.style.display = '';
                        } else {
                            row.style.display = 'none';
                        }
                    });
                }
            });
        }
    });

    // Pagination functionality
    function setupPagination(tableId, paginationId, itemsPerPage = 10) {
        const table = document.getElementById(tableId);
        const pagination = document.getElementById(paginationId);

        if (!table || !pagination) return;

        const rows = table.querySelectorAll('.option-row');
        const pageCount = Math.ceil(rows.length / itemsPerPage);

        // Don't show pagination if there's only one page
        if (pageCount <= 1) {
            pagination.style.display = 'none';
            return;
        }

        // Create pagination links
        pagination.innerHTML = '';

        // Previous button
        const prevLi = document.createElement('li');
        prevLi.className = 'page-item';
        const prevLink = document.createElement('a');
        prevLink.className = 'page-link';
        prevLink.href = '#';
        prevLink.textContent = 'Previous';
        prevLi.appendChild(prevLink);
        pagination.appendChild(prevLi);

        // Page numbers
        for (let i = 1; i <= pageCount; i++) {
            const li = document.createElement('li');
            li.className = 'page-item' + (i === 1 ? ' active' : '');
            const link = document.createElement('a');
            link.className = 'page-link';
            link.href = '#';
            link.textContent = i;
            link.addEventListener('click', function(e) {
                e.preventDefault();
                showPage(i);

                // Update active state
                pagination.querySelectorAll('.page-item').forEach(function(item) {
                    item.classList.remove('active');
                });
                li.classList.add('active');
            });
            li.appendChild(link);
            pagination.appendChild(li);
        }

        // Next button
        const nextLi = document.createElement('li');
        nextLi.className = 'page-item';
        const nextLink = document.createElement('a');
        nextLink.className = 'page-link';
        nextLink.href = '#';
        nextLink.textContent = 'Next';
        nextLi.appendChild(nextLink);
        pagination.appendChild(nextLi);

        // Show first page initially
        showPage(1);

        // Previous button functionality
        prevLink.addEventListener('click', function(e) {
            e.preventDefault();
            const activeItem = pagination.querySelector('.page-item.active');
            if (activeItem && activeItem.previousElementSibling && activeItem.previousElementSibling.classList.contains('page-item')) {
                activeItem.previousElementSibling.querySelector('.page-link').click();
            }
        });

        // Next button functionality
        nextLink.addEventListener('click', function(e) {
            e.preventDefault();
            const activeItem = pagination.querySelector('.page-item.active');
            if (activeItem && activeItem.nextElementSibling && activeItem.nextElementSibling.classList.contains('page-item')) {
                activeItem.nextElementSibling.querySelector('.page-link').click();
            }
        });

        function showPage(pageNum) {
            const start = (pageNum - 1) * itemsPerPage;
            const end = start + itemsPerPage;

            rows.forEach(function(row, index) {
                if (index >= start && index < end) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            });
        }
    }

    // Setup pagination for each table
    setupPagination('shipment-types-table', 'shipment-types-pagination');
    setupPagination('shipment-modes-table', 'shipment-modes-pagination');
    setupPagination('payment-modes-table', 'payment-modes-pagination');
    setupPagination('carriers-table', 'carriers-pagination');

    // Quick add common locations
    document.querySelectorAll('.add-common-location').forEach(function(button) {
        button.addEventListener('click', function() {
            const location = this.getAttribute('data-location');
            const textarea = document.getElementById('locations-textarea');
            const currentLocations = textarea.value.split(/[,\n]+/).map(loc => loc.trim()).filter(loc => loc);

            // Only add if not already in the list
            if (!currentLocations.includes(location)) {
                currentLocations.push(location);
                textarea.value = currentLocations.join(',\n');
            }
        });
    });

    // Sort locations
    document.getElementById('save-locations-btn').addEventListener('click', function() {
        if (document.getElementById('sort-locations-checkbox').checked) {
            const textarea = document.getElementById('locations-textarea');
            const locations = textarea.value.split(/[,\n]+/).map(loc => loc.trim()).filter(loc => loc);
            locations.sort();
            textarea.value = locations.join(',\n');
        }
    });
});
</script>
