<?php if (isset($flash_success)): ?>
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <i class="fas fa-check-circle me-2"></i>
        <?= App\Core\View::e($flash_success) ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
<?php endif; ?>

<?php if (isset($flash_error)): ?>
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <i class="fas fa-exclamation-circle me-2"></i>
        <?= App\Core\View::e($flash_error) ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
<?php endif; ?>

<div class="card">
    <div class="card-header">
        <div class="d-flex justify-content-between align-items-center">
            <h2>Manage Status Options</h2>
            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#add-status-modal">
                <i class="fas fa-plus"></i> Add New Status
            </button>
        </div>
    </div>
    <div class="card-body p-0">
        <div class="table-container">
            <table class="table table-striped">
                <thead>
                    <tr>
                        <th>Name</th>
                        <th>Description</th>
                        <th>Color</th>
                        <th>Status</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php if (empty($statusOptions)): ?>
                        <tr>
                            <td colspan="5" class="text-center">No status options found.</td>
                        </tr>
                    <?php else: ?>
                        <?php foreach ($statusOptions as $status): ?>
                            <tr>
                                <td><?= App\Core\View::e($status['name']) ?></td>
                                <td><?= App\Core\View::e($status['description'] ?? '') ?></td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="color-box" style="background-color: <?= App\Core\View::e($status['color']) ?>"></div>
                                        <span class="ms-2"><?= App\Core\View::e($status['color']) ?></span>
                                    </div>
                                </td>
                                <td>
                                    <?php if ($status['is_active']): ?>
                                        <span class="badge bg-success">Active</span>
                                    <?php else: ?>
                                        <span class="badge bg-danger">Inactive</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <div class="d-flex gap-2">
                                        <button type="button" class="btn btn-sm btn-outline-primary edit-status-btn"
                                            data-id="<?= $status['id'] ?>"
                                            data-name="<?= App\Core\View::e($status['name']) ?>"
                                            data-description="<?= App\Core\View::e($status['description'] ?? '') ?>"
                                            data-color="<?= App\Core\View::e($status['color']) ?>"
                                            data-is-active="<?= $status['is_active'] ? '1' : '0' ?>">
                                            <i class="fas fa-edit me-1"></i> Edit
                                        </button>
                                        <form action="<?= App\Core\View::url('/admin/settings/toggle-status/' . $status['id']) ?>" method="POST" class="d-inline">
                                            <?= App\Core\View::csrfField() ?>
                                            <?php if ($status['is_active']): ?>
                                                <button type="submit" class="btn btn-sm btn-outline-warning" title="Deactivate Status">
                                                    <i class="fas fa-ban me-1"></i> Deactivate
                                                </button>
                                            <?php else: ?>
                                                <button type="submit" class="btn btn-sm btn-outline-success" title="Activate Status">
                                                    <i class="fas fa-check me-1"></i> Activate
                                                </button>
                                            <?php endif; ?>
                                        </form>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Add Status Modal -->
<div class="modal fade" id="add-status-modal" tabindex="-1" aria-labelledby="add-status-modal-label" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <form id="add-status-form" action="<?= App\Core\View::url('/admin/settings/add-status') ?>" method="POST">
                <?= App\Core\View::csrfField() ?>
                <div class="modal-header">
                    <h5 class="modal-title" id="add-status-modal-label">Add New Status</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="name" class="form-label">Status Name</label>
                        <input type="text" class="form-control" id="name" name="name" required>
                        <small class="form-text text-muted">Use lowercase with underscores for spaces (e.g., in_progress)</small>
                    </div>
                    <div class="mb-3">
                        <label for="description" class="form-label">Description</label>
                        <textarea class="form-control" id="description" name="description" rows="2"></textarea>
                    </div>
                    <div class="mb-3">
                        <label for="color" class="form-label">Color</label>
                        <input type="color" class="form-control" id="color" name="color" value="#5bc0de">
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Save Status</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit Status Modal -->
<div class="modal fade" id="edit-status-modal" tabindex="-1" aria-labelledby="edit-status-modal-label" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <form id="edit-status-form" action="<?= App\Core\View::url('/admin/settings/update-status') ?>" method="POST">
                <?= App\Core\View::csrfField() ?>
                <input type="hidden" id="edit-status-id" name="id">
                <div class="modal-header">
                    <h5 class="modal-title" id="edit-status-modal-label">Edit Status</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="edit-name" class="form-label">Status Name</label>
                        <input type="text" class="form-control" id="edit-name" name="name" required>
                        <small class="form-text text-muted">Use lowercase with underscores for spaces (e.g., in_progress)</small>
                    </div>
                    <div class="mb-3">
                        <label for="edit-description" class="form-label">Description</label>
                        <textarea class="form-control" id="edit-description" name="description" rows="2"></textarea>
                    </div>
                    <div class="mb-3">
                        <label for="edit-color" class="form-label">Color</label>
                        <input type="color" class="form-control" id="edit-color" name="color">
                    </div>
                    <div class="mb-3 form-check">
                        <input type="checkbox" class="form-check-input" id="edit-is-active" name="is_active" value="1">
                        <label class="form-check-label" for="edit-is-active">Active</label>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Update Status</button>
                </div>
            </form>
        </div>
    </div>
</div>

<style>
.color-box {
    width: 20px;
    height: 20px;
    border-radius: 4px;
    border: 1px solid #ddd;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Handle edit status button clicks
    document.querySelectorAll('.edit-status-btn').forEach(function(button) {
        button.addEventListener('click', function() {
            const id = this.getAttribute('data-id');
            const name = this.getAttribute('data-name');
            const description = this.getAttribute('data-description');
            const color = this.getAttribute('data-color');
            const isActive = this.getAttribute('data-is-active') === '1';

            document.getElementById('edit-status-id').value = id;
            document.getElementById('edit-name').value = name;
            document.getElementById('edit-description').value = description;
            document.getElementById('edit-color').value = color;
            document.getElementById('edit-is-active').checked = isActive;

            // Show the modal
            const modal = new bootstrap.Modal(document.getElementById('edit-status-modal'));
            modal.show();
        });
    });

    // Auto-close alerts after 5 seconds
    setTimeout(function() {
        const alerts = document.querySelectorAll('.alert');
        alerts.forEach(function(alert) {
            const bsAlert = new bootstrap.Alert(alert);
            bsAlert.close();
        });
    }, 5000);

    // Handle form submissions with AJAX
    const addStatusForm = document.querySelector('#add-status-form');
    if (addStatusForm) {
        addStatusForm.addEventListener('submit', function(e) {
            // Don't prevent default - let the form submit normally
            // This is just to close the modal after submission
            const modal = bootstrap.Modal.getInstance(document.getElementById('add-status-modal'));
            if (modal) {
                modal.hide();
            }
        });
    }

    // Handle edit form submissions
    const editStatusForm = document.querySelector('#edit-status-form');
    if (editStatusForm) {
        editStatusForm.addEventListener('submit', function(e) {
            // Don't prevent default - let the form submit normally
            // This is just to close the modal after submission
            const modal = bootstrap.Modal.getInstance(document.getElementById('edit-status-modal'));
            if (modal) {
                modal.hide();
            }
        });
    }

    // If there's a success message in the URL, show it
    const urlParams = new URLSearchParams(window.location.search);
    if (urlParams.has('success')) {
        const successMessage = urlParams.get('success');
        if (successMessage) {
            // Create a success alert
            const alertDiv = document.createElement('div');
            alertDiv.className = 'alert alert-success alert-dismissible fade show';
            alertDiv.setAttribute('role', 'alert');
            alertDiv.innerHTML = `
                <i class="fas fa-check-circle me-2"></i>
                ${decodeURIComponent(successMessage)}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            `;

            // Insert at the top of the page
            document.body.insertBefore(alertDiv, document.body.firstChild);

            // Auto-close after 5 seconds
            setTimeout(function() {
                const bsAlert = new bootstrap.Alert(alertDiv);
                bsAlert.close();
            }, 5000);

            // Remove the success parameter from the URL
            const newUrl = window.location.pathname;
            window.history.replaceState({}, document.title, newUrl);
        }
    }
});
</script>
