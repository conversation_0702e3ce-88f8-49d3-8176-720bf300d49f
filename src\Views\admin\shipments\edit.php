<?php
/**
 * Edit Shipment view
 *
 * This file includes partial views for each section of the form to improve maintainability.
 */

// Debug shipment data
if (isset($shipment)) {
    error_log('Edit view received shipment data: ' . json_encode($shipment));
    error_log('Edit view shipment data structure: ' . print_r($shipment, true));

    // Check for specific fields
    $keysToCheck = ['id', 'tracking_number', 'client_id', 'shipper_name', 'receiver_name', 'shipment_type', 'shipment_mode', 'carrier', 'payment_mode'];
    foreach ($keysToCheck as $key) {
        if (isset($shipment[$key])) {
            error_log("Edit view: {$key} = {$shipment[$key]}");
        } else {
            error_log("Edit view: {$key} is missing");
        }
    }
} else {
    error_log('Edit view: No shipment data received');
}

// Include form header (contains helper functions, form start, and error messages)
include __DIR__ . '/partials/_form_header.php';

// Include tracking section first (contains tracking number and status)
include __DIR__ . '/partials/_tracking_section.php';

// Include shipper information section
include __DIR__ . '/partials/_shipper_info.php';

// Include receiver information section
include __DIR__ . '/partials/_receiver_info.php';

// Include package details section
include __DIR__ . '/partials/_package_details.php';

// Include form footer (contains right column with shipment details, dates & financials, comments, and form actions)
include __DIR__ . '/partials/_form_footer.php';
?>
