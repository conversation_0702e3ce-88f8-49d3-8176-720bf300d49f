<?php
$pageTitle = 'Generate Shipping Label';
$breadcrumbs = [
    ['title' => 'Dashboard', 'url' => '/admin/dashboard'],
    ['title' => 'Shipments', 'url' => '/admin/shipments'],
    ['title' => 'Generate Label', 'url' => '']
];
?>

<div class="page-header">
    <div class="page-header-content">
        <h1 class="page-title">
            <i class="fas fa-tag"></i>
            Generate Shipping Label
        </h1>
        <p class="page-description">Generate professional shipping labels for shipment #<?= htmlspecialchars($shipment['tracking_number']) ?></p>
    </div>
    <div class="page-actions">
        <a href="<?= App\Core\View::url('/admin/shipments') ?>" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i>
            Back to Shipments
        </a>
    </div>
</div>

<div class="content-wrapper">
    <div class="row">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-cog"></i>
                        Label Configuration
                    </h3>
                </div>
                <div class="card-body">
                    <form id="labelForm">
                        <?= App\Core\View::csrfField() ?>
                        <input type="hidden" name="shipment_id" value="<?= $shipment['id'] ?>">
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="label_type">Label Type</label>
                                    <select class="form-control" id="label_type" name="label_type" required>
                                        <option value="">Select label type</option>
                                        <option value="standard">Standard Label (4x6)</option>
                                        <option value="thermal">Thermal Label (4x6)</option>
                                        <option value="letter">Letter Size (8.5x11)</option>
                                        <option value="a4">A4 Size</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="label_format">Format</label>
                                    <select class="form-control" id="label_format" name="label_format" required>
                                        <option value="">Select format</option>
                                        <option value="pdf">PDF</option>
                                        <option value="png">PNG Image</option>
                                        <option value="zpl">ZPL (Zebra)</option>
                                        <option value="epl">EPL (Eltron)</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label>
                                <input type="checkbox" id="include_barcode" name="include_barcode" checked>
                                Include Barcode
                            </label>
                        </div>
                        
                        <div class="form-group">
                            <label>
                                <input type="checkbox" id="include_qr" name="include_qr" checked>
                                Include QR Code
                            </label>
                        </div>
                        
                        <div class="form-group">
                            <label>
                                <input type="checkbox" id="include_logo" name="include_logo" checked>
                                Include Company Logo
                            </label>
                        </div>
                        
                        <div class="form-group">
                            <label for="special_instructions">Special Instructions</label>
                            <textarea class="form-control" id="special_instructions" name="special_instructions" rows="3" 
                                      placeholder="Any special handling instructions for the label"></textarea>
                        </div>
                        
                        <div class="form-actions">
                            <button type="button" class="btn btn-primary" onclick="generateLabel()">
                                <i class="fas fa-tag"></i>
                                Generate Label
                            </button>
                            <button type="button" class="btn btn-success" onclick="generateAndDownload()">
                                <i class="fas fa-download"></i>
                                Generate & Download
                            </button>
                            <button type="button" class="btn btn-info" onclick="printLabel()">
                                <i class="fas fa-print"></i>
                                Generate & Print
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-info-circle"></i>
                        Shipment Details
                    </h3>
                </div>
                <div class="card-body">
                    <div class="shipment-info">
                        <div class="info-row">
                            <strong>Tracking #:</strong>
                            <span><?= htmlspecialchars($shipment['tracking_number']) ?></span>
                        </div>
                        <div class="info-row">
                            <strong>From:</strong>
                            <span><?= htmlspecialchars($shipment['shipper_name']) ?></span>
                            <small class="d-block text-muted"><?= htmlspecialchars($shipment['shipper_address']) ?></small>
                        </div>
                        <div class="info-row">
                            <strong>To:</strong>
                            <span><?= htmlspecialchars($shipment['receiver_name']) ?></span>
                            <small class="d-block text-muted"><?= htmlspecialchars($shipment['receiver_address']) ?></small>
                        </div>
                        <div class="info-row">
                            <strong>Service:</strong>
                            <span><?= htmlspecialchars($shipment['shipment_type'] ?? 'Standard') ?></span>
                        </div>
                        <div class="info-row">
                            <strong>Weight:</strong>
                            <span><?= htmlspecialchars($shipment['weight'] ?? '0') ?> kg</span>
                        </div>
                        <div class="info-row">
                            <strong>Status:</strong>
                            <span class="badge badge-<?= getStatusColor($shipment['status']) ?>">
                                <?= htmlspecialchars(ucfirst(str_replace('_', ' ', $shipment['status']))) ?>
                            </span>
                        </div>
                    </div>
                    
                    <?php if (!empty($packages)): ?>
                    <div class="packages-info mt-3">
                        <h6><strong>Packages (<?= count($packages) ?>):</strong></h6>
                        <?php foreach ($packages as $package): ?>
                        <div class="package-item">
                            <small>
                                <strong><?= htmlspecialchars($package['piece_type']) ?></strong>
                                <?php if ($package['quantity'] > 1): ?>
                                    (Qty: <?= $package['quantity'] ?>)
                                <?php endif; ?>
                                <br>
                                <?= htmlspecialchars($package['description']) ?>
                                <br>
                                <span class="text-muted">
                                    <?= $package['length'] ?>×<?= $package['width'] ?>×<?= $package['height'] ?> cm, 
                                    <?= $package['weight'] ?> kg
                                </span>
                            </small>
                        </div>
                        <?php endforeach; ?>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
            
            <div class="card mt-3">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-history"></i>
                        Recent Labels
                    </h3>
                </div>
                <div class="card-body">
                    <div id="recent_labels">
                        <p class="text-muted">No recent labels generated</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Label Preview Modal -->
<div id="labelPreviewModal" class="modal fade" tabindex="-1" role="dialog" style="z-index: 99999;">
    <div class="modal-dialog" role="document" style="max-width: 95vw; width: 1000px; margin: 1rem auto;">
        <div class="modal-content" style="height: 95vh; display: flex; flex-direction: column;">
            <div class="modal-header" style="flex-shrink: 0; background: #f8f9fa; border-bottom: 2px solid #e9ecef;">
                <h5 class="modal-title" style="color: #2c3e50; font-weight: 600;">
                    <i class="fas fa-tag"></i> Label Preview - Full Size
                </h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close" style="font-size: 1.5rem; opacity: 0.8;">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body" style="flex: 1; overflow-y: auto; padding: 0; background: #f5f5f5;">
                <div id="label_preview_content" style="display: flex; justify-content: center; align-items: flex-start; min-height: 100%; padding: 20px;">
                    <!-- Label preview will be loaded here -->
                </div>
            </div>
            <div class="modal-footer" style="flex-shrink: 0; background: #f8f9fa; border-top: 2px solid #e9ecef; padding: 15px;">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">
                    <i class="fas fa-times"></i> Close
                </button>
                <button type="button" class="btn btn-primary" onclick="downloadLabel()">
                    <i class="fas fa-download"></i> Download PDF
                </button>
                <button type="button" class="btn btn-info" onclick="printLabelFromPreview()">
                    <i class="fas fa-print"></i> Print Label
                </button>
            </div>
        </div>
    </div>
</div>

<style>
.page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 1px solid #e9ecef;
}

.page-header-content h1 {
    margin: 0;
    color: var(--primary-color);
}

.page-header-content p {
    margin: 5px 0 0 0;
    color: #6c757d;
}

.card {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    border: none;
    margin-bottom: 20px;
}

.card-header {
    background: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
    padding: 15px 20px;
    border-radius: 8px 8px 0 0;
}

.card-title {
    margin: 0;
    color: var(--text-color);
    font-size: 16px;
    font-weight: 600;
}

.card-body {
    padding: 20px;
}

.form-actions {
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid #e9ecef;
}

.shipment-info .info-row {
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #f0f0f0;
}

.shipment-info .info-row:last-child {
    border-bottom: none;
    margin-bottom: 0;
    padding-bottom: 0;
}

.packages-info {
    border-top: 1px solid #e9ecef;
    padding-top: 15px;
}

.package-item {
    background: #f8f9fa;
    padding: 10px;
    border-radius: 4px;
    margin-bottom: 8px;
}

.package-item:last-child {
    margin-bottom: 0;
}

.badge {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 500;
    text-transform: uppercase;
}

.badge-warning { background: #fff3cd; color: #856404; }
.badge-info { background: #d1ecf1; color: #0c5460; }
.badge-success { background: #d4edda; color: #155724; }
.badge-danger { background: #f8d7da; color: #721c24; }
.badge-secondary { background: #e2e3e5; color: #383d41; }

#label_preview_content {
    text-align: center;
    width: 100%;
}

#label_preview_content .shipping-label {
    margin: 0 auto;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    border-radius: 8px;
    overflow: hidden;
}

/* Optimize modal backdrop */
.modal-backdrop {
    background-color: rgba(0, 0, 0, 0.7);
}

/* Ensure modal is properly centered and sized */
.modal.show {
    display: flex !important;
    align-items: center;
    justify-content: center;
}

.btn-group {
    margin-right: 10px;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    loadRecentLabels();

    // Initialize modal functionality
    initializeModalHandlers();
});

function initializeModalHandlers() {
    // Close modal handlers
    const closeBtns = document.querySelectorAll('[data-dismiss="modal"]');
    closeBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const modal = this.closest('.modal');
            if (modal) {
                modal.style.display = 'none';
                modal.classList.remove('show');
                document.body.classList.remove('modal-open');
                // Remove backdrop if exists
                const backdrop = document.querySelector('.modal-backdrop');
                if (backdrop) {
                    backdrop.remove();
                }
            }
        });
    });

    // Close modal when clicking outside
    window.addEventListener('click', function(event) {
        if (event.target.classList.contains('modal')) {
            event.target.style.display = 'none';
            event.target.classList.remove('show');
            document.body.classList.remove('modal-open');
            // Remove backdrop if exists
            const backdrop = document.querySelector('.modal-backdrop');
            if (backdrop) {
                backdrop.remove();
            }
        }
    });
}

function generateLabel() {
    const formData = new FormData(document.getElementById('labelForm'));

    fetch('<?= App\Core\View::url('/api/admin/labels/generate.php') ?>', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            document.getElementById('label_preview_content').innerHTML = data.html;
            showModal('labelPreviewModal');
        } else {
            alert('Error generating label: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error generating label:', error);
        alert('An error occurred while generating the label.');
    });
}

function showModal(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) {
        modal.style.display = 'block';
        modal.classList.add('show');
        document.body.classList.add('modal-open');

        // Add backdrop
        const backdrop = document.createElement('div');
        backdrop.className = 'modal-backdrop fade show';
        document.body.appendChild(backdrop);
    }
}

function generateAndDownload() {
    const formData = new FormData(document.getElementById('labelForm'));
    formData.append('download', '1');
    
    fetch('<?= App\Core\View::url('/api/admin/labels/generate.php') ?>', {
        method: 'POST',
        body: formData
    })
    .then(response => response.blob())
    .then(blob => {
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.style.display = 'none';
        a.href = url;
        a.download = `label-<?= $shipment['tracking_number'] ?>.pdf`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
    })
    .catch(error => {
        console.error('Error downloading label:', error);
        alert('An error occurred while downloading the label.');
    });
}

function printLabel() {
    generateLabel();
    // After preview loads, trigger print
    setTimeout(() => {
        printLabelFromPreview();
    }, 1000);
}

function downloadLabel() {
    generateAndDownload();
}

function printLabelFromPreview() {
    const printContent = document.getElementById('label_preview_content').innerHTML;
    const printWindow = window.open('', '_blank', 'width=800,height=900');

    printWindow.document.write(`
        <html>
            <head>
                <title>Shipping Label - <?= $shipment['tracking_number'] ?></title>
                <style>
                    body {
                        margin: 0;
                        padding: 20px;
                        font-family: Arial, sans-serif;
                        background: white;
                    }

                    @media print {
                        body { margin: 0; padding: 10px; }
                        .shipping-label {
                            page-break-inside: avoid;
                            margin: 0 auto;
                            width: 100% !important;
                            max-width: none !important;
                        }
                    }

                    .shipping-label {
                        border: 2px solid #000 !important;
                        background: white !important;
                        font-family: Arial, sans-serif !important;
                    }

                    /* Ensure all text is visible */
                    * {
                        color: #000 !important;
                        background-color: transparent !important;
                    }

                    /* Preserve specific backgrounds for sections */
                    .section-title {
                        background-color: #333 !important;
                        color: white !important;
                    }

                    .label-header {
                        background-color: #f8f9fa !important;
                    }
                </style>
            </head>
            <body>
                <div style="text-align: center; margin-bottom: 20px;">
                    <h2>ELTA COURIER - Shipping Label</h2>
                    <p>Tracking: <?= $shipment['tracking_number'] ?> | Generated: ${new Date().toLocaleString()}</p>
                </div>
                ${printContent}
                <div style="text-align: center; margin-top: 20px; font-size: 12px; color: #666;">
                    <p>This label was generated by ELTA Courier System</p>
                </div>
            </body>
        </html>
    `);

    printWindow.document.close();

    // Wait for content to load then print
    printWindow.onload = function() {
        setTimeout(() => {
            printWindow.print();
            printWindow.close();
        }, 500);
    };
}

function loadRecentLabels() {
    fetch('<?= App\Core\View::url('/api/admin/labels/recent.php') ?>?shipment_id=<?= $shipment['id'] ?>')
        .then(response => response.json())
        .then(data => {
            if (data.success && data.labels.length > 0) {
                const container = document.getElementById('recent_labels');
                container.innerHTML = '';
                
                data.labels.forEach(label => {
                    const item = document.createElement('div');
                    item.className = 'recent-label-item';
                    item.innerHTML = `
                        <small>
                            <strong>${label.label_type}</strong> (${label.format})<br>
                            ${label.created_at}
                        </small>
                    `;
                    container.appendChild(item);
                });
            }
        })
        .catch(error => {
            console.error('Error loading recent labels:', error);
        });
}

<?php
function getStatusColor($status) {
    $colors = [
        'pending' => 'warning',
        'in_transit' => 'info',
        'delivered' => 'success',
        'delayed' => 'danger',
        'cancelled' => 'secondary'
    ];
    return $colors[$status] ?? 'secondary';
}
?>
</script>

<!-- Include JsBarcode library for barcode generation -->
<script src="https://cdn.jsdelivr.net/npm/jsbarcode@3.11.5/dist/JsBarcode.all.min.js"></script>
