<!-- Dates & Financials -->
<div class="card shadow-sm mb-4">
    <div class="card-header bg-light py-3">
        <h5 class="mb-0 fw-bold"><i class="fas fa-calendar-alt me-2"></i>Dates & Financials</h5>
    </div>
    <div class="card-body">
        <div class="mb-3">
            <label for="pickup_date" class="form-label">Pickup Date</label>
            <input type="date" id="pickup_date" name="pickup_date" class="form-control" value="<?= get_form_value('pickup_date', date('Y-m-d')) ?>">
        </div>
        <div class="mb-3">
            <label for="pickup_time" class="form-label">Pickup Time</label>
            <input type="time" id="pickup_time" name="pickup_time" class="form-control" value="<?= get_form_value('pickup_time') ?>">
        </div>
        <div class="mb-3">
            <label for="expected_delivery_date" class="form-label">Expected Delivery Date</label>
            <input type="date" id="expected_delivery_date" name="expected_delivery_date" class="form-control" value="<?= get_form_value('expected_delivery_date') ?>">
        </div>
        <hr>
        <div class="mb-3">
            <label for="payment_mode" class="form-label">Payment Mode</label>
            <select id="payment_mode" name="payment_mode" class="form-select">
                <option value="">-- Select Payment Mode --</option>
                <?php
                $selectedPaymentMode = get_form_value('payment_mode');
                error_log("Selected payment_mode: {$selectedPaymentMode}");
                if (isset($paymentModes)): foreach ($paymentModes as $mode):
                    // Extract name from array if it's an array
                    $modeName = is_array($mode) ? $mode['name'] : $mode;
                    $isSelected = (string)$selectedPaymentMode === (string)$modeName;
                    error_log("Checking payment mode {$modeName}: " . ($isSelected ? 'selected' : 'not selected'));
                ?>
                    <option value="<?= App\Core\View::e($modeName) ?>" <?= $isSelected ? 'selected' : '' ?>><?= App\Core\View::e($modeName) ?></option>
                <?php endforeach; endif; ?>
            </select>
        </div>
        <div class="mb-3">
            <label for="total_freight" class="form-label">Total Freight Cost</label>
            <div class="input-group">
                <span class="input-group-text">$</span>
                <input type="number" step="0.01" min="0" id="total_freight" name="total_freight" class="form-control" value="<?= get_form_value('total_freight', '0.00') ?>" placeholder="0.00">
            </div>
        </div>
        <div class="mb-3">
            <label for="total_weight" class="form-label">Total Weight (kg)</label>
            <input type="number" step="0.01" id="total_weight" name="total_weight" class="form-control" value="<?= get_form_value('total_weight') ?>" readonly>
            <small class="text-muted">Calculated from packages</small>
        </div>
    </div>
</div>
