<?php
/**
 * Edit Form Partial - Used inside the modal
 */
?>
<form action="<?= App\Core\View::url('/admin/shipments/update/' . $shipment['id']) ?>" method="POST" id="editShipmentForm">
    <?= App\Core\View::csrfField() ?>
    <input type="hidden" name="id" value="<?= $shipment['id'] ?>">

    <div class="row">
        <!-- Left Column -->
        <div class="col-md-6">
            <!-- Tracking Information -->
            <div class="card shadow-sm mb-3">
                <div class="card-header bg-light">
                    <h5 class="mb-0"><i class="fas fa-barcode me-2"></i>Tracking Information</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label for="tracking_number" class="form-label">Tracking Number</label>
                        <input type="text" id="tracking_number" name="tracking_number" class="form-control"
                               value="<?= App\Core\View::e($shipment['tracking_number']) ?>" required>
                    </div>

                    <div class="mb-3">
                        <label for="status" class="form-label">Status</label>
                        <select id="status" name="status" class="form-select" required>
                            <?php
                            // Use status options from database if available
                            if (isset($statusOptions) && !empty($statusOptions)):
                                foreach ($statusOptions as $value => $label):
                                    if (is_array($label) && isset($label['name'])):
                                        $optionValue = $label['name'];
                                        $optionLabel = ucfirst(str_replace('_', ' ', $label['name']));
                                    else:
                                        $optionValue = $value;
                                        $optionLabel = ucfirst(str_replace('_', ' ', $label));
                                    endif;
                                    $selected = (string)$shipment['status'] === (string)$optionValue ? 'selected' : '';
                            ?>
                                <option value="<?= $optionValue ?>" <?= $selected ?>>
                                    <?= App\Core\View::e($optionLabel) ?>
                                </option>
                            <?php
                                endforeach;
                            else:
                                // Fallback to hardcoded statuses
                                $statuses = ['pending' => 'Pending', 'in_transit' => 'In Transit', 'out_for_delivery' => 'Out for Delivery', 'delivered' => 'Delivered', 'delayed' => 'Delayed', 'cancelled' => 'Cancelled'];
                                foreach ($statuses as $value => $label):
                                    $selected = (string)$shipment['status'] === (string)$value ? 'selected' : '';
                            ?>
                                <option value="<?= $value ?>" <?= $selected ?>>
                                    <?= App\Core\View::e($label) ?>
                                </option>
                            <?php
                                endforeach;
                            endif;
                            ?>
                        </select>
                    </div>
                </div>
            </div>

            <!-- Shipper Information -->
            <div class="card shadow-sm mb-3">
                <div class="card-header bg-light">
                    <h5 class="mb-0"><i class="fas fa-user-tie me-2"></i>Shipper Information</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label for="shipper_name" class="form-label">Shipper Name</label>
                        <input type="text" id="shipper_name" name="shipper_name" class="form-control"
                               value="<?= App\Core\View::e($shipment['shipper_name']) ?>" required>
                    </div>

                    <div class="mb-3">
                        <label for="shipper_phone" class="form-label">Shipper Phone</label>
                        <input type="text" id="shipper_phone" name="shipper_phone" class="form-control"
                               value="<?= App\Core\View::e($shipment['shipper_phone']) ?>" required>
                    </div>

                    <div class="mb-3">
                        <label for="shipper_email" class="form-label">Shipper Email</label>
                        <input type="email" id="shipper_email" name="shipper_email" class="form-control"
                               value="<?= App\Core\View::e($shipment['shipper_email']) ?>">
                    </div>

                    <div class="mb-3">
                        <label for="shipper_address" class="form-label">Shipper Address</label>
                        <textarea id="shipper_address" name="shipper_address" class="form-control" rows="3" required><?= App\Core\View::e($shipment['shipper_address']) ?></textarea>
                    </div>
                </div>
            </div>
        </div>

        <!-- Right Column -->
        <div class="col-md-6">
            <!-- Receiver Information -->
            <div class="card shadow-sm mb-3">
                <div class="card-header bg-light">
                    <h5 class="mb-0"><i class="fas fa-user-tag me-2"></i>Receiver Information</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label for="receiver_name" class="form-label">Receiver Name</label>
                        <input type="text" id="receiver_name" name="receiver_name" class="form-control"
                               value="<?= App\Core\View::e($shipment['receiver_name']) ?>" required>
                    </div>

                    <div class="mb-3">
                        <label for="receiver_phone" class="form-label">Receiver Phone</label>
                        <input type="text" id="receiver_phone" name="receiver_phone" class="form-control"
                               value="<?= App\Core\View::e($shipment['receiver_phone']) ?>" required>
                    </div>

                    <div class="mb-3">
                        <label for="receiver_email" class="form-label">Receiver Email</label>
                        <input type="email" id="receiver_email" name="receiver_email" class="form-control"
                               value="<?= App\Core\View::e($shipment['receiver_email']) ?>">
                    </div>

                    <div class="mb-3">
                        <label for="receiver_address" class="form-label">Receiver Address</label>
                        <textarea id="receiver_address" name="receiver_address" class="form-control" rows="3" required><?= App\Core\View::e($shipment['receiver_address']) ?></textarea>
                    </div>
                </div>
            </div>

            <!-- Shipment Details -->
            <div class="card shadow-sm mb-3">
                <div class="card-header bg-light">
                    <h5 class="mb-0"><i class="fas fa-shipping-fast me-2"></i>Shipment Details</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="origin" class="form-label">Origin</label>
                            <input type="text" id="origin" name="origin" class="form-control"
                                   value="<?= App\Core\View::e($shipment['origin']) ?>" required>
                        </div>

                        <div class="col-md-6 mb-3">
                            <label for="destination" class="form-label">Destination</label>
                            <input type="text" id="destination" name="destination" class="form-control"
                                   value="<?= App\Core\View::e($shipment['destination']) ?>" required>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="shipment_type" class="form-label">Shipment Type</label>
                            <select id="shipment_type" name="shipment_type" class="form-select">
                                <option value="">-- Select Type --</option>
                                <?php foreach ($shipmentTypes as $type):
                                    // Extract name from array if it's an array
                                    $typeName = is_array($type) ? $type['name'] : $type;
                                    $selected = (string)$shipment['shipment_type'] === (string)$typeName ? 'selected' : '';
                                ?>
                                <option value="<?= App\Core\View::e($typeName) ?>" <?= $selected ?>>
                                    <?= App\Core\View::e($typeName) ?>
                                </option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <div class="col-md-6 mb-3">
                            <label for="shipment_mode" class="form-label">Shipment Mode</label>
                            <select id="shipment_mode" name="shipment_mode" class="form-select">
                                <option value="">-- Select Mode --</option>
                                <?php foreach ($shipmentModes as $mode):
                                    // Extract name from array if it's an array
                                    $modeName = is_array($mode) ? $mode['name'] : $mode;
                                    $selected = (string)$shipment['shipment_mode'] === (string)$modeName ? 'selected' : '';
                                ?>
                                <option value="<?= App\Core\View::e($modeName) ?>" <?= $selected ?>>
                                    <?= App\Core\View::e($modeName) ?>
                                </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="carrier" class="form-label">Carrier</label>
                            <select id="carrier" name="carrier" class="form-select">
                                <option value="">-- Select Carrier --</option>
                                <?php foreach ($carriers as $carrier):
                                    // Extract name from array if it's an array
                                    $carrierName = is_array($carrier) ? $carrier['name'] : $carrier;
                                    $selected = (string)$shipment['carrier'] === (string)$carrierName ? 'selected' : '';
                                ?>
                                <option value="<?= App\Core\View::e($carrierName) ?>" <?= $selected ?>>
                                    <?= App\Core\View::e($carrierName) ?>
                                </option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <div class="col-md-6 mb-3">
                            <label for="payment_mode" class="form-label">Payment Mode</label>
                            <select id="payment_mode" name="payment_mode" class="form-select">
                                <option value="">-- Select Payment Mode --</option>
                                <?php foreach ($paymentModes as $mode):
                                    // Extract name from array if it's an array
                                    $modeName = is_array($mode) ? $mode['name'] : $mode;
                                    $selected = (string)$shipment['payment_mode'] === (string)$modeName ? 'selected' : '';
                                ?>
                                <option value="<?= App\Core\View::e($modeName) ?>" <?= $selected ?>>
                                    <?= App\Core\View::e($modeName) ?>
                                </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="comments" class="form-label">Comments</label>
                        <textarea id="comments" name="comments" class="form-control" rows="3"><?= App\Core\View::e($shipment['comments']) ?></textarea>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Package Details -->
    <div class="card shadow-sm mb-3">
        <div class="card-header bg-light d-flex justify-content-between align-items-center">
            <h5 class="mb-0"><i class="fas fa-box-open me-2"></i>Package Details</h5>
            <button type="button" class="btn btn-sm btn-success" id="addPackageBtn">
                <i class="fas fa-plus me-1"></i> Add Package
            </button>
        </div>
        <div class="card-body">
            <div id="packagesContainer">
                <?php
                $packages = $shipment['packages'] ?? [];
                $packageIndex = 0;

                if (empty($packages)) {
                    // Add one empty package row if none exist
                    $packages = [[]];
                }

                foreach ($packages as $package):
                    $packageId = $package['id'] ?? '';
                ?>
                <div class="package-row border rounded p-3 mb-3" data-index="<?= $packageIndex ?>">
                    <?php if ($packageId): ?>
                        <input type="hidden" name="packages[<?= $packageIndex ?>][id]" value="<?= $packageId ?>">
                    <?php endif; ?>

                    <div class="row g-2 align-items-center">
                        <div class="col-md-2">
                            <label for="packages_<?= $packageIndex ?>_piece_type" class="form-label small mb-1">Type</label>
                            <input type="text" id="packages_<?= $packageIndex ?>_piece_type"
                                   name="packages[<?= $packageIndex ?>][piece_type]"
                                   class="form-control form-control-sm"
                                   value="<?= App\Core\View::e($package['piece_type'] ?? 'Box') ?>">
                        </div>

                        <div class="col-md-3">
                            <label for="packages_<?= $packageIndex ?>_description" class="form-label small mb-1">Description</label>
                            <input type="text" id="packages_<?= $packageIndex ?>_description"
                                   name="packages[<?= $packageIndex ?>][description]"
                                   class="form-control form-control-sm"
                                   value="<?= App\Core\View::e($package['description'] ?? '') ?>">
                        </div>

                        <div class="col-md-1">
                            <label for="packages_<?= $packageIndex ?>_quantity" class="form-label small mb-1">Qty</label>
                            <input type="number" id="packages_<?= $packageIndex ?>_quantity"
                                   name="packages[<?= $packageIndex ?>][quantity]"
                                   class="form-control form-control-sm package-quantity"
                                   value="<?= App\Core\View::e($package['quantity'] ?? 1) ?>" min="1">
                        </div>

                        <div class="col-md-1">
                            <label for="packages_<?= $packageIndex ?>_weight" class="form-label small mb-1">Wt (kg)</label>
                            <input type="number" step="0.01" id="packages_<?= $packageIndex ?>_weight"
                                   name="packages[<?= $packageIndex ?>][weight]"
                                   class="form-control form-control-sm package-weight"
                                   value="<?= App\Core\View::e($package['weight'] ?? '') ?>" min="0">
                        </div>

                        <div class="col-md-4">
                            <label class="form-label small mb-1">Dims (LxWxH cm)</label>
                            <div class="input-group input-group-sm">
                                <input type="number" step="0.1" name="packages[<?= $packageIndex ?>][length]"
                                       class="form-control package-dim" placeholder="L"
                                       value="<?= App\Core\View::e($package['length'] ?? '') ?>" min="0">
                                <input type="number" step="0.1" name="packages[<?= $packageIndex ?>][width]"
                                       class="form-control package-dim" placeholder="W"
                                       value="<?= App\Core\View::e($package['width'] ?? '') ?>" min="0">
                                <input type="number" step="0.1" name="packages[<?= $packageIndex ?>][height]"
                                       class="form-control package-dim" placeholder="H"
                                       value="<?= App\Core\View::e($package['height'] ?? '') ?>" min="0">
                            </div>
                        </div>

                        <div class="col-md-1 text-end">
                            <label class="form-label small mb-1 d-block">&nbsp;</label> <!-- Spacer -->
                            <button type="button" class="btn btn-sm btn-outline-danger remove-package-btn" title="Remove Package">
                                <i class="fas fa-trash-alt"></i>
                            </button>
                        </div>
                    </div>
                </div>
                <?php $packageIndex++; endforeach; ?>
            </div>
        </div>
    </div>

    <div class="modal-footer">
        <button type="button" class="btn btn-secondary btn-lg" data-bs-dismiss="modal">
            <i class="fas fa-times me-1"></i> Close
        </button>
        <button type="submit" class="btn btn-primary btn-lg">
            <i class="fas fa-save me-1"></i> Update Shipment
        </button>
    </div>
</form>

<script>
    // Package template for adding new packages
    const packageTemplate = `
    <div class="package-row border rounded p-3 mb-3" data-index="__INDEX__">
        <div class="row g-2 align-items-center">
            <div class="col-md-2">
                <label for="packages___INDEX___piece_type" class="form-label small mb-1">Type</label>
                <input type="text" id="packages___INDEX___piece_type"
                       name="packages[__INDEX__][piece_type]"
                       class="form-control form-control-sm"
                       value="Box">
            </div>

            <div class="col-md-3">
                <label for="packages___INDEX___description" class="form-label small mb-1">Description</label>
                <input type="text" id="packages___INDEX___description"
                       name="packages[__INDEX__][description]"
                       class="form-control form-control-sm">
            </div>

            <div class="col-md-1">
                <label for="packages___INDEX___quantity" class="form-label small mb-1">Qty</label>
                <input type="number" id="packages___INDEX___quantity"
                       name="packages[__INDEX__][quantity]"
                       class="form-control form-control-sm package-quantity"
                       value="1" min="1">
            </div>

            <div class="col-md-1">
                <label for="packages___INDEX___weight" class="form-label small mb-1">Wt (kg)</label>
                <input type="number" step="0.01" id="packages___INDEX___weight"
                       name="packages[__INDEX__][weight]"
                       class="form-control form-control-sm package-weight"
                       min="0">
            </div>

            <div class="col-md-4">
                <label class="form-label small mb-1">Dims (LxWxH cm)</label>
                <div class="input-group input-group-sm">
                    <input type="number" step="0.1" name="packages[__INDEX__][length]"
                           class="form-control package-dim" placeholder="L" min="0">
                    <input type="number" step="0.1" name="packages[__INDEX__][width]"
                           class="form-control package-dim" placeholder="W" min="0">
                    <input type="number" step="0.1" name="packages[__INDEX__][height]"
                           class="form-control package-dim" placeholder="H" min="0">
                </div>
            </div>

            <div class="col-md-1 text-end">
                <label class="form-label small mb-1 d-block">&nbsp;</label> <!-- Spacer -->
                <button type="button" class="btn btn-sm btn-outline-danger remove-package-btn" title="Remove Package">
                    <i class="fas fa-trash-alt"></i>
                </button>
            </div>
        </div>
    </div>
    `;

    // Initialize package management
    document.addEventListener('DOMContentLoaded', function() {
        let packageIndex = <?= $packageIndex ?>;
        const packagesContainer = document.getElementById('packagesContainer');
        const addPackageBtn = document.getElementById('addPackageBtn');

        // Add package
        addPackageBtn.addEventListener('click', function() {
            const newPackageHtml = packageTemplate.replace(/__INDEX__/g, packageIndex);
            const tempDiv = document.createElement('div');
            tempDiv.innerHTML = newPackageHtml;
            packagesContainer.appendChild(tempDiv.firstElementChild);
            packageIndex++;
        });

        // Remove package
        packagesContainer.addEventListener('click', function(e) {
            if (e.target.closest('.remove-package-btn')) {
                const packageRow = e.target.closest('.package-row');
                if (packagesContainer.children.length > 1) {
                    packageRow.remove();
                } else {
                    // Reset values instead of removing if it's the last package
                    const inputs = packageRow.querySelectorAll('input:not([type="hidden"])');
                    inputs.forEach(input => {
                        if (input.classList.contains('package-quantity')) {
                            input.value = 1;
                        } else if (input.type === 'text' && input.name.includes('piece_type')) {
                            input.value = 'Box';
                        } else {
                            input.value = '';
                        }
                    });
                }
            }
        });

        // Form submission
        const form = document.getElementById('editShipmentForm');
        form.addEventListener('submit', function(e) {
            e.preventDefault();

            // Submit form via AJAX
            const formData = new FormData(form);

            fetch(form.action, {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Close modal and show success message
                    const modal = bootstrap.Modal.getInstance(document.getElementById('editShipmentModal'));
                    modal.hide();

                    // Show success message
                    alert(data.message || 'Shipment updated successfully');

                    // Reload the page to show updated data
                    window.location.reload();
                } else {
                    // Show error message
                    alert(data.message || 'Error updating shipment');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('An error occurred while updating the shipment');
            });
        });
    });
</script>
