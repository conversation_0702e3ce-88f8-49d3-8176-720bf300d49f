<?php
/**
 * Edit Shipment Modal
 * This partial contains the modal for editing shipments
 */
?>
<!-- Edit Shipment Modal -->
<div class="modal fade" id="editShipmentModal" tabindex="-1" aria-labelledby="editShipmentModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl" style="max-width: 95%; width: 95%;">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editShipmentModalLabel">Edit Shipment</h5>
                <button type="button" class="btn btn-danger btn-lg" data-bs-dismiss="modal" aria-label="Close" style="font-size: 1.2rem; padding: 0.5rem 1rem;"><i class="fas fa-times"></i> Close</button>
            </div>
            <div class="modal-body">
                <div id="editShipmentContent">
                    <!-- Content will be loaded via AJAX -->
                    <div class="text-center p-5">
                        <div class="spinner-border" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <p class="mt-2">Loading shipment data...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- JavaScript for Edit Modal -->
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Function to load shipment data into the modal
        window.loadShipmentForEdit = function(shipmentId) {
            const modal = document.getElementById('editShipmentModal');
            const contentContainer = document.getElementById('editShipmentContent');

            // Show loading spinner
            contentContainer.innerHTML = `
                <div class="text-center p-5">
                    <div class="spinner-border" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p class="mt-2">Loading shipment data...</p>
                </div>
            `;

            // Load shipment data via AJAX
            fetch(`<?= App\Core\View::url('/admin/shipments/getEditForm/') ?>${shipmentId}`)
                .then(response => {
                    if (!response.ok) {
                        throw new Error('Network response was not ok');
                    }
                    return response.text();
                })
                .then(html => {
                    contentContainer.innerHTML = html;

                    // Initialize any form elements or scripts
                    initializeEditForm();
                })
                .catch(error => {
                    contentContainer.innerHTML = `
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-circle me-2"></i>
                            Error loading shipment data: ${error.message}
                        </div>
                    `;
                    console.error('Error loading shipment:', error);
                });
        };

        // Function to initialize form elements after loading
        function initializeEditForm() {
            // Initialize any select2 dropdowns
            if (typeof $.fn.select2 !== 'undefined') {
                $('.select2').select2({
                    dropdownParent: $('#editShipmentModal')
                });
            }

            // Initialize datepickers
            if (typeof $.fn.datepicker !== 'undefined') {
                $('.datepicker').datepicker({
                    format: 'yyyy-mm-dd',
                    autoclose: true
                });
            }

            // Add any other initialization code here
        }
    });
</script>
