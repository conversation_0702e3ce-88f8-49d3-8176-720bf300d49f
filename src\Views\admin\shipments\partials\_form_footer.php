        </div> <!-- End Left Column -->

        <!-- Right Column -->
        <div class="col-lg-4">
            <?php
            // Include right column partials
            include __DIR__ . '/_shipment_details.php';
            include __DIR__ . '/_dates_financials.php';
            include __DIR__ . '/_comments.php';
            ?>

            <!-- Form Actions -->
            <div class="card shadow-sm mb-4">
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-1"></i> <?= $isEdit ? 'Update Shipment' : 'Create Shipment' ?>
                        </button>
                        <a href="<?= App\Core\View::url('/admin/shipments') ?>" class="btn btn-outline-secondary">
                            <i class="fas fa-times me-1"></i> Cancel
                        </a>
                    </div>
                </div>
            </div>
        </div> <!-- End Right Column -->
    </div> <!-- End row -->
</form>

<!-- JavaScript for form functionality -->
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Package management
        const addPackageBtn = document.getElementById('addPackageBtn');
        const packagesContainer = document.getElementById('packagesContainer');
        const packageTemplate = document.getElementById('packageTemplate').innerHTML;
        let packageIndex = <?= $packageIndex ?>;

        // Add package
        addPackageBtn.addEventListener('click', function() {
            const newPackageHtml = packageTemplate.replace(/__INDEX__/g, packageIndex);
            const tempDiv = document.createElement('div');
            tempDiv.innerHTML = newPackageHtml;
            packagesContainer.appendChild(tempDiv.firstElementChild);

            // Add event listener to the new package type select
            const newPackageTypeSelect = tempDiv.firstElementChild.querySelector('.package-type-select');
            if (newPackageTypeSelect) {
                newPackageTypeSelect.addEventListener('change', handlePackageTypeChange);
            }

            packageIndex++;
            updateTotalWeight();
        });

        // Remove package
        packagesContainer.addEventListener('click', function(e) {
            if (e.target.closest('.remove-package-btn')) {
                const packageRow = e.target.closest('.package-row');
                if (packagesContainer.children.length > 1) {
                    packageRow.remove();
                } else {
                    // Reset values instead of removing if it's the last package
                    const inputs = packageRow.querySelectorAll('input:not([type="hidden"])');
                    inputs.forEach(input => {
                        if (input.classList.contains('package-quantity')) {
                            input.value = 1;
                        } else {
                            input.value = '';
                        }
                    });

                    // Reset select to first option
                    const selects = packageRow.querySelectorAll('select');
                    selects.forEach(select => {
                        select.selectedIndex = 0;
                    });
                }
                updateTotalWeight();
            }
        });

        // Update weight when package weights change
        packagesContainer.addEventListener('input', function(e) {
            if (e.target.classList.contains('package-weight') || e.target.classList.contains('package-quantity')) {
                updateTotalWeight();
            }
        });

        // Calculate total weight
        function updateTotalWeight() {
            let totalWeight = 0;
            const packageRows = packagesContainer.querySelectorAll('.package-row');

            packageRows.forEach(row => {
                const weightInput = row.querySelector('.package-weight');
                const quantityInput = row.querySelector('.package-quantity');

                if (weightInput.value && !isNaN(weightInput.value)) {
                    const weight = parseFloat(weightInput.value);
                    const quantity = parseInt(quantityInput.value) || 1;
                    totalWeight += weight * quantity;
                }
            });

            document.getElementById('total_weight').value = totalWeight.toFixed(2);
        }

        // Initialize total weight
        updateTotalWeight();

        // Handle package type selection
        function handlePackageTypeChange(e) {
            const select = e.target;
            const packageRow = select.closest('.package-row');
            const selectedOption = select.options[select.selectedIndex];
            const index = select.getAttribute('data-index');

            // Get the weight and dimensions inputs
            const weightInput = packageRow.querySelector(`[name="packages[${index}][weight]"]`);
            const lengthInput = packageRow.querySelector(`[name="packages[${index}][length]"]`);
            const widthInput = packageRow.querySelector(`[name="packages[${index}][width]"]`);
            const heightInput = packageRow.querySelector(`[name="packages[${index}][height]"]`);

            if (select.value === 'custom') {
                // Clear fields for custom entry
                weightInput.value = '';
                lengthInput.value = '';
                widthInput.value = '';
                heightInput.value = '';
                return;
            }

            // Auto-fill weight if available
            const weight = selectedOption.getAttribute('data-weight');
            if (weight) {
                weightInput.value = weight;
            }

            // Auto-fill dimensions if available
            const dimensions = selectedOption.getAttribute('data-dimensions');
            if (dimensions) {
                const [length, width, height] = dimensions.split('x').map(dim => dim.trim());
                if (length) lengthInput.value = length;
                if (width) widthInput.value = width;
                if (height) heightInput.value = height;
            }

            // Update total weight
            updateTotalWeight();
        }

        // Add event listeners to existing package type selects
        document.querySelectorAll('.package-type-select').forEach(select => {
            select.addEventListener('change', handlePackageTypeChange);
        });

        // Client selection and address loading
        const clientSelects = document.querySelectorAll('.client-select');

        clientSelects.forEach(select => {
            select.addEventListener('change', function() {
                const targetPrefix = this.dataset.targetPrefix;
                const clientId = this.value;

                if (clientId) {
                    // Get the selected option
                    const selectedOption = this.options[this.selectedIndex];

                    // Fill in the client data from the data attributes
                    const nameValue = selectedOption.dataset.name || '';
                    const phoneValue = selectedOption.dataset.phone || '';
                    const emailValue = selectedOption.dataset.email || '';

                    document.getElementById(`${targetPrefix}_name`).value = nameValue;
                    document.getElementById(`${targetPrefix}_phone`).value = phoneValue;
                    document.getElementById(`${targetPrefix}_email`).value = emailValue;

                    // Also update the hidden fields for validation
                    if (targetPrefix === 'shipper') {
                        document.getElementById('sender_name').value = nameValue;
                        document.getElementById('sender_phone').value = phoneValue;
                    } else if (targetPrefix === 'receiver') {
                        document.getElementById('recipient_name').value = nameValue;
                        document.getElementById('recipient_phone').value = phoneValue;
                    }

                    // Combine address parts for the textarea
                    let fullAddress = selectedOption.dataset.address || '';
                    if (selectedOption.dataset.city || selectedOption.dataset.state || selectedOption.dataset.postalCode) {
                        if (fullAddress) fullAddress += '\n';
                        if (selectedOption.dataset.city) fullAddress += selectedOption.dataset.city;
                        if (selectedOption.dataset.state) fullAddress += ', ' + selectedOption.dataset.state;
                        if (selectedOption.dataset.postalCode) fullAddress += ' ' + selectedOption.dataset.postalCode;
                    }
                    if (selectedOption.dataset.country) {
                        if (fullAddress) fullAddress += '\n';
                        fullAddress += selectedOption.dataset.country;
                    }

                    document.getElementById(`${targetPrefix}_address`).value = fullAddress;

                    // Also update the hidden fields for validation
                    if (targetPrefix === 'shipper') {
                        document.getElementById('sender_address').value = fullAddress;
                    } else if (targetPrefix === 'receiver') {
                        document.getElementById('recipient_address').value = fullAddress;
                    }

                    // Fetch addresses for this client
                    fetch(`<?= App\Core\View::url('/admin/shipments/getClientAddresses/') ?>${clientId}`)
                        .then(response => response.json())
                        .then(data => {
                            const addressSelect = document.getElementById(`${targetPrefix}_address_id`);

                            // Clear existing options except the first one
                            while (addressSelect.options.length > 1) {
                                addressSelect.remove(1);
                            }

                            // Add new options
                            if (data.addresses && data.addresses.length > 0) {
                                data.addresses.forEach(address => {
                                    const option = document.createElement('option');
                                    option.value = address.id;
                                    option.textContent = `${address.address_line1}, ${address.city}`;

                                    // Add data attributes for all address fields
                                    option.dataset.name = address.contact_name || '';
                                    option.dataset.address1 = address.address_line1 || '';
                                    option.dataset.address2 = address.address_line2 || '';
                                    option.dataset.city = address.city || '';
                                    option.dataset.state = address.state || '';
                                    option.dataset.zip = address.postal_code || '';
                                    option.dataset.country = address.country || '';
                                    option.dataset.phone = address.phone || '';
                                    option.dataset.email = address.email || '';

                                    addressSelect.appendChild(option);
                                });
                            }
                        })
                        .catch(error => console.error('Error fetching addresses:', error));
                }
            });
        });

        // Address selection
        const addressSelects = document.querySelectorAll('.address-select');

        addressSelects.forEach(select => {
            select.addEventListener('change', function() {
                const targetPrefix = this.dataset.targetPrefix;
                const selectedOption = this.options[this.selectedIndex];

                if (this.value) {
                    // Fill in the address fields
                    document.getElementById(`${targetPrefix}_name`).value = selectedOption.dataset.name || '';
                    document.getElementById(`${targetPrefix}_phone`).value = selectedOption.dataset.phone || '';
                    document.getElementById(`${targetPrefix}_email`).value = selectedOption.dataset.email || '';

                    // Combine address parts for the textarea
                    let fullAddress = selectedOption.dataset.address1 || '';
                    if (selectedOption.dataset.address2) {
                        fullAddress += '\n' + selectedOption.dataset.address2;
                    }
                    if (selectedOption.dataset.city || selectedOption.dataset.state || selectedOption.dataset.zip) {
                        fullAddress += '\n';
                        if (selectedOption.dataset.city) fullAddress += selectedOption.dataset.city;
                        if (selectedOption.dataset.state) fullAddress += ', ' + selectedOption.dataset.state;
                        if (selectedOption.dataset.zip) fullAddress += ' ' + selectedOption.dataset.zip;
                    }
                    if (selectedOption.dataset.country) {
                        fullAddress += '\n' + selectedOption.dataset.country;
                    }

                    document.getElementById(`${targetPrefix}_address`).value = fullAddress;
                }
            });
        });

        // New address button
        const addAddressBtns = document.querySelectorAll('.btn-add-address');

        addAddressBtns.forEach(btn => {
            btn.addEventListener('click', function() {
                const clientSelectId = this.dataset.clientSelect;
                const clientSelect = document.querySelector(clientSelectId);
                const clientId = clientSelect.value;

                if (!clientId) {
                    alert('Please select a client first.');
                    return;
                }

                // Open address creation page in a new tab/window
                window.open(`<?= App\Core\View::url('/admin/clients/') ?>${clientId}/addresses/create`, '_blank');
            });
        });
    });
</script>
