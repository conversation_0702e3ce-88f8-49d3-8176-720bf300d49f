<?php
// Shipment form view (used for both create and edit)
$isEdit = isset($shipment) && $shipment !== null;
$formAction = $isEdit ? App\Core\View::url('/admin/shipments/edit/' . $shipment['id']) : App\Core\View::url('/admin/shipments/create');

// Prepare old input or shipment data
$formData = [];
if (isset($old) && !empty($old)) {
    $formData = $old;
    error_log('Using old input data: ' . json_encode($old));
} elseif ($isEdit && isset($shipment) && !empty($shipment)) {
    $formData = $shipment;
    error_log('Using shipment data for edit: ' . json_encode($shipment));
    error_log('Form data being used: ' . print_r($formData, true));
    error_log('Shipment data structure: ' . print_r($shipment, true));
} else {
    error_log('No form data available. isEdit=' . ($isEdit ? 'true' : 'false') . ', shipment=' . (isset($shipment) ? 'set' : 'not set'));
}

// Helper function to get form value
function get_form_value($key, $default = '') {
    global $formData;

    // Debug all keys being requested
    error_log("Form value requested for key: {$key}");

    if (is_array($formData) && array_key_exists($key, $formData) && $formData[$key] !== null) {
        $value = $formData[$key];

        // For important fields, log the value
        if ($key == 'tracking_number' || $key == 'shipper_name' || $key == 'receiver_name' ||
            $key == 'shipment_type' || $key == 'shipment_mode' || $key == 'carrier' ||
            $key == 'payment_mode' || $key == 'status' || $key == 'client_id') {
            error_log("Form value for {$key}: {$value}");
        }

        // Handle different data types
        if (is_array($value)) {
            error_log("Value for {$key} is an array: " . json_encode($value));
            return $value; // Return array as is for special handling
        } else {
            return App\Core\View::e($value);
        }
    }

    // For important fields, log when default is used
    if ($key == 'tracking_number' || $key == 'shipper_name' || $key == 'receiver_name' ||
        $key == 'shipment_type' || $key == 'shipment_mode' || $key == 'carrier' ||
        $key == 'payment_mode' || $key == 'status' || $key == 'client_id') {
        error_log("No form value for {$key}, using default: {$default}");
    }

    return App\Core\View::e($default);
}
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <p class="text-muted">Fill in the details below to <?= $isEdit ? 'update' : 'create' ?> a shipment</p>
    </div>
    <a href="<?= App\Core\View::url('/admin/shipments') ?>" class="btn btn-outline-primary">
        <i class="fas fa-arrow-left me-1"></i> Back to Shipments
    </a>
</div>

<?php if (isset($flash_error)): ?>
    <div class="alert alert-danger mb-4">
        <i class="fas fa-exclamation-circle me-2"></i>
        <?= App\Core\View::e($flash_error) ?>
    </div>
<?php endif; ?>

<?php if (isset($errors) && !empty($errors)): ?>
    <div class="alert alert-danger mb-4">
        <strong><i class="fas fa-exclamation-triangle me-2"></i>Please fix the following errors:</strong>
        <ul class="mt-2 ms-4">
            <?php foreach ($errors as $field => $error): ?>
                <li><?= App\Core\View::e($error) ?></li>
            <?php endforeach; ?>
        </ul>
    </div>
<?php endif; ?>

<!-- Shipment form -->
<form action="<?= $formAction ?>" method="POST" id="shipmentForm">
    <?= App\Core\View::csrfField() ?>

    <!-- Hidden fields for validation -->
    <input type="hidden" id="sender_address" name="sender_address" value="<?= get_form_value('sender_address') ?>">
    <input type="hidden" id="recipient_address" name="recipient_address" value="<?= get_form_value('recipient_address') ?>">
    <input type="hidden" id="sender_name" name="sender_name" value="<?= get_form_value('sender_name') ?>">
    <input type="hidden" id="sender_phone" name="sender_phone" value="<?= get_form_value('sender_phone') ?>">
    <input type="hidden" id="recipient_name" name="recipient_name" value="<?= get_form_value('recipient_name') ?>">
    <input type="hidden" id="recipient_phone" name="recipient_phone" value="<?= get_form_value('recipient_phone') ?>">

    <div class="row g-4">
        <!-- Left Column -->
        <div class="col-lg-8"><?php // Left column starts here, will be closed in _form_footer.php ?>
