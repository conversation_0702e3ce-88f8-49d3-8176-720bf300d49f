<!-- Package Details -->
<div class="card shadow-sm mb-4">
    <div class="card-header bg-light py-3 d-flex justify-content-between align-items-center">
        <h5 class="mb-0 fw-bold"><i class="fas fa-box-open me-2"></i>Package Details</h5>
        <button type="button" id="addPackageBtn" class="btn btn-sm btn-success">
            <i class="fas fa-plus me-1"></i> Add Package
        </button>
    </div>
    <div class="card-body">
        <div id="packagesContainer">
            <?php
            // Debug package data
            error_log('Package details - formData packages: ' . (isset($formData['packages']) ? json_encode($formData['packages']) : 'not set'));
            error_log('Package details - shipmentPackages: ' . (isset($shipmentPackages) ? json_encode($shipmentPackages) : 'not set'));

            // Get packages from either formData or shipmentPackages
            $packages = $formData['packages'] ?? ($isEdit && isset($shipmentPackages) ? $shipmentPackages : [[]]); // Start with one empty row or existing data
            error_log('Package details - packages to use: ' . json_encode($packages));

            $packageIndex = 0;
            foreach ($packages as $package):
                error_log('Processing package: ' . json_encode($package));
                $packageId = $package['id'] ?? ''; // Keep track of existing package IDs for update
            ?>
            <div class="package-row border rounded p-3 mb-3" data-index="<?= $packageIndex ?>">
                <?php if ($isEdit && $packageId): ?>
                    <input type="hidden" name="packages[<?= $packageIndex ?>][id]" value="<?= $packageId ?>">
                <?php endif; ?>
                <div class="row g-2 align-items-center">
                    <div class="col-md-2">
                        <label for="packages_<?= $packageIndex ?>_piece_type" class="form-label small mb-1">Type</label>
                        <select id="packages_<?= $packageIndex ?>_piece_type" name="packages[<?= $packageIndex ?>][piece_type]" class="form-select form-select-sm package-type-select" data-index="<?= $packageIndex ?>">
                            <option value="">-- Select Type --</option>
                            <?php if (isset($packageTypes) && is_array($packageTypes)): ?>
                                <?php foreach ($packageTypes as $type): ?>
                                    <?php
                                        $typeName = is_array($type) ? $type['name'] : $type;
                                        $isSelected = isset($package['piece_type']) && $package['piece_type'] === $typeName;
                                    ?>
                                    <option value="<?= App\Core\View::e($typeName) ?>"
                                        data-weight="<?= is_array($type) ? App\Core\View::e($type['max_weight'] ?? '') : '' ?>"
                                        data-dimensions="<?= is_array($type) ? App\Core\View::e($type['dimensions'] ?? '') : '' ?>"
                                        <?= $isSelected ? 'selected' : '' ?>>
                                        <?= App\Core\View::e($typeName) ?>
                                    </option>
                                <?php endforeach; ?>
                            <?php endif; ?>
                            <option value="custom" <?= (!isset($package['piece_type']) || !isset($packageTypes) || !in_array($package['piece_type'], array_column(is_array($packageTypes) ? $packageTypes : [], 'name'))) ? 'selected' : '' ?>>Custom</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="packages_<?= $packageIndex ?>_description" class="form-label small mb-1">Description</label>
                        <input type="text" id="packages_<?= $packageIndex ?>_description" name="packages[<?= $packageIndex ?>][description]" class="form-control form-control-sm" value="<?= App\Core\View::e($package['description'] ?? '') ?>">
                    </div>
                    <div class="col-md-1">
                        <label for="packages_<?= $packageIndex ?>_quantity" class="form-label small mb-1">Qty</label>
                        <input type="number" id="packages_<?= $packageIndex ?>_quantity" name="packages[<?= $packageIndex ?>][quantity]" class="form-control form-control-sm package-quantity" value="<?= App\Core\View::e($package['quantity'] ?? 1) ?>" min="1">
                    </div>
                    <div class="col-md-1">
                        <label for="packages_<?= $packageIndex ?>_weight" class="form-label small mb-1">Wt (kg)</label>
                        <input type="number" step="0.01" id="packages_<?= $packageIndex ?>_weight" name="packages[<?= $packageIndex ?>][weight]" class="form-control form-control-sm package-weight" value="<?= App\Core\View::e($package['weight'] ?? '') ?>" min="0">
                    </div>
                    <div class="col-md-4">
                        <label class="form-label small mb-1">Dims (LxWxH cm)</label>
                        <div class="input-group input-group-sm">
                            <input type="number" step="0.1" name="packages[<?= $packageIndex ?>][length]" class="form-control package-dim" placeholder="L" value="<?= App\Core\View::e($package['length'] ?? '') ?>" min="0">
                            <input type="number" step="0.1" name="packages[<?= $packageIndex ?>][width]" class="form-control package-dim" placeholder="W" value="<?= App\Core\View::e($package['width'] ?? '') ?>" min="0">
                            <input type="number" step="0.1" name="packages[<?= $packageIndex ?>][height]" class="form-control package-dim" placeholder="H" value="<?= App\Core\View::e($package['height'] ?? '') ?>" min="0">
                        </div>
                    </div>
                    <div class="col-md-1 text-end">
                        <label class="form-label small mb-1 d-block">&nbsp;</label> <!-- Spacer -->
                        <button type="button" class="btn btn-sm btn-outline-danger remove-package-btn" title="Remove Package">
                            <i class="fas fa-trash-alt"></i>
                        </button>
                    </div>
                </div>
            </div>
            <?php $packageIndex++; endforeach; ?>
        </div>
        <div id="packageTemplate" style="display: none;">
            <div class="package-row border rounded p-3 mb-3" data-index="__INDEX__">
                <div class="row g-2 align-items-center">
                    <div class="col-md-2">
                        <label for="packages___INDEX___piece_type" class="form-label small mb-1">Type</label>
                        <select id="packages___INDEX___piece_type" name="packages[__INDEX__][piece_type]" class="form-select form-select-sm package-type-select" data-index="__INDEX__">
                            <option value="">-- Select Type --</option>
                            <?php if (isset($packageTypes) && is_array($packageTypes)): ?>
                                <?php foreach ($packageTypes as $type): ?>
                                    <?php $typeName = is_array($type) ? $type['name'] : $type; ?>
                                    <option value="<?= App\Core\View::e($typeName) ?>"
                                        data-weight="<?= is_array($type) ? App\Core\View::e($type['max_weight'] ?? '') : '' ?>"
                                        data-dimensions="<?= is_array($type) ? App\Core\View::e($type['dimensions'] ?? '') : '' ?>">
                                        <?= App\Core\View::e($typeName) ?>
                                    </option>
                                <?php endforeach; ?>
                            <?php endif; ?>
                            <option value="custom">Custom</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="packages___INDEX___description" class="form-label small mb-1">Description</label>
                        <input type="text" id="packages___INDEX___description" name="packages[__INDEX__][description]" class="form-control form-control-sm">
                    </div>
                    <div class="col-md-1">
                        <label for="packages___INDEX___quantity" class="form-label small mb-1">Qty</label>
                        <input type="number" id="packages___INDEX___quantity" name="packages[__INDEX__][quantity]" class="form-control form-control-sm package-quantity" value="1" min="1">
                    </div>
                    <div class="col-md-1">
                        <label for="packages___INDEX___weight" class="form-label small mb-1">Wt (kg)</label>
                        <input type="number" step="0.01" id="packages___INDEX___weight" name="packages[__INDEX__][weight]" class="form-control form-control-sm package-weight" min="0">
                    </div>
                    <div class="col-md-4">
                        <label class="form-label small mb-1">Dims (LxWxH cm)</label>
                        <div class="input-group input-group-sm">
                            <input type="number" step="0.1" name="packages[__INDEX__][length]" class="form-control package-dim" placeholder="L" min="0">
                            <input type="number" step="0.1" name="packages[__INDEX__][width]" class="form-control package-dim" placeholder="W" min="0">
                            <input type="number" step="0.1" name="packages[__INDEX__][height]" class="form-control package-dim" placeholder="H" min="0">
                        </div>
                    </div>
                    <div class="col-md-1 text-end">
                        <label class="form-label small mb-1 d-block">&nbsp;</label> <!-- Spacer -->
                        <button type="button" class="btn btn-sm btn-outline-danger remove-package-btn" title="Remove Package">
                            <i class="fas fa-trash-alt"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
