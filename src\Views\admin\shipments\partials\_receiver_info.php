<!-- Receiver Information -->
<div class="card shadow-sm mb-4">
    <div class="card-header bg-light py-3">
        <h5 class="mb-0 fw-bold"><i class="fas fa-user-tag me-2"></i>Receiver Information</h5>
    </div>
    <div class="card-body">
        <div class="row g-3">
            <div class="col-md-6 mb-3">
                <label for="receiver_client_id" class="form-label fw-bold">Select Client (Receiver)</label>
                <select id="receiver_client_id" name="receiver_client_id" class="form-select client-select" data-target-prefix="receiver">
                    <option value="">-- Select Client --</option>
                    <?php if (isset($clients) && is_array($clients)): ?>
                        <?php foreach ($clients as $client): ?>
                            <option value="<?= $client['id'] ?>"
                                <?= (string)get_form_value('receiver_client_id') === (string)$client['id'] ? 'selected' : '' ?>
                                data-name="<?= App\Core\View::e($client['name']) ?>"
                                data-phone="<?= App\Core\View::e($client['phone']) ?>"
                                data-email="<?= App\Core\View::e($client['email']) ?>"
                                data-address="<?= App\Core\View::e($client['address']) ?>"
                                data-city="<?= App\Core\View::e($client['city']) ?>"
                                data-state="<?= App\Core\View::e($client['state']) ?>"
                                data-postal-code="<?= App\Core\View::e($client['postal_code']) ?>"
                                data-country="<?= App\Core\View::e($client['country']) ?>"
                            >
                                <?= App\Core\View::e($client['name']) ?> <?= !empty($client['company']) ? '(' . App\Core\View::e($client['company']) . ')' : '' ?>
                            </option>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </select>
                 <div class="mt-1">
                    <a href="<?= App\Core\View::url('/admin/clients/create') ?>" class="btn btn-sm btn-outline-secondary" target="_blank">
                        <i class="fas fa-plus me-1"></i> New Client
                    </a>
                </div>
            </div>
            <div class="col-md-6 mb-3">
                <label for="receiver_address_id" class="form-label fw-bold">Select Address</label>
                <div class="d-flex">
                    <select id="receiver_address_id" name="receiver_address_id" class="form-select address-select" data-target-prefix="receiver">
                        <option value="">-- Select Address --</option>
                        <!-- Addresses will be loaded via JavaScript -->
                    </select>
                    <button type="button" class="btn btn-sm btn-outline-secondary ms-2 btn-add-address" data-client-select="#receiver_client_id">
                        <i class="fas fa-plus"></i>
                    </button>
                </div>
                <p class="form-text text-muted small mt-1">
                    <i class="fas fa-info-circle me-1"></i> Select a client to load their addresses
                </p>
            </div>
        </div>
        <div class="row g-3 mt-2">
            <div class="col-md-6 mb-3">
                <label for="receiver_name" class="form-label fw-bold">Receiver Name <span class="text-danger">*</span></label>
                <div class="input-group">
                    <span class="input-group-text"><i class="fas fa-user"></i></span>
                    <input type="text" id="receiver_name" name="receiver_name" class="form-control" value="<?= get_form_value('receiver_name') ?>" required>
                </div>

            </div>
            <div class="col-md-6 mb-3">
                <label for="receiver_phone" class="form-label fw-bold">Receiver Phone <span class="text-danger">*</span></label>
                <div class="input-group">
                    <span class="input-group-text"><i class="fas fa-phone"></i></span>
                    <input type="tel" id="receiver_phone" name="receiver_phone" class="form-control" value="<?= get_form_value('receiver_phone') ?>" required>
                </div>

            </div>
            <div class="col-12 mb-3">
                <label for="receiver_address" class="form-label fw-bold">Receiver Address <span class="text-danger">*</span></label>
                <div class="input-group">
                    <span class="input-group-text"><i class="fas fa-map-marker-alt"></i></span>
                    <textarea id="receiver_address" name="receiver_address" class="form-control" rows="3" required><?= get_form_value('receiver_address') ?></textarea>
                </div>

            </div>
            <div class="col-md-6 mb-3">
                <label for="receiver_email" class="form-label fw-bold">Receiver Email</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="fas fa-envelope"></i></span>
                    <input type="email" id="receiver_email" name="receiver_email" class="form-control" value="<?= get_form_value('receiver_email') ?>">
                </div>
            </div>
        </div>
    </div>
</div>
