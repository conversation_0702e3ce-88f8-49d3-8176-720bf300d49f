<!-- Shipment Details -->
<div class="card shadow-sm mb-4">
    <div class="card-header bg-light py-3">
        <h5 class="mb-0 fw-bold"><i class="fas fa-shipping-fast me-2"></i>Shipment Details</h5>
    </div>
    <div class="card-body">

        <div class="mb-3">
            <label for="client_id" class="form-label fw-bold">Primary Client (Billing/Owner)</label>
            <div class="input-group">
                <span class="input-group-text"><i class="fas fa-user-tie"></i></span>
                <select id="client_id" name="client_id" class="form-select">
                    <option value="">-- Select Client --</option>
                    <?php if (isset($clients) && is_array($clients)): ?>
                        <?php
                        $selectedPrimaryClient = get_form_value('client_id');
                        error_log("Selected client_id: {$selectedPrimaryClient}");
                        error_log("Client ID type: " . gettype($selectedPrimaryClient));

                        // Dump all clients for debugging
                        error_log("Available clients: " . json_encode($clients));

                        foreach ($clients as $client):
                            // Force string comparison
                            $isSelected = (string)$selectedPrimaryClient === (string)$client['id'];
                            error_log("Checking client {$client['id']} (" . gettype($client['id']) . ") against {$selectedPrimaryClient} (" . gettype($selectedPrimaryClient) . "): " . ($isSelected ? 'selected' : 'not selected'));
                        ?>
                            <option value="<?= $client['id'] ?>" <?= $isSelected ? 'selected' : '' ?>>
                                <?= App\Core\View::e($client['name']) ?> <?= !empty($client['company']) ? '(' . App\Core\View::e($client['company']) . ')' : '' ?>
                            </option>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </select>
            </div>
        </div>

        <div class="mb-3">
            <label for="employee_id" class="form-label fw-bold">Assigned Employee</label>
            <div class="input-group">
                <span class="input-group-text"><i class="fas fa-user-shield"></i></span>
                <select id="employee_id" name="employee_id" class="form-select">
                    <option value="">-- Select Employee --</option>
                    <?php if (isset($employees) && is_array($employees)): ?>
                        <?php
                        $selectedEmployee = get_form_value('employee_id');
                        foreach ($employees as $employee):
                        ?>
                            <option value="<?= $employee['id'] ?>" <?= $selectedEmployee == $employee['id'] ? 'selected' : '' ?>>
                                <?= App\Core\View::e($employee['name']) ?>
                            </option>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </select>
            </div>
        </div>

        <div class="mb-3">
            <label for="origin" class="form-label">Origin <span class="text-danger">*</span></label>
            <input type="text" id="origin" name="origin" class="form-control" value="<?= get_form_value('origin') ?>" required>
        </div>

        <div class="mb-3">
            <label for="destination" class="form-label">Destination <span class="text-danger">*</span></label>
            <input type="text" id="destination" name="destination" class="form-control" value="<?= get_form_value('destination') ?>" required>
        </div>

        <div class="mb-3">
            <label for="shipment_type" class="form-label">Shipment Type</label>
            <select id="shipment_type" name="shipment_type" class="form-select">
                <option value="">-- Select Type --</option>
                <?php
                $selectedType = get_form_value('shipment_type');
                error_log("Selected shipment_type: {$selectedType}");
                if (isset($shipmentTypes)): foreach ($shipmentTypes as $type):
                    // Extract name from array if it's an array
                    $typeName = is_array($type) ? $type['name'] : $type;
                    $isSelected = (string)$selectedType === (string)$typeName;
                    error_log("Checking type {$typeName}: " . ($isSelected ? 'selected' : 'not selected'));
                ?>
                    <option value="<?= App\Core\View::e($typeName) ?>" <?= $isSelected ? 'selected' : '' ?>><?= App\Core\View::e($typeName) ?></option>
                <?php endforeach; endif; ?>
            </select>
        </div>

        <div class="mb-3">
            <label for="shipment_mode" class="form-label">Shipment Mode</label>
            <select id="shipment_mode" name="shipment_mode" class="form-select">
                <option value="">-- Select Mode --</option>
                <?php
                $selectedMode = get_form_value('shipment_mode');
                error_log("Selected shipment_mode: {$selectedMode}");
                if (isset($shipmentModes)): foreach ($shipmentModes as $mode):
                    // Extract name from array if it's an array
                    $modeName = is_array($mode) ? $mode['name'] : $mode;
                    $isSelected = (string)$selectedMode === (string)$modeName;
                    error_log("Checking mode {$modeName}: " . ($isSelected ? 'selected' : 'not selected'));
                ?>
                    <option value="<?= App\Core\View::e($modeName) ?>" <?= $isSelected ? 'selected' : '' ?>><?= App\Core\View::e($modeName) ?></option>
                <?php endforeach; endif; ?>
            </select>
        </div>

        <div class="mb-3">
            <label for="carrier" class="form-label">Carrier</label>
            <select id="carrier" name="carrier" class="form-select">
                <option value="">-- Select Carrier --</option>
                <?php
                $selectedCarrier = get_form_value('carrier');
                error_log("Selected carrier: {$selectedCarrier}");
                if (isset($carriers)): foreach ($carriers as $carrier):
                    // Extract name from array if it's an array
                    $carrierName = is_array($carrier) ? $carrier['name'] : $carrier;
                    $isSelected = (string)$selectedCarrier === (string)$carrierName;
                    error_log("Checking carrier {$carrierName}: " . ($isSelected ? 'selected' : 'not selected'));
                ?>
                    <option value="<?= App\Core\View::e($carrierName) ?>" <?= $isSelected ? 'selected' : '' ?>><?= App\Core\View::e($carrierName) ?></option>
                <?php endforeach; endif; ?>
            </select>
        </div>

        <div class="mb-3">
            <label for="carrier_reference_no" class="form-label">Carrier Reference No.</label>
            <input type="text" id="carrier_reference_no" name="carrier_reference_no" class="form-control" value="<?= get_form_value('carrier_reference_no') ?>">
        </div>
    </div>
</div>
