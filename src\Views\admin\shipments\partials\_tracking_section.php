<!-- Tracking Section -->
<div class="card shadow-sm mb-4">
    <div class="card-header bg-light py-3">
        <h5 class="mb-0 fw-bold"><i class="fas fa-barcode me-2"></i>Tracking Information</h5>
    </div>
    <div class="card-body">
        <div class="mb-3">
            <label for="tracking_number" class="form-label fw-bold">Tracking Number</label>
            <div class="input-group">
                <span class="input-group-text"><i class="fas fa-barcode"></i></span>
                <input type="text" id="tracking_number" name="tracking_number" class="form-control"
                    value="<?= get_form_value('tracking_number', $generatedTrackingNumber ?? '') ?>"
                    <?= $isEdit ? '' : 'readonly' ?> required>
            </div>
            <?php if (!$isEdit): ?>
                <small class="text-muted mt-1 d-block">Auto-generated tracking number</small>
            <?php endif; ?>
        </div>

        <div class="mb-3">
            <label for="status" class="form-label fw-bold">Status</label>
            <div class="input-group">
                <span class="input-group-text"><i class="fas fa-tag"></i></span>
                <select id="status" name="status" class="form-select" required>
                    <?php
                    $selectedStatus = get_form_value('status', 'pending');
                    error_log("Selected status: {$selectedStatus}");
                    error_log("Status options: " . json_encode($statusOptions));

                    if (isset($statusOptions) && !empty($statusOptions)) {
                        foreach ($statusOptions as $value => $label):
                            if (is_array($label) && isset($label['name'])) {
                                $optionValue = $label['name'];
                                $optionLabel = ucfirst(str_replace('_', ' ', $label['name']));
                            } else {
                                $optionValue = $value;
                                $optionLabel = ucfirst(str_replace('_', ' ', $label));
                            }

                            // Force string comparison
                            $selected = (string)$selectedStatus === (string)$optionValue ? 'selected' : '';
                            error_log("Checking status option {$optionValue} against {$selectedStatus}: " . ($selected ? 'selected' : 'not selected'));
                    ?>
                        <option value="<?= $optionValue ?>" <?= $selected ?>>
                            <?= App\Core\View::e($optionLabel) ?>
                        </option>
                    <?php
                        endforeach;
                    } else {
                        // Fallback
                        $statuses = ['pending' => 'Pending', 'in_transit' => 'In Transit', 'delivered' => 'Delivered', 'delayed' => 'Delayed', 'cancelled' => 'Cancelled'];
                        foreach ($statuses as $value => $label):
                    ?>
                        <option value="<?= $value ?>" <?= $selectedStatus === $value ? 'selected' : '' ?>>
                            <?= App\Core\View::e($label) ?>
                        </option>
                    <?php
                        endforeach;
                    }
                    ?>
                </select>
            </div>
            <div class="mt-2">
                <a href="<?= App\Core\View::url('/admin/settings/status') ?>" class="btn btn-sm btn-outline-secondary">
                    <i class="fas fa-cog me-1"></i> Manage Statuses
                </a>
            </div>
        </div>
    </div>
</div>
