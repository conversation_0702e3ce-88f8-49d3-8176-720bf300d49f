<?php
// Shipment status update view
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 fw-bold">Update Shipment Status</h1>
        <p class="text-muted">Update the status and location of shipment #<?= App\Core\View::e($shipment['id']) ?></p>
    </div>
    <a href="<?= App\Core\View::url('/admin/shipments/view/' . $shipment['id']) ?>" class="btn btn-outline-primary">
        <i class="fas fa-arrow-left me-1"></i> Back to Shipment
    </a>
</div>

<?php if (isset($flash_error)): ?>
    <div class="alert alert-error mb-4">
        <i class="fas fa-exclamation-circle me-2"></i>
        <?= App\Core\View::e($flash_error) ?>
    </div>
<?php endif; ?>

<?php if (isset($errors) && !empty($errors)): ?>
    <div class="alert alert-error mb-4">
        <strong><i class="fas fa-exclamation-triangle me-2"></i>Please fix the following errors:</strong>
        <ul class="mt-2 ms-4">
            <?php foreach ($errors as $field => $error): ?>
                <li><?= App\Core\View::e($error) ?></li>
            <?php endforeach; ?>
        </ul>
    </div>
<?php endif; ?>

<div class="row">
    <div class="col-md-6">
        <div class="card shadow-sm mb-4">
            <div class="card-header bg-light">
                <h5 class="mb-0"><i class="fas fa-info-circle me-2"></i>Shipment Information</h5>
            </div>
            <div class="card-body">
                <table class="table-container">
                    <tr>
                        <th>Tracking Number:</th>
                        <td><?= App\Core\View::e($shipment['tracking_number']) ?></td>
                    </tr>
                    <tr>
                        <th>Origin:</th>
                        <td><?= App\Core\View::e($shipment['origin']) ?></td>
                    </tr>
                    <tr>
                        <th>Destination:</th>
                        <td><?= App\Core\View::e($shipment['destination']) ?></td>
                    </tr>
                    <tr>
                        <th>Shipper:</th>
                        <td><?= App\Core\View::e($shipment['shipper_name']) ?></td>
                    </tr>
                    <tr>
                        <th>Receiver:</th>
                        <td><?= App\Core\View::e($shipment['receiver_name']) ?></td>
                    </tr>
                    <tr>
                        <th>Current Status:</th>
                        <td>
                            <?php
                            $statusClass = 'bg-primary';
                            switch ($shipment['status']) {
                                case 'pending': $statusClass = 'bg-warning text-dark'; break;
                                case 'in_transit': $statusClass = 'bg-info'; break;
                                case 'out_for_delivery': $statusClass = 'bg-primary'; break;
                                case 'delivered': $statusClass = 'bg-success'; break;
                                case 'delayed': $statusClass = 'bg-danger'; break;
                                case 'cancelled': $statusClass = 'bg-secondary'; break;
                                case 'picked_up': $statusClass = 'bg-info'; break;
                                case 'on_hold': $statusClass = 'bg-warning text-dark'; break;
                                case 'returned': $statusClass = 'bg-danger'; break;
                            }
                            ?>
                            <span class="badge <?= $statusClass ?>">
                                <?= App\Core\View::e(ucfirst(str_replace('_', ' ', $shipment['status']))) ?>
                            </span>
                        </td>
                    </tr>
                </table>
            </div>
        </div>
    </div>

    <div class="col-md-6">
        <div class="card shadow-sm mb-4">
            <div class="card-header bg-light">
                <h5 class="mb-0"><i class="fas fa-edit me-2"></i>Update Status</h5>
            </div>
            <div class="card-body">
                <form action="<?= App\Core\View::url('/admin/shipments/updateStatus/' . $shipment['id']) ?>" method="POST">
                    <?= App\Core\View::csrfField() ?>

                    <div class="form-group mb-3">
                        <label for="status" class="form-label fw-bold mb-2">New Status</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-tag"></i></span>
                            <select id="status" name="status" class="form-select" required>
                                <?php
                                // Use status options from database if available
                                if (isset($statusOptions) && !empty($statusOptions)) {
                                    foreach ($statusOptions as $value => $label):
                                        if (is_array($label) && isset($label['name'])) {
                                            // Handle case where statusOptions is an array of objects
                                            $optionValue = $label['name'];
                                            $optionLabel = ucfirst(str_replace('_', ' ', $label['name']));
                                        } else {
                                            // Handle case where statusOptions is an associative array
                                            $optionValue = $value;
                                            $optionLabel = ucfirst(str_replace('_', ' ', $label));
                                        }
                                ?>
                                    <option value="<?= $optionValue ?>">
                                        <?= App\Core\View::e($optionLabel) ?>
                                    </option>
                                <?php
                                    endforeach;
                                } else {
                                    // Fallback to hardcoded statuses
                                    $statuses = ['pending' => 'Pending', 'in_transit' => 'In Transit', 'out_for_delivery' => 'Out for Delivery', 'delivered' => 'Delivered', 'delayed' => 'Delayed', 'cancelled' => 'Cancelled'];
                                    foreach ($statuses as $value => $label):
                                ?>
                                    <option value="<?= $value ?>">
                                        <?= App\Core\View::e($label) ?>
                                    </option>
                                <?php
                                    endforeach;
                                }
                                ?>
                            </select>
                        </div>
                    </div>

                    <div class="form-group mb-3">
                        <label for="location" class="form-label fw-bold mb-2">Current Location</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-map-marker-alt"></i></span>
                            <input type="text" id="location" name="location" class="form-control" required>
                        </div>
                    </div>

                    <div class="row g-3">
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label for="status_date" class="form-label fw-bold mb-2">Date</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="fas fa-calendar"></i></span>
                                    <input type="date" id="status_date" name="status_date" class="form-control" value="<?= date('Y-m-d') ?>" required>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label for="status_time" class="form-label fw-bold mb-2">Time</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="fas fa-clock"></i></span>
                                    <input type="time" id="status_time" name="status_time" class="form-control" value="<?= date('H:i') ?>" required>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="form-group mb-3">
                        <label for="description" class="form-label fw-bold mb-2">Description</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-comment"></i></span>
                            <textarea id="description" name="description" class="form-control" rows="3" placeholder="Enter any additional information about this status update"></textarea>
                        </div>
                    </div>

                    <div class="form-check mb-3">
                        <input class="form-check-input" type="checkbox" id="notify_receiver" name="notify_receiver" value="1" checked>
                        <label class="form-check-label" for="notify_receiver">
                            Notify receiver via email
                        </label>
                    </div>

                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary" style="background-color: #007bff; color: white; border-color: #007bff;">
                            <i class="fas fa-save me-1"></i> Update Status
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<div class="card shadow-sm mb-4">
    <div class="card-header bg-light">
        <h5 class="mb-0"><i class="fas fa-history me-2"></i>Status History</h5>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-hover">
                <thead class="table-light">
                    <tr>
                        <th>Date & Time</th>
                        <th>Status</th>
                        <th>Location</th>
                        <th>Description</th>
                    </tr>
                </thead>
                <tbody>
                    <?php if (empty($history)): ?>
                    <tr>
                        <td colspan="4" class="text-center">No history records found.</td>
                    </tr>
                    <?php else: ?>
                        <?php foreach ($history as $record): ?>
                        <tr>
                            <td><?= App\Core\View::e(date('M d, Y H:i', strtotime($record['date_time']))) ?></td>
                            <td>
                                <?php
                                $statusClass = 'bg-primary';
                                switch ($record['status']) {
                                    case 'pending': $statusClass = 'bg-warning text-dark'; break;
                                    case 'in_transit': $statusClass = 'bg-info'; break;
                                    case 'out_for_delivery': $statusClass = 'bg-primary'; break;
                                    case 'delivered': $statusClass = 'bg-success'; break;
                                    case 'delayed': $statusClass = 'bg-danger'; break;
                                    case 'cancelled': $statusClass = 'bg-secondary'; break;
                                    case 'picked_up': $statusClass = 'bg-info'; break;
                                    case 'on_hold': $statusClass = 'bg-warning text-dark'; break;
                                    case 'returned': $statusClass = 'bg-danger'; break;
                                }
                                ?>
                                <span class="badge <?= $statusClass ?>">
                                    <?= App\Core\View::e(ucfirst(str_replace('_', ' ', $record['status']))) ?>
                                </span>
                            </td>
                            <td><?= App\Core\View::e($record['location']) ?></td>
                            <td><?= App\Core\View::e($record['message']) ?></td>
                        </tr>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>
