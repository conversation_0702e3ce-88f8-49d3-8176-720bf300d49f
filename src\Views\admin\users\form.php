<?php
// User form view (used for both create and edit)
$isEdit = isset($user) && $user !== null;
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h1><?= $isEdit ? 'Edit User' : 'Create New User' ?></h1>
    <a href="<?= App\Core\View::url('/admin/users') ?>" class="btn btn-primary">
        <i class="fas fa-arrow-left"></i> Back to Users
    </a>
</div>

<?php if (isset($flash_error)): ?>
    <div class="alert alert-error">
        <?= App\Core\View::e($flash_error) ?>
    </div>
<?php endif; ?>

<?php if (isset($errors) && !empty($errors)): ?>
    <div class="alert alert-error">
        <strong>Please fix the following errors:</strong>
        <ul>
            <?php foreach ($errors as $field => $error): ?>
                <li><?= App\Core\View::e($error) ?></li>
            <?php endforeach; ?>
        </ul>
    </div>
<?php endif; ?>

<div class="card">
    <div class="card-header"><?= $isEdit ? 'Edit User Details' : 'User Details' ?></div>
    <div class="card-body">
        <form action="<?= App\Core\View::url($formAction) ?>" method="POST">
            <?= App\Core\View::csrfField() ?>
            
            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="username">Username</label>
                        <input type="text" id="username" name="username" class="form-control" 
                            value="<?= isset($old['username']) ? App\Core\View::e($old['username']) : ($isEdit ? App\Core\View::e($user['username']) : '') ?>" 
                            required>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="email">Email</label>
                        <input type="email" id="email" name="email" class="form-control" 
                            value="<?= isset($old['email']) ? App\Core\View::e($old['email']) : ($isEdit ? App\Core\View::e($user['email']) : '') ?>" 
                            required>
                    </div>
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="name">Full Name</label>
                        <input type="text" id="name" name="name" class="form-control" 
                            value="<?= isset($old['name']) ? App\Core\View::e($old['name']) : ($isEdit ? App\Core\View::e($user['name']) : '') ?>" 
                            required>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="role">Role</label>
                        <select id="role" name="role" class="form-control" required>
                            <?php 
                            $roles = ['admin' => 'Administrator', 'staff' => 'Staff'];
                            $selectedRole = isset($old['role']) ? $old['role'] : ($isEdit ? $user['role'] : 'staff');
                            
                            foreach ($roles as $value => $label): 
                            ?>
                                <option value="<?= $value ?>" <?= $selectedRole === $value ? 'selected' : '' ?>>
                                    <?= App\Core\View::e($label) ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="password">
                            <?= $isEdit ? 'Password (leave blank to keep current)' : 'Password' ?>
                        </label>
                        <input type="password" id="password" name="password" class="form-control" 
                            <?= $isEdit ? '' : 'required' ?>>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="is_active">Status</label>
                        <select id="is_active" name="is_active" class="form-control">
                            <?php 
                            $statuses = [1 => 'Active', 0 => 'Inactive'];
                            $selectedStatus = isset($old['is_active']) ? (int)$old['is_active'] : ($isEdit ? (int)$user['is_active'] : 1);
                            
                            foreach ($statuses as $value => $label): 
                            ?>
                                <option value="<?= $value ?>" <?= $selectedStatus === $value ? 'selected' : '' ?>>
                                    <?= App\Core\View::e($label) ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                </div>
            </div>
            
            <div class="form-group mt-4">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save"></i> <?= $isEdit ? 'Update User' : 'Create User' ?>
                </button>
                <a href="<?= App\Core\View::url('/admin/users') ?>" class="btn btn-secondary">
                    <i class="fas fa-times"></i> Cancel
                </a>
            </div>
        </form>
    </div>
</div>
