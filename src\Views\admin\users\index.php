<?php
// Users index view
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>Manage Users</h1>
    <a href="<?= App\Core\View::url('/admin/users/create') ?>" class="btn btn-primary">
        <i class="fas fa-plus"></i> New User
    </a>
</div>

<?php if (isset($flash_success)): ?>
    <div class="alert alert-success">
        <?= App\Core\View::e($flash_success) ?>
    </div>
<?php endif; ?>

<?php if (isset($flash_error)): ?>
    <div class="alert alert-error">
        <?= App\Core\View::e($flash_error) ?>
    </div>
<?php endif; ?>

<div class="card">
    <div class="card-header">All Users</div>
    <div class="card-body">
        <div class="table-container">
            <?php if (empty($users)): ?>
                <p class="text-muted">No users found.</p>
            <?php else: ?>
                <table>
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Username</th>
                            <th>Name</th>
                            <th>Email</th>
                            <th>Role</th>
                            <th>Status</th>
                            <th>Last Login</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($users as $user): ?>
                            <tr>
                                <td><?= App\Core\View::e($user['id']) ?></td>
                                <td><?= App\Core\View::e($user['username']) ?></td>
                                <td><?= App\Core\View::e($user['name']) ?></td>
                                <td><?= App\Core\View::e($user['email']) ?></td>
                                <td>
                                    <?php if ($user['role'] === 'admin'): ?>
                                        <span class="badge bg-primary">Admin</span>
                                    <?php else: ?>
                                        <span class="badge bg-info">Staff</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php if ($user['is_active']): ?>
                                        <span class="badge bg-success">Active</span>
                                    <?php else: ?>
                                        <span class="badge bg-danger">Inactive</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?= $user['last_login'] ? App\Core\View::e(date('Y-m-d H:i', strtotime($user['last_login']))) : 'Never' ?>
                                </td>
                                <td>
                                    <a href="<?= App\Core\View::url('/admin/users/edit/' . $user['id']) ?>" class="btn btn-sm btn-success" title="Edit">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <?php if ($user['id'] != App\Core\Session::get('admin_user_id')): ?>
                                        <form action="<?= App\Core\View::url('/admin/users/delete/' . $user['id']) ?>" method="POST" style="display: inline;">
                                            <?= App\Core\View::csrfField() ?>
                                            <button type="submit" class="btn btn-sm btn-danger delete-confirm" title="Delete" onclick="return confirm('Are you sure you want to delete this user? This action cannot be undone.');">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </form>
                                    <?php endif; ?>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            <?php endif; ?>
        </div>
    </div>
</div>
