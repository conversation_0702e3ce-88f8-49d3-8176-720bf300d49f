<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Contact Support - <?= App\Models\Setting::get('site_name', 'Enterprise Logistics Tracking Application') ?></title>
    <link rel="stylesheet" href="<?= App\Core\View::asset('css/track-result.css') ?>">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .contact-container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }
        
        .contact-header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .contact-header h1 {
            color: var(--primary-color);
            margin-bottom: 10px;
        }
        
        .contact-methods {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .contact-method {
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
            text-align: center;
            border: 1px solid #e9ecef;
        }
        
        .contact-method i {
            font-size: 2rem;
            color: var(--primary-color);
            margin-bottom: 10px;
        }
        
        .contact-method h3 {
            margin-bottom: 10px;
            color: var(--text-color);
        }
        
        .contact-form {
            background: #f8f9fa;
            padding: 30px;
            border-radius: 8px;
            margin-top: 20px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
            color: var(--text-color);
        }
        
        .form-group input,
        .form-group textarea,
        .form-group select {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 6px;
            font-size: 14px;
            transition: border-color 0.3s;
        }
        
        .form-group input:focus,
        .form-group textarea:focus,
        .form-group select:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 2px rgba(33, 150, 243, 0.1);
        }
        
        .form-group textarea {
            resize: vertical;
            min-height: 120px;
        }
        
        .submit-btn {
            background: linear-gradient(135deg, var(--primary-color), #1976D2);
            color: white;
            padding: 12px 30px;
            border: none;
            border-radius: 6px;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .submit-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(33, 150, 243, 0.3);
        }
        
        .back-link {
            display: inline-flex;
            align-items: center;
            color: var(--primary-color);
            text-decoration: none;
            margin-bottom: 20px;
            font-weight: 500;
        }
        
        .back-link:hover {
            text-decoration: underline;
        }
        
        .back-link i {
            margin-right: 8px;
        }
    </style>
</head>
<body>
    <div class="tracking-page">
        <div class="tracking-container">
            <div class="tracking-content">
                <a href="<?= App\Core\View::url('/track') ?>" class="back-link">
                    <i class="fas fa-arrow-left"></i>
                    Back to Tracking
                </a>
                
                <div class="contact-container">
                    <div class="contact-header">
                        <h1>Contact Support</h1>
                        <p>Need help with your shipment? We're here to assist you!</p>
                    </div>
                    
                    <div class="contact-methods">
                        <div class="contact-method">
                            <i class="fas fa-phone"></i>
                            <h3>Phone Support</h3>
                            <p><strong>+****************</strong></p>
                            <p>Mon-Fri: 8:00 AM - 6:00 PM</p>
                            <p>Sat: 9:00 AM - 4:00 PM</p>
                        </div>
                        
                        <div class="contact-method">
                            <i class="fas fa-envelope"></i>
                            <h3>Email Support</h3>
                            <p><strong>support@<?= $_SERVER['HTTP_HOST'] ?? 'courier.com' ?></strong></p>
                            <p>Response within 24 hours</p>
                        </div>
                        
                        <div class="contact-method">
                            <i class="fas fa-clock"></i>
                            <h3>Live Chat</h3>
                            <p><strong>Available 24/7</strong></p>
                            <p>Instant support for urgent issues</p>
                        </div>
                    </div>
                    
                    <div class="contact-form">
                        <h3>Send us a Message</h3>
                        <form id="contactForm" action="<?= App\Core\View::url('/contact/submit') ?>" method="POST">
                            <?= App\Core\View::csrfField() ?>
                            
                            <div class="form-group">
                                <label for="tracking_number">Tracking Number (Optional)</label>
                                <input type="text" id="tracking_number" name="tracking_number" placeholder="Enter your tracking number">
                            </div>
                            
                            <div class="form-group">
                                <label for="inquiry_type">Inquiry Type</label>
                                <select id="inquiry_type" name="inquiry_type" required>
                                    <option value="">Select inquiry type</option>
                                    <option value="tracking">Tracking Information</option>
                                    <option value="delivery">Delivery Issues</option>
                                    <option value="documents">Document Requirements</option>
                                    <option value="billing">Billing Questions</option>
                                    <option value="general">General Inquiry</option>
                                    <option value="complaint">Complaint</option>
                                </select>
                            </div>
                            
                            <div class="form-group">
                                <label for="name">Full Name</label>
                                <input type="text" id="name" name="name" required placeholder="Enter your full name">
                            </div>
                            
                            <div class="form-group">
                                <label for="email">Email Address</label>
                                <input type="email" id="email" name="email" required placeholder="Enter your email address">
                            </div>
                            
                            <div class="form-group">
                                <label for="phone">Phone Number (Optional)</label>
                                <input type="tel" id="phone" name="phone" placeholder="Enter your phone number">
                            </div>
                            
                            <div class="form-group">
                                <label for="message">Message</label>
                                <textarea id="message" name="message" required placeholder="Please describe your inquiry in detail..."></textarea>
                            </div>
                            
                            <button type="submit" class="submit-btn">
                                <i class="fas fa-paper-plane"></i>
                                Send Message
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        document.getElementById('contactForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            // Show loading state
            const submitBtn = this.querySelector('.submit-btn');
            const originalText = submitBtn.innerHTML;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Sending...';
            submitBtn.disabled = true;
            
            // Simulate form submission (replace with actual AJAX call)
            setTimeout(() => {
                alert('Thank you for your message! We will get back to you within 24 hours.');
                this.reset();
                submitBtn.innerHTML = originalText;
                submitBtn.disabled = false;
            }, 2000);
        });
    </script>
</body>
</html>
