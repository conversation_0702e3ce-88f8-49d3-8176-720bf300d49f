<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Track Your Shipment - <?= App\Core\Config::get('app.name', 'Courier Service') ?></title>
    <link rel="stylesheet" href="<?= App\Core\View::asset('css/public-style.css') ?>">
    <link rel="stylesheet" href="<?= App\Core\View::asset('css/track-result.css') ?>">
    <link rel="stylesheet" href="<?= App\Core\View::asset('css/tracking-details.css') ?>">
    <link rel="stylesheet" href="<?= App\Core\View::asset('css/custom-theme.css') ?>">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" integrity="sha256-p4NxAoJBhIIN+hmNHrzRCf9tD/miZyoHS5obTRR9BMY=" crossorigin=""/>
</head>
<body class="tracking-page-body">
    <div class="tracking-container tracking-result-container">
        <div class="tracking-layout">
            <!-- Left Sidebar -->
            <div class="tracking-sidebar">
                <div class="sidebar-logo">
                    <a href="<?= App\Core\View::url('/') ?>">
                        <i class="fas fa-shipping-fast"></i>
                    </a>
                </div>

                <!-- Functional Sidebar Sections -->
                <div class="sidebar-sections">
                    <!-- Document History Section -->
                    <div class="sidebar-section" id="document-history-section">
                        <button class="sidebar-toggle" data-section="document-history" title="Document History">
                            <i class="fas fa-file-alt"></i>
                            <span class="notification-badge" id="document-count-badge" style="display: none;">0</span>
                        </button>
                        <div class="sidebar-panel" id="document-history-panel" style="transform: translateX(-430px);">
                            <div class="panel-header">
                                <h3>Document History</h3>
                                <button class="panel-close">&times;</button>
                            </div>
                            <div class="panel-content" id="document-history-content">
                                <div class="no-content">
                                    <i class="fas fa-file-alt"></i>
                                    <p>No document history available</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Notifications Section -->
                    <div class="sidebar-section" id="notifications-section">
                        <button class="sidebar-toggle" data-section="notifications" title="Notifications">
                            <i class="fas fa-bell"></i>
                            <span class="notification-badge" id="notification-count-badge" style="display: none;">0</span>
                        </button>
                        <div class="sidebar-panel" id="notifications-panel" style="transform: translateX(-430px);">
                            <div class="panel-header">
                                <h3>Notifications</h3>
                                <button class="panel-close">&times;</button>
                            </div>
                            <div class="panel-content" id="notifications-content">
                                <div class="no-content">
                                    <i class="fas fa-bell"></i>
                                    <p>No notifications available</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Track Details Section -->
                    <div class="sidebar-section" id="track-details-section">
                        <button class="sidebar-toggle" data-section="track-details" title="Track Details">
                            <i class="fas fa-info-circle"></i>
                        </button>
                        <div class="sidebar-panel" id="track-details-panel" style="transform: translateX(-430px);">
                            <div class="panel-header">
                                <h3>Track Details</h3>
                                <button class="panel-close">&times;</button>
                            </div>
                            <div class="panel-content" id="track-details-content">
                                <div class="no-content">
                                    <i class="fas fa-info-circle"></i>
                                    <p>No tracking information available</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Navigation Links -->
                <div class="sidebar-nav">
                    <a href="<?= App\Core\View::url('/') ?>" class="nav-item" title="Home">
                        <i class="fas fa-home"></i>
                    </a>
                    <a href="<?= App\Core\View::url('/track') ?>" class="nav-item active" title="Track">
                        <i class="fas fa-search"></i>
                    </a>
                    <a href="<?= App\Core\View::url('/') ?>" class="nav-item" title="Services">
                        <i class="fas fa-box"></i>
                    </a>
                    <a href="<?= App\Core\View::url('/contact') ?>" class="nav-item" title="Contact">
                        <i class="fas fa-envelope"></i>
                    </a>
                </div>
            </div>

            <!-- Main Content -->
            <div class="tracking-content">
                <div class="tracking-header">
                    <h1 class="tracking-title">Tracking List</h1>
                    <p class="tracking-subtitle">Track your shipment</p>
                </div>

                <div class="tracking-main">
                    <div class="tracking-search-container">
                        <form id="tracking-form" class="tracking-form" action="<?= App\Core\View::url('/api/track') ?>" method="POST">
                            <?= App\Core\View::csrfField() ?>
                            <div class="search-input-group">
                                <i class="fas fa-search search-icon"></i>
                                <input type="text" id="tracking_number" name="tracking_number" class="search-input" placeholder="Enter tracking number..." required>
                                <button type="submit" class="search-button">
                                    <i class="fas fa-search"></i> Track
                                </button>
                            </div>
                            <p class="tracking-note">
                                <i class="fas fa-info-circle note-icon"></i>
                                <span>Please Note: Shipment information will appear approximately 8 hours after being handed in at a <?= App\Models\Setting::get('site_name', 'Courier Service') ?> location</span>
                            </p>
                        </form>
                    </div>

                    <div id="tracking-results" class="tracking-results-area">
                        <!-- Results will be loaded here via JS -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js" integrity="sha256-20nQCchB9co0qIjJZRGuk2/Z9VM+kNiyxNV1lvTlZBo=" crossorigin=""></script>
    <script src="https://cdn.jsdelivr.net/npm/jsbarcode@3.11.5/dist/JsBarcode.all.min.js"></script>
    <script src="<?= App\Core\View::asset('js/public-script.js') ?>"></script>
    <script src="<?= App\Core\View::asset('js/tracking-details.js') ?>"></script>
</body>
</html>
