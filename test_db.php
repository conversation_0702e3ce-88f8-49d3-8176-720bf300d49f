<?php
require_once 'config/app.php';

use App\Core\Database;

try {
    // Test database connection
    Database::prepare('SHOW TABLES');
    Database::execute();
    echo "Database connection successful\n";
    
    $tables = Database::fetchAll();
    echo "Tables found:\n";
    foreach($tables as $table) {
        echo "- " . array_values($table)[0] . "\n";
    }
    
    // Check if shipments table has currency column
    Database::prepare('DESCRIBE shipments');
    Database::execute();
    $columns = Database::fetchAll();
    
    $hasCurrency = false;
    echo "\nShipments table columns:\n";
    foreach($columns as $column) {
        echo "- " . $column['Field'] . " (" . $column['Type'] . ")\n";
        if($column['Field'] === 'currency') {
            $hasCurrency = true;
        }
    }
    
    if(!$hasCurrency) {
        echo "\nAdding currency support to shipments table...\n";
        Database::prepare('ALTER TABLE shipments ADD COLUMN currency VARCHAR(3) DEFAULT "EUR" AFTER total_freight');
        Database::execute();
        
        Database::prepare('ALTER TABLE shipments ADD COLUMN exchange_rate DECIMAL(10,4) DEFAULT 1.0000 AFTER currency');
        Database::execute();
        
        echo "Currency columns added successfully!\n";
    } else {
        echo "\nCurrency support already exists in shipments table.\n";
    }
    
} catch (Exception $e) {
    echo "Database error: " . $e->getMessage() . "\n";
}
?>
