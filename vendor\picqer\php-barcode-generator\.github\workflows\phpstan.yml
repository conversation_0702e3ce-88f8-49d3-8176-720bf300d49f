name: Static analysis (phpstan)

on:
  push:
    branches:
      - main
  pull_request:

jobs:
  build:
    runs-on: ubuntu-latest

    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      with:
        fetch-depth: 0

    - name: Setup PHP
      uses: shivammathur/setup-php@v2
      with:
        php-version: 8.3
        extensions: mbstring, gd, bcmath, imagick

    - name: Install dependencies
      run: composer install --prefer-dist --no-progress --no-interaction

    - name: Run analysis
      run: vendor/bin/phpstan --error-format=github --no-progress
