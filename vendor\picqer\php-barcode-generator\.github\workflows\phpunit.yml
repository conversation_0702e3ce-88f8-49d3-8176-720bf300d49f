name: Unit tests (phpunit)

on:
  push:
    branches:
      - main
  pull_request:

jobs:
  build:
    runs-on: ubuntu-latest

    strategy:
      matrix:
        php-versions: ['8.1', '8.2', '8.3']

    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      with:
        fetch-depth: 0

    - name: Setup PHP
      uses: shivammathur/setup-php@v2
      with:
        php-version: ${{ matrix.php-versions }}
        extensions: mbstring, gd, bcmath, imagick

    - name: Validate composer.json
      run: composer validate

    - name: Install dependencies
      run: composer install --prefer-dist --no-progress --no-interaction

    - name: Run test suite
      run: composer run-script test
