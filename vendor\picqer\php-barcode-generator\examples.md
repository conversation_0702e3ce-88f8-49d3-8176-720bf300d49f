# Examples of supported barcodes

These are examples of supported barcodes with this library.



### C39

![Barcode 1234567890ABC as C39](tests/verified-files/C39-1234567890ABC.svg)

### C39+

![Barcode 1234567890ABC as C39+](tests/verified-files/C39+-1234567890ABC.svg)

### C39E

![Barcode 1234567890abcABC as C39E](tests/verified-files/C39E-1234567890abcABC.svg)

### C39E+

![Barcode 1234567890abcABC as C39E+](tests/verified-files/C39E+-1234567890abcABC.svg)

### C93

![Barcode 1234567890abcABC as C93](tests/verified-files/C93-1234567890abcABC.svg)

### S25

![Barcode 1234567890 as S25](tests/verified-files/S25-1234567890.svg)

### S25+

![Barcode 1234567890 as S25+](tests/verified-files/S25+-1234567890.svg)

### I25

![Barcode 1234567890 as I25](tests/verified-files/I25-1234567890.svg)

### I25+

![Barcode 1234567890 as I25+](tests/verified-files/I25+-1234567890.svg)

### EAN13

![Barcode 081231723897 as EAN13](tests/verified-files/EAN13-081231723897.svg)

![Barcode 0049000004632 as EAN13](tests/verified-files/EAN13-0049000004632.svg)

![Barcode 004900000463 as EAN13](tests/verified-files/EAN13-004900000463.svg)

### ITF14

![Barcode 00012345600012 as ITF14](tests/verified-files/ITF14-00012345600012.svg)

![Barcode 05400141288766 as ITF14](tests/verified-files/ITF14-05400141288766.svg)

### C128

![Barcode 081231723897 as C128](tests/verified-files/C128-081231723897.svg)

![Barcode 1234567890abcABC-283*33 as C128](tests/verified-files/C128-1234567890abcABC-283-33.svg)

### C128A

![Barcode 1234567890 as C128A](tests/verified-files/C128A-1234567890.svg)

### C128B

![Barcode 081231723897 as C128B](tests/verified-files/C128B-081231723897.svg)

![Barcode 1234567890abcABC-283*33 as C128B](tests/verified-files/C128B-1234567890abcABC-283-33.svg)

### EAN2

![Barcode 22 as EAN2](tests/verified-files/EAN2-22.svg)

### EAN5

![Barcode 1234567890abcABC-283*33 as EAN5](tests/verified-files/EAN5-1234567890abcABC-283-33.svg)

### EAN8

![Barcode 1234568 as EAN8](tests/verified-files/EAN8-1234568.svg)

### UPCA

![Barcode 123456789 as UPCA](tests/verified-files/UPCA-123456789.svg)

### UPCE

![Barcode 123456789 as UPCE](tests/verified-files/UPCE-123456789.svg)

### MSI

![Barcode 123456789 as MSI](tests/verified-files/MSI-123456789.svg)

### MSI+

![Barcode 123456789 as MSI+](tests/verified-files/MSI+-123456789.svg)

### POSTNET

![Barcode 123456789 as POSTNET](tests/verified-files/POSTNET-123456789.svg)

### PLANET

![Barcode 123456789 as PLANET](tests/verified-files/PLANET-123456789.svg)

### RMS4CC

![Barcode 123456789 as RMS4CC](tests/verified-files/RMS4CC-123456789.svg)

### KIX

![Barcode 123456789 as KIX](tests/verified-files/KIX-123456789.svg)

### IMB

![Barcode 123456789 as IMB](tests/verified-files/IMB-123456789.svg)

### CODABAR

![Barcode 123456789 as CODABAR](tests/verified-files/CODABAR-123456789.svg)

### CODE11

![Barcode 123456789 as CODE11](tests/verified-files/CODE11-123456789.svg)

### PHARMA

![Barcode 123456789 as PHARMA](tests/verified-files/PHARMA-123456789.svg)

### PHARMA2T

![Barcode 123456789 as PHARMA2T](tests/verified-files/PHARMA2T-123456789.svg)



*This file is generated by generate-examples.php*