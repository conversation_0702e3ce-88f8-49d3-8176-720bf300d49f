const mix = require('laravel-mix');

/*
 |--------------------------------------------------------------------------
 | Mix Asset Management
 |--------------------------------------------------------------------------
 |
 | Mix provides a clean, fluent API for defining some Webpack build steps
 | for your application. By default, we are compiling the Sass
 | file for the application as well as bundling up all the JS files.
 |
 */

// Example: Compile main JS and CSS
mix.js('src/resources/js/app.js', 'public/js') // Adjust source path if needed
   .postCss('src/resources/css/app.css', 'public/css', [ // Adjust source path if needed
       require('tailwindcss'), // If using Tailwind
   ]);

// Example: If using Sass
// mix.sass('src/resources/sass/app.scss', 'public/css');

// Example: Copy assets
// mix.copyDirectory('src/resources/assets', 'public/assets');

// Enable source maps for development
if (!mix.inProduction()) {
    mix.sourceMaps();
}

// Versioning/Cache Busting for production
if (mix.inProduction()) {
    mix.version();
}

// Add any other Mix configurations needed (e.g., BrowserSync, options)
// mix.browserSync('your-local-domain.test'); // Example BrowserSync

// mix.options({
//     processCssUrls: false, // Useful if URLs are already correct
// });
